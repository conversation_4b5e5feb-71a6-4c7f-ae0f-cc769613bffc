# 数据加工工具平台

## 安装依赖
```
npm install
```

### 本地服务
```
npm run serve
```

### 生产编译
```
npm run build
```

### 单元测试
```
npm run test:unit
```

### 代码格式修复
```
npm run lint
```

### 权限项

[权限项](https://doc.weixin.qq.com/sheet/e3_AW0AJQZ0ANkCNyB5aimVCSsG3oxfL?scode=AJEAIQdfAAot4XsPqjACUASAYHACk&tab=BB08J2)

### 项目结构

```
├─docs
├─public
├─src
│  ├─assets               // 静态资源
│  ├─components           // 全局公共组件
│  ├─composables          // 全局公共组合式API
│  ├─directives           // 全局vue指令
│  ├─router               // 路由信息
│  ├─server               // 请求相关
│  ├─stores               // store
│  ├─styles               // 全局公共样式及CSS变量定义和重置
│  ├─utils                // 公共工具库
│  └─views                // 页面内容
│      ├─approval         // 审批管理
│      ├─operations       // 系统运维
│      ├─spaces           // 空间管理
│      └─workbench        // 工作台
└─tests                   // 单元测试目录
    └─unit

```

### 接口文档

[数据工具平台服务接口](http://*************:30170/swagger-ui/index.html)

### 流水线
[测试环境-数据加工平台](https://zhiyan.woa.com/qci/8767/pipeline/#/pipeline/detail/11282191/build/current)

### 公共组件说明

#### HeadMenu 顶部公共菜单

![HeadMenu](./docs/head-menu.png)

#### BlockHeader 带色块的标题栏

![BlockHeader](./docs/block-header.png)

#### EllipsisWithTooltip 超出神略并通过tooltip显示完全内容

![EllipsisWithTooltip](./docs/ellipsis-width-tooltip.png)

#### PageHeader 页面内容的头部，包含一个返回图标和标题

![PageHeader](./docs/page-header.png)

#### PageLayout 明细类页面布局，内置PageHeader，并使PageHeader始终在页面顶部。

![PageLayout](./docs/page-layout.png)

#### BasicSearch 搜索并重置

![BasicSearch](./docs/basic-search.png)

#### BasicDialog 弹窗

![BasicDialog](./docs/basic-dialog.png)

#### SelectWithIcon 带图标的选择器

![SelectWithIcon](./docs/select-with-icon.png)

#### BasicSqlEditor SQL编辑器。

![BasicSqlEditor](./docs/basic-sql-editor.png)
#### UserName 带头像的用户名称

![UserName](./docs/user-name.png)

#### DesRectIcon 带背景的矩形图标
![DesRectIcon](./docs/des-react-icon.png)

#### FormTable 表格型表单
![FormTable](./docs/table-form.png)

#### BasicPageFooter 底部栏
![BasicPageFooter](./docs/basic-page-footer.png)

#### CrontabSelect 执行时间选择器
![CrontabSelect](./docs/crontab-select.png);

#### OwnedSpace 所属空间
![OwnedSpace](./docs/owned-space.png);

### FilePathSelect 文件路径选择器
![FilePathSelect](./docs/file-path-select.png)

### BaseAuth 按权限渲染组件
只有有权限才会被渲染，无权限时不会渲染

### NoData 无数据图片
![NoData](./docs/no-data.png)