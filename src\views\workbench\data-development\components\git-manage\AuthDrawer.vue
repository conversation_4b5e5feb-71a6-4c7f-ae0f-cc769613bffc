/** 授权drawer */
<template>
  <t-drawer
    :visible.sync="visible"
    header="Git项目授权"
    size="696px"
    :closeBtn="true"
    :cancelBtn="null"
    :confirmBtn="null"
    destroyOnClose
    @close="closeDrawer"
  >
    <div class="auth-wrap">
      <AuthForm ref="formRef" :powerList="currentUserAuth" @handleAdd="handleAdd">
        <t-form-item label="Git项目名称">
          <span>{{ currentRow.gitProjectName }}</span>
        </t-form-item>
      </AuthForm>

      <t-divider></t-divider>

      <section>
        <div>已授权的成员</div>
        <t-table row-key="index" :data="list" :columns="columns" max-height="100%" />
      </section>
    </div>
  </t-drawer>
</template>

<script setup>
import { to } from '@/utils/util';
import { MessagePlugin } from 'tdesign-vue';
import { AuthForm } from '@/views/workbench/components';
import { ref, defineExpose, reactive, defineEmits, computed } from 'vue';
import { useDataDevelopmentStore } from '@/stores/data-development';
import { useCommonStore } from '@/stores/common';
import UserName from '@/components/UserName.vue';
import { useSpacesStore } from '@/stores/spaces';
import { storeToRefs } from 'pinia';
import { useRoute } from 'vue-router/composables';
import { showDialogConfirm, showDialogAlert } from '@/utils/dialog';

const route = useRoute();

const emit = defineEmits(['fetchData']);

const commonStore = useCommonStore();
const spacesStore = useSpacesStore();
const { isSpaceOwner } = storeToRefs(spacesStore);

const dataDevelopmentStore = useDataDevelopmentStore();
const { fetchGitManagerGrantors, fetchGitManagerGrant, fetchGitManagerRevoke } = dataDevelopmentStore;

const currentRow = reactive({});
const setCurrentRow = (obj) => {
  Object.assign(currentRow, obj);
};

const currentUserAuth = computed(() => {
  const staff = list.value.find(item => item.staffId === commonStore.staffId);
  return staff?.permissionList.map(item => ({ EDIT: 'edit', USE: 'use', GRANT: 'grant' }[item])) || [];
});

const authNameMap = {
  EDIT: '编辑',
  USE: '使用',
  GRANT: '授予',
};
const getAuthName = type => type?.map(item => authNameMap[item]).join('、');
const delDisabled = () => {
  if (list.value.length === 1) {
    return true;
  }
  if (isSpaceOwner.value) {
    return false;
  }
  if (currentUserAuth.value.includes('grant')) {
    return false;
  }
  return true;
};
const columns = ref([
  {
    title: '成员',
    colKey: 'staffName',
    width: 188,
    cell: (h, { row }) => (<UserName fullName={row.staffName}></UserName>),
  },
  {
    title: '用户组',
    colKey: 'spaceUserGroupList',
    ellipsis: true,
    cell: (h, { row }) => (<span>{row.spaceUserGroupList?.map(item => item.name).join('、') || '-'}</span>),
  },
  { title: '权限', colKey: 'permissionList', cell: (h, { row }) => getAuthName(row.permissionList) },
  {
    title: '操作',
    colKey: 'operation',
    width: 80,
    cell: (h, { row, rowIndex }) => (<t-link hover="color" theme="danger" onClick={() => handleDel(row, rowIndex)} disabled={ delDisabled(row) }>移除</t-link>),
  },
]);

const list = ref([]);
const initData = async () => {
  const [err, data] = await to(fetchGitManagerGrantors(route.query.spaceId, currentRow.id));
  if (err) {
    return;
  }
  list.value = data;
};

const visible = ref(false);
const openDrawer = async (obj) => {
  setCurrentRow(obj);
  initData();
  visible.value = true;
};

const formRef = ref(null);
const closeDrawer = () => {
  formRef.value.resetForm();
  visible.value = false;
  emit('fetchData');
};

const handleDel = async (row) => {
  if (row.spaceUserGroupList?.length) {
    const permission = new Set();
    row.spaceUserGroupList.forEach((item) => {
      // 注意-Git gitPermissionTypes字段不同于工作流
      item.gitPermissionTypes?.forEach(v => permission.add(v));
      if (item.isSystemDefault === 1) {
        ['USE', 'EDIT', 'GRANT'].forEach(v => permission.add(v));
      }
    });
    // 查出可移除的权限
    const removeablePermission = row.permissionList.filter(item => !permission.has(item));
    const dialogConfig = {
      title: `移除成员「${row.staffName}」`,
      body: null,
      width: '440px',
    };
    if (removeablePermission.length) {
      dialogConfig.confirmText = '移除';
      dialogConfig.body = () => (
        <div style="padding-left: 26px;">
          <p>
            无法移除该成员所在用户组权限: { Array.from(permission).map(item => authNameMap[item])
            .join('、') }
          </p>
          <p>可移除的个人权限: { removeablePermission.map(item => authNameMap[item]).join('、') }</p>
        </div>
      );
      const { type } =  await showDialogConfirm(dialogConfig);
      if (type === 'cancel') return;
    } else {
      dialogConfig.body = () => (
        <div style="padding-left: 26px;">
          <p>
            无法移除该成员所在用户组权限: { Array.from(permission).map(item => authNameMap[item])
            .join('、') }
          </p>
        </div>
      );
      // 用户组有全部权限
      showDialogAlert(dialogConfig);
      return;
    }
  }
  const params = { gitManagerId: currentRow.id, staffId: row.staffId };
  const [err, success] = await to(fetchGitManagerRevoke(params));
  if (err) {
    return;
  }
  success && MessagePlugin('success',  '移除授权成功');
  initData();
};

// 提交前检查权限的方法
async function checkPermissionsBeforeSubmit(spaceId, staffList) {
  for (const staff of staffList) {
    const { data } = await spacesStore.checkSpaceIsOwnerByStaff(spaceId, staff.StaffID);
    if (data) {
      MessagePlugin.error(`${staff.StaffName}是空间负责人，默认已有全部权限，不需要添加`);
      return false;
    }
  }
  return true;
}

const handleAdd = async (data, callback) => {
  const { form, selectStaffList } = data;

  const canSubmit = await checkPermissionsBeforeSubmit(route.query.spaceId, selectStaffList.value);
  if (!canSubmit) return;

  const array = selectStaffList.value?.map(item => ({
    staffId: String(item.StaffID),
    staffName: item.StaffName,
    engName: item.EngName,
    permissionList: form.type.map(item => ({ edit: 'EDIT', use: 'USE', grant: 'GRANT' }[item])),
  }));
  list.value.push(...array);

  handleGrantAuth();
  callback();
};

const handleGrantAuth = async () => {
  const params = { userList: list.value };
  const [err, success] = await to(fetchGitManagerGrant(route.query.spaceId, currentRow.id, params));
  if (err) {
    return;
  }
  success && MessagePlugin('success',  '授权成功');
  initData();
};

defineExpose({ openDrawer });
</script>

<style lang="less" scoped>
.auth-wrap {
  height: 100%;
  section {
    height: calc(100% - 339px);
  }
  .t-table {
  margin-top: 16px;
  height: calc(100% - 38px);
}
}
</style>
