<template>
  <t-dropdown trigger="click" :options="options" @click="clickHandler">
    <span class="tdesign-demo-dropdown__text">
      {{ dropdownLabel }}
      <DesIcon name="des-icon-line-jiantou-xia" size="16"></DesIcon>
    </span>
  </t-dropdown>
</template>

<script setup>
import { ref, defineEmits } from 'vue';
import DesIcon from '@/components/DesIcon.vue';

const emit = defineEmits(['change']);

const options = ref([
  {
    content: '默认',
    value: 1,
  },
  {
    content: '数仓分层',
    value: 2,
  },
  {
    content: '主题域',
    value: 3,
  },
]);
const dropdownLabel = ref('默认');

const clickHandler = (value) => {
  dropdownLabel.value = value.content;
  emit('change', value);
};
</script>

<style lang="less" scoped>
.tdesign-demo-dropdown {
  &__text {
    display: inline-flex;
    align-items: center;
    font-size: 12px;
    font-style: normal;
    line-height: 20px;
  }
}
</style>
