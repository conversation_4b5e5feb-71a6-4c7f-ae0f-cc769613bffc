<script setup>
import { defineProps } from 'vue';
import BlockHeader from '@/components/BlockHeader.vue';

defineProps({
  formData: {
    type: Object,
    required: true,
  },
});
</script>

<template>
  <!-- eslint-disable vue/no-mutating-props -->
  <section>
    <block-header title="告警配置" style="margin-bottom: 12px;" />
    <t-form-item label="超时告警" name="timeoutFlag">
      <t-radio-group v-model="formData.timeoutFlag">
        <t-radio :value="true">开</t-radio>
        <t-radio :value="false">关</t-radio>
      </t-radio-group>
    </t-form-item>
    <template v-if="formData.timeoutFlag">
      <t-form-item label="超时时长" name="timeout">
        <t-input-number v-model="formData.timeout" :min="0" theme="column" placeholder="请输入" style="width: 374px;">
          <template #suffix><span>分钟</span></template>
        </t-input-number>
      </t-form-item>
      <t-form-item label="超时是否失败" name="timeoutFailed">
        <t-radio-group v-model="formData.timeoutFailed">
          <t-radio :value="true">是</t-radio>
          <t-radio :value="false">否</t-radio>
        </t-radio-group>
      </t-form-item>
    </template>
  </section>
</template>
