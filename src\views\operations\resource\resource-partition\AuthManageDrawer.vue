<template>
  <t-drawer
    :visible.sync="visible"
    header="权限管理"
    size="640px"
    :closeBtn="true"
    :cancelBtn="null"
    :confirmBtn="null"
    destroyOnClose
    @close="closeDrawer"
  >
    <div class="wrap">
      <section>
        <div>授权给</div>
        <div class="operation-wrap">
          <t-select
            v-model="checked"
            :options="options"
            placeholder="请选择授权空间"
            multiple
            clearable
            valueType="object"
            :minCollapsedNum="3"
            :keys="{ label: 'name', value: 'id' }"
          />
          <t-button variant="outline" theme="primary" @click="addSpace">+ 添加</t-button>
        </div>
      </section>

      <t-divider></t-divider>

      <section>
        <div>已授权的空间名单</div>

        <t-table
          class="basic-list"
          row-key="index"
          :data="list"
          :columns="columns"
          max-height="65vh"
        />
      </section>
    </div>
  </t-drawer>
</template>

<script setup>
import { to } from '@/utils/util';
import { ref, defineExpose } from 'vue';
import { MessagePlugin } from 'tdesign-vue';
import { useSpacesStore } from '@/stores/spaces';
import { useOperationsStore } from '@/stores/operations';

const { getSpacesList } = useSpacesStore();
const { fetResourceGrantList, fetchResourceGrantAuth } = useOperationsStore();

let resourceGroupId;
const setResourceGroupId = id => (resourceGroupId = id);

const columns = ref([
  {
    colKey: 'spaceName',
    title: '空间名称',
  },
  {
    colKey: 'operation',
    title: '操作',
    width: 95,
    cell: (h, { rowIndex }) => (
      <t-popconfirm content="确认移除吗" onConfirm={() => delSpace(rowIndex)}>
        <t-link theme="danger" hover="color">
          移除
        </t-link>
      </t-popconfirm>
    ),
  },
]);

const list = ref([]);
const initOptions = async () => {
  const params = { resourceGroupId };
  const [err, data] = await to(fetResourceGrantList(params));
  if (err) {
    return;
  }
  list.value = data;
  return true;
};

const options = ref([]);
const initSpaces = async () => {
  const [err, data] = await to(getSpacesList());
  if (err) {
    return;
  }
  options.value = data.list;
  return true;
};

const visible = ref(false);
const closeDrawer = () => {
  checked.value = [];
  visible.value = false;
};
const openDrawer = async (resourceGroup) => {
  setResourceGroupId(resourceGroup.id);
  const result = await initSpaces();
  if (!result) return;
  const res = await initOptions();
  if (!res) return;
  visible.value = true;
};

const checked = ref([]);
const addSpace = () => {
  if (checked.value.length === 0) {
    return;
  }

  const data = checked.value.map(item => ({
    spaceName: item.name,
    spaceId: item.id,
  }));
  const { length } = list.value;
  // 列表值去重
  const result = data.filter(item => list.value.every(v => v.spaceId !== item.spaceId));
  list.value.push(...result);
  checked.value = [];

  // 无变化不触发
  if (list.value.length === length) {
    MessagePlugin('error', '该授权已存在');
    return;
  }

  handleGrantAuth('add');
};

const delSpace = (rowIndex) => {
  list.value.splice(rowIndex, 1);

  handleGrantAuth('del');
};

const handleGrantAuth = async (type = 'add') => {
  const params = { resourceGroupId, resourceGrantDatas: list.value };
  const [err, success] = await to(fetchResourceGrantAuth(params));
  if (err) {
    return;
  }
  success && MessagePlugin('success', type === 'add' ? '授权成功' : '移除授权成功');
};

defineExpose({ openDrawer });
</script>

<style lang="less" scoped>
.wrap {
  .operation-wrap {
    margin-top: 10px;
    display: flex;
    .t-button {
      margin-left: 10px;
    }
  }
  .basic-list {
    margin-top: 16px;
  }
}
</style>
