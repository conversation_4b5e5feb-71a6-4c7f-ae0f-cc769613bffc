import { useDataDevelopmentStore } from '@/stores/data-development';
import { showDialogConfirm } from '@/utils/dialog';

/**
 * 工作流上锁逻辑
 * @param {Object} row 工作流数据
 * @param {String} spaceId 空间ID
 * @returns {Promise<Object>} 锁状态结果
 */
export async function lockFlow(row, spaceId) {
  const dataDevelopmentStore = useDataDevelopmentStore();
  const { data } = await dataDevelopmentStore.fetchFlowDefineLock(spaceId, row.code);
  return data;
}

/**
 * 编辑工作流通用逻辑
 * @param {Object} row 工作流数据
 * @param {String} spaceId 空间ID
 * @param {Function} viewHandler 查看工作流函数
 * @returns {Promise<void>}
 */
export async function editFlow(row, spaceId, props = {}) {
  const response = await lockFlow(row, spaceId);
  const dataDevelopmentStore = useDataDevelopmentStore();

  if (response.success) {
    dataDevelopmentStore.goEditFlow(row, props);
    return;
  }

  const result = await showDialogConfirm({
    title: `当前工作流「${row.name}」正在编辑中`,
    body: () => (
      <div style="padding-left: 26px;">
        <p>用户 {response.staffName} 正在编辑</p>
        <p>开始时间：{response.startTime}</p>
      </div>
    ),
    cancelText: '查看工作流',
    confirmText: '我知道了',
    width: '440px',
  });

  if (result.type === 'cancel') {
    viewFlow(row);
  }
}

/**
 * 查看工作流通用逻辑
 * @param {Object} row 工作流数据
 */
export function viewFlow(row) {
  const dataDevelopmentStore = useDataDevelopmentStore();
  dataDevelopmentStore.goViewFlow(row);
}
