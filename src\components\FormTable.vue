<script setup>
import { defineProps, computed } from 'vue';
const props = defineProps({
  rowKey: {
    type: String,
  },
  // {title: String|function, required: boolean, cell: function, colKey: string, rules: Array, width: string|numbwr}
  columns: {
    type: Array,
    default: () => ([]),
  },
  data: {
    type: Array,
  },
  // formitem的name属性补全，用于在特定表单中触发校验
  prefixFormItemName: {
    type: String,
  },
  onAdd: {
    type: Function,
  },
  showRemove: {
    type: Boolean,
    default: true,
  },
  showAdd: {
    type: Boolean,
    default: true,
  },
});
const tableColumns = computed(() => {
  const columns = props.columns.map((col) => {
    const { colKey, rules = [], width } = col;
    let { title, cell } = col;
    if (typeof col.title === 'string' && col.required) {
      title = (<span><b style="color:#F81D22;fontWeight:normal">*</b>{ col.title }</span>);
    }
    cell = (h, scope) => (<t-form-item name={`${props.prefixFormItemName}[${scope.rowIndex}].${colKey}`} rules={rules}>
    { col.cell(h, scope) }
  </t-form-item>);
    const colData = {
      colKey,
      title,
      cell,
      width,
    };
    return colData;
  });
  if (props.showRemove) {
    columns.push({
      colKey: 'operation',
      title: '操作',
      width: 60,
      cell: (h, { rowIndex }) => (<t-link hover="color" theme="primary" onClick={() => handleRemoveRow(rowIndex)}>删除</t-link>),
    });
  }
  return columns;
});

function handleAddRow() {
  props.onAdd();
}
function handleRemoveRow(index) {
  // eslint-disable-next-line vue/no-mutating-props
  props.data.splice(index, 1);
}
</script>

<template>
  <div>
    <t-table class="des-form-table" :row-key="rowKey" :columns="tableColumns" :data="data"></t-table>
    <t-link v-if="showAdd" style="margin-top: 13px;" hover="color" theme="primary" @click="handleAddRow"><des-icon name="des-icon-jiaxiao" size="14px"></des-icon> 添加参数</t-link>
  </div>
</template>

<style lang="less" scoped>
.des-form-table {
  border: 1px solid var(--td-component-border);
  border-radius: var(--td-radius-default);
  overflow: hidden;
  :deep(.t-table__content) {
    tbody > tr:last-child > td,
    tfoot > tr:last-child > td {
      border-bottom: 0;
    }
  }
}
</style>
