<script setup>
import { defineProps } from 'vue';

defineProps({
  text: {
    type: String,
    default: '',
  },
});

</script>

<template>
  <div class="des-space-empty">
    <img src="@/assets/empty.png" alt="无空间">
    <p>{{ text }}</p>
    <div class="btn-group">
      <slot name="btn-group"></slot>
    </div>
  </div>
</template>

<style lang="less" scoped>
.des-space-empty {
  text-align: center;
  img {
    display: block;
    width: 200px;
    height: 200px;
    margin: 0 auto;
  }
  p {
    color: #333333;
    font-family: "PingFang SC";
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 28px;
    text-align: center;
  }
  .btn-group {
    margin-top: 24px;
  }
}
</style>
