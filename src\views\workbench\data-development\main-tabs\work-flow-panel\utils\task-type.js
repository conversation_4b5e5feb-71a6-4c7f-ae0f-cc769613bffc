export const SUBPROCESS = 'SUB_PROCESS';
export const DEPENDENT = 'DEPENDENT';
export const CONDITIONS = 'CONDITIONS';
export const SWITCH = 'SWITCH';
export const MESSAGE = 'MESSAGE';
export const BIDSPLUS = 'BIDSPLUS';
export const STARROCKS_MATERIALIZED_VIEW = 'STARROCKS_MATERIALIZED_VIEW';
export const JDBC_SQL = 'JDBC_SQL';
export const SHELL = 'SHELL';
export const PYTHON = 'PYTHON';
export const STARROCKS_SQL = 'STARROCKS_SQL';
export const HTTP = 'HTTP';

// 显示名的映射
export const taskTypeLabelMapping = {
  [SUBPROCESS]: 'Subprocess',
  [DEPENDENT]: 'Dependent',
  [CONDITIONS]: 'Conditions',
  [SWITCH]: 'Switch',
  [MESSAGE]: '消息通知',
  [BIDSPLUS]: '管道任务',
  [STARROCKS_MATERIALIZED_VIEW]: 'StarRocks物化视图',
  [JDBC_SQL]: 'JdbcSQL',
  [SHELL]: 'Shell',
  [PYTHON]: 'Python',
  [STARROCKS_SQL]: 'StarRocksSQL',
  [HTTP]: 'Http',
};
// 颜色标签映射
export const taskTypeColorMapping = {
  [SUBPROCESS]: '#25DC93',
  [DEPENDENT]: '#25DC93',
  [CONDITIONS]: '#25DC93',
  [SWITCH]: '#25DC93',
  [MESSAGE]: '#25DC93',

  [BIDSPLUS]: '#25CDE0',

  [STARROCKS_MATERIALIZED_VIEW]: '#8B70F6',
  [JDBC_SQL]: '#8B70F6',
  [SHELL]: '#8B70F6',
  [PYTHON]: '#8B70F6',
  [STARROCKS_SQL]: '#8B70F6',
  [HTTP]: '#8B70F6',
};
// code映射
export const taskTypeCodeMapping = {
  [SUBPROCESS]: 'sub',
  // [DEPENDENT]: 'dep',
  // [CONDITIONS]: 'con',
  // [SWITCH]: 'switch',
  [MESSAGE]: 'message',

  [BIDSPLUS]: 'bids',

  [STARROCKS_MATERIALIZED_VIEW]: 'sr-mv',
  [JDBC_SQL]: 'jdbc-sql',
  // [SHELL]: 'shell',
  // [PYTHON]: 'python',
  [STARROCKS_SQL]: 'sr-sql',
  [HTTP]: 'http',
};
