<script setup>
import { ref, defineProps, defineExpose } from 'vue';
import BasicDialog from '@/components/BasicDialog.vue';
import BlockHeader from '@/components/BlockHeader.vue';
import AlarmInfo from './AlarmInfo.vue';

const visible = ref(false);
const formRef = ref(null);
defineProps({
  data: {
    type: Object,
    default: () => ({
      name: '',
      code: '',
      warehouseLayer: '',
      businessType: '',
      description: '',
      timeoutFlag: false,
      alertChannels: [],
      timeout: '',
      alertConfigId: '',
      executionType: '',
      globalParams: [],
    }),
  },
});

const columns = ref([
  {
    title: '全局变量',
    render: (h, { rowIndex }) => (<span>全局变量{ rowIndex !== -1 ? (rowIndex + 1) : ''}</span>),
  },
  { colKey: 'prop', title: '键' },
  { colKey: 'value', title: '值' },
]);

function handleClose() {
  close();
}

function open() {
  visible.value = true;
}
function close() {
  visible.value = false;
}

function alertChannelsName(list = []) {
  const nameMapping = {
    TIMEOUT: '超时告警',
    FAILED: '失败告警',
  };
  return list.map(type => nameMapping[type]).join('、') || '-';
}
function executionTypeName(type) {
  const nameMapping = {
    PARALLEL: '并行',
    SERIAL_WAIT: '串行等待',
    SERIAL_DISCARD: '串行抛弃',
    SERIAL_PRIORITY: '串行优先',
  };
  return nameMapping[type] || type;
}

defineExpose({ open, close });
</script>

<template>
  <BasicDialog
    width="560"
    :visible.sync="visible"
    header="工作流基本信息"
    :footer="null"
    :showFooter="null"
    @handleClose="handleClose"
  >
    <t-form class="des-work-flow__work-flow-info" :data="data" style="padding-right:12px;" ref="formRef" labelWidth="118px">
      <section>
        <block-header title="基本信息" style="margin-bottom: 12px;" />
        <t-form-item label="工作流名称" name="name">
          <div>{{ data.name }}</div>
        </t-form-item>
        <t-form-item label="工作流标识" name="code">
          <div>{{ data.code }}</div>
        </t-form-item>
        <t-form-item label="数仓层" name="warehouseLayer">
          <div>{{ data.warehouseLayer }}</div>
        </t-form-item>
        <t-form-item label="业务" name="businessType">
          <div>{{ data.businessType }}</div>
        </t-form-item>
        <t-form-item label="描述" name="description">
          <div>{{ data.description }}</div>
        </t-form-item>
        <t-form-item label="执行策略" name="executionType">
          <div>{{ executionTypeName(data.executionType) }}</div>
        </t-form-item>
        <t-form-item label="全局变量" name="globalParams" v-if="data.globalParams.length">
          <t-table row-key="prop" :data="data.globalParams" :columns="columns"></t-table>
        </t-form-item>
      </section>
      <section>
        <block-header title="告警配置" style="margin-bottom: 12px;" />
        <t-form-item label="告警渠道" name="alertChannels">
          <div>{{ alertChannelsName(data.alertChannels) }}</div>
        </t-form-item>
        <t-form-item label="超时时长" name="timeout" v-if="data.alertChannels.includes('TIMEOUT')">
          <div>{{ data.timeout }} 分钟</div>
        </t-form-item>
        <div class="form-item">
          <span class="label">告警模板</span>
          <div style="flex: 1">
            <template v-for="id in data.alertConfigId">
              <AlarmInfo :alarmId="id" :key="id"></AlarmInfo>
            </template>
          </div>
        </div>
      </section>
    </t-form>
  </BasicDialog>
</template>

<style lang="less" scoped>
.form-item {
  display: flex;
  color: #333333;
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  .label {
    width: 118px;
    margin-right: 12px;
    color: #666666;
    text-align: right;
  }
  :deep(.alarm-template-info) {
    margin-left: 0 !important;
  }
}
</style>
