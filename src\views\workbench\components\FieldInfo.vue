<template>
  <div class="field-info__wrap">
    <p class="title">分区信息</p>
    <div><span class="label">分区类型</span><span class="content">retail_e_commer1</span></div>
    <div><span class="label">分区字段</span><span class="content">retail_e_commer1</span></div>
    <div><span class="label">分区粒度</span><span class="content">retail_e_commer1</span></div>
    <div><span class="label">保留分区数</span><span class="content">retail_e_commer1</span></div>
    <t-link theme="primary" hover="color" size="small">
      查看字段信息
    </t-link>
  </div>
</template>

<script setup>
import { defineProps } from 'vue';

defineProps({
  data: Object,
});
</script>

<style lang="less" scoped>
.field-info__wrap {
  height: 100%;
  overflow-y: auto;

  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  .title {
    margin-bottom: 8px;
    font-weight: 600;
    color: #333333;
  }
  span {
    margin-bottom: 8px;
  }
  .label {
    display: inline-block;
    margin-right: 8px;
    color: #999999;
  }
  .content {
    display: inline-block;
    color: #333333;
  }
}
</style>
