<template>
  <t-space :size="12" class="basic-search-space__wrap" break-line>
    <slot name="prefix"></slot>
    <t-input class="basic-search-input" :value="value" v-bind="{ ...$attrs }" :placeholder="placeholder" @change="handleChange">
      <template #suffixIcon>
        <search-icon :style="{ cursor: 'pointer' }" />
      </template>
    </t-input>
    <slot name="suffix"></slot>
    <t-button variant="outline" theme="default" @click="emit('reset')">重置</t-button>
    <slot name="button-behind"></slot>
  </t-space>
</template>

<script setup>
import { defineEmits, defineProps, useAttrs } from 'vue';
import { SearchIcon } from 'tdesign-icons-vue';

const $attrs = useAttrs();
const emit = defineEmits(['input', 'reset', 'change']);

defineProps({
  value: {
    type: String,
    default: '',
  },
  placeholder: {
    type: String,
    default: '标识/创建人…',
  },
});

const handleChange = (val) => {
  emit('input', val);
  emit('change', val);
};
</script>

<style lang="less" scoped>
.basic-search-space__wrap {
  :deep(.t-space-item) {
    height: 32px; // 因用timepicker高度不一致
  }
  .basic-search-input {
    width: 290px;
  }
}
</style>
