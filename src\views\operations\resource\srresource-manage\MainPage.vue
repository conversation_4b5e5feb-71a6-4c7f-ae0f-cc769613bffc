<template>
  <div style="height: 100%">
    <PageLayout>
      <template #search>
        <BasicSearch v-model="searchKey" @reset="handleReset" @change="searchChange">
          <template #prefix>
            <t-select
              v-model="datasourceId"
              :options="datasourceOptions"
              placeholder="数据源"
              filterable
              @change="searchChange"
            ></t-select>
          </template>
        </BasicSearch>
      </template>

      <template #button>
        <t-button theme="primary" @click="handleAdd"><add-icon slot="icon" />新增资源组</t-button>
      </template>

      <template #table>
        <t-table rowKey="code" :data="tableData" :columns="columns" :pagination="pagination" max-height="68vh">
          <template #empty v-if="!datasourceId">
            <no-data text="请先选择数据源"></no-data>
          </template>
        </t-table>
      </template>
    </PageLayout>
    <SRResourceDialog ref="dialogRef" :datasourceOptions="datasourceOptions" @fetchData="fetchData"></SRResourceDialog>
  </div>
</template>

<script setup lang="jsx">
import { to } from '@/utils/util';
import { ref, onMounted } from 'vue';
import { MessagePlugin } from 'tdesign-vue';
import { AddIcon } from 'tdesign-icons-vue';
import { useSearch, usePagination } from '@/hooks';
import { showDialogConfirm } from '@/utils/dialog';
import { useOperationsStore } from '@/stores/operations';
import { PageLayout } from '@/views/operations/components';
import SRResourceDialog from './SRResourceDialog.vue';
import BasicSearch from '@/components/BasicSearch.vue';
import UserName from '@/components/UserName.vue';
import NoData from '@/components/NoData.vue';

const operationsStore = useOperationsStore();

const datasourceId = ref('');
const datasourceOptions = ref([]);

// 表格列
const columns = ref([
  { colKey: 'serial-number', width: 61, title: '序号', fixed: 'left' },
  {
    colKey: 'code',
    title: '资源组标识',
    width: 118,
    ellipsis: true,
  },
  {
    colKey: 'name',
    title: '资源组名称',
    width: 118,
    ellipsis: true,
  },
  {
    colKey: 'dataSourceName',
    title: '所属SR数据源',
    width: 169,
    ellipsis: true,
  },
  {
    colKey: 'backend',
    title: 'BE的IP/FQDN',
    width: 123,
    ellipsis: true,
  },
  {
    colKey: 'beInUseCpuCores',
    title: '在该be上使用的cpu核数',
    width: 184,
    ellipsis: true,
  },
  {
    colKey: 'beInUseMemBytes',
    title: '在该be上使用的cpu内存',
    width: 184,
    ellipsis: true,
  },
  {
    colKey: 'beRunningQueries',
    title: '在该be上正在运行的SQL数',
    width: 184,
    ellipsis: true,
  },
  {
    colKey: 'createUser',
    title: '创建人',
    width: 184,
    cell: (h, { row }) => <UserName fullName={row.createUser}></UserName>,
  },
  {
    title: '操作',
    colKey: 'operate',
    width: 150,
    fixed: 'right',
    cell: (h, { row }) => (
      <t-space>
        <t-link theme="primary" hover="color" onClick={ () => handleEdit(row)}>编辑</t-link>
        <t-link theme="primary" hover="color" onClick={ () => handleDel(row) }>删除</t-link>
      </t-space>
    ),
  },
]);

const tableData = ref([]);

const fetchData = async () => {
  if (!datasourceId.value) return;
  const { current: pageNum, pageSize } = pagination;
  const params = {
    pageNum,
    pageSize,
    queryData: { queryKey: searchKey.value },
  };
  const [err, data] = await to(operationsStore.fetchSourceGroupList(datasourceId.value, params));
  if (err) {
    return;
  }
  const { total, list } = data;
  setPaginationTotal(total);
  tableData.value = list;
};

const { searchKey, setSearchKey, onSearch } = useSearch(fetchData);
const { pagination, setPaginationCurrent, setPaginationTotal } = usePagination(fetchData);

const searchChange = () => {
  setPaginationCurrent(1);
  onSearch();
};

// 重置按钮
const handleReset = () => {
  setSearchKey('');
  setPaginationCurrent(1);
  fetchData();
};

const handleEdit = (row) => {
  dialogRef.value.openEditDialog(row);
};

const handleDel = async (row) => {
  const { type } = await showDialogConfirm({
    title: `确定删除资源组「${row.name}」吗？`,
    body: '删除后不可恢复',
    width: '440px',
  });
  if (type === 'cancel') return;
  const [err, data] = await to(operationsStore.fetchSourceGroupDel(datasourceId.value, row.name));
  if (err) return;
  if (data) {
    fetchData();
    MessagePlugin('success', '操作成功');
  }
};

const dialogRef = ref(null);
const handleAdd = () => {
  dialogRef.value.openAddDialog();
};

const getDataSourceOptions = async () => {
  const data = { pageNum: 1, pageSize: 100, queryData: { queryKey: '' } };
  const [err, result] = await to(operationsStore.fetchDatasourcesList(data));
  if (err) {
    return;
  }
  datasourceOptions.value = result.list.map(item => ({
    ...item,
    label: item.name,
    value: item.id,
  }));
};

onMounted(() => {
  getDataSourceOptions();
});
</script>

<style lang="less" scoped></style>
