<template>
  <div class="view-task">
    <section class="filter-wrap">
      <BasicSearch v-model="searchKey" placeholder="任务名称/CATALOG/数据库/创建人..." @reset="handleReset" @change="searchChange">
        <template #prefix>
          <t-select
            class="des-common-select-placeholder"
            v-model="value"
            :options="options"
            clearable
            placeholder="全部数据源"
            @change="searchChange"
          ></t-select>

          <t-select v-model="value1" :options="options1" clearable style="width: 200px" @change="searchChange">
            <template #valueDisplay="{ value }">
              <span v-if="value">{{ getOptionLabel(value) }}</span>
              <span v-else>全部类型</span>
            </template>
          </t-select>
        </template>
      </BasicSearch>
    </section>

    <t-table rowKey="id" :data="tableData" :columns="columns" :pagination="pagination" max-height="100%">
      <template #empty>
        视图任务页面未开发
      </template>
    </t-table>

    <ViewTaskDialog ref="dialogRef"></ViewTaskDialog>
  </div>
</template>

<script setup lang="jsx" name="view-task">
import { ref, onMounted } from 'vue';
import { useSearch, usePagination } from '@/hooks';
import BasicSearch from '@/components/BasicSearch.vue';
import ViewTaskDialog from './ViewTaskDialog.vue';

const dialogRef = ref(null);

const value = ref('');
const options = ref([]);

const value1 = ref('');
const options1 = ref([]);

const columns = ref([
  { colKey: 'serial-number', width: 61, title: '序号', fixed: 'left' },
  {
    colKey: 'task',
    title: '任务名称',
    width: 110,
    fixed: 'left',
    ellipsis: true,
  },
  {
    colKey: 'createTime',
    title: '创建时间',
    width: 198,
    ellipsis: true,
  },
  {
    colKey: 'schedule',
    title: '调度',
    width: 165,
    ellipsis: true,
  },
  {
    colKey: 'catalog',
    title: 'CATALOG',
    width: 131,
    ellipsis: true,
  },
  {
    colKey: 'databse',
    title: '数据库',
    width: 111,
    ellipsis: true,
  },
  {
    colKey: 'defination',
    title: 'SQL定义',
    width: 111,
    ellipsis: true,
  },
  {
    colKey: 'expireTime',
    title: '过期时间',
    width: 198,
    ellipsis: true,
  },
  {
    title: '操作',
    colKey: 'operate',
    width: 132,
    fixed: 'right',
    cell: (h, { row }) => (
      <t-space>
        <t-link theme="primary" hover="color" onClick={() => handleView(row) }>查看</t-link>
        <t-link theme="primary" hover="color" onClick={() => handleExecute(row)}>手动执行</t-link>
      </t-space>
    ),
  },
]);

const tableData = ref([]);

const fetchData = async () => {
  const params = { pagination, query: searchKey.value };
  console.log(params);
};

const { searchKey, setSearchKey, onSearch } = useSearch(fetchData);
const { pagination, setPaginationCurrent } = usePagination(fetchData);

const searchChange = () => {
  setPaginationCurrent(1);
  onSearch();
};

const handleReset = () => {
  setSearchKey('');
  fetchData();
};

const handleView = () => {
  dialogRef.value.openAddDialog();
};

const handleExecute = () => {
};

const getOptionLabel = value => options1.value.find(item => item.value === value)?.label;

onMounted(() => {});
</script>

<style lang="less" scoped>
.view-task {
  height: 100%;
  padding: 20px 20px 0 20px;
  background: #fff;
  .filter-wrap {
    margin-bottom: 16px;
  }
  :deep(.t-table) {
    height: calc(100% - 48px);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
}
:deep(.basic-search-space__wrap) {
  .basic-search-input {
    width: 366px;
  }
}
</style>
