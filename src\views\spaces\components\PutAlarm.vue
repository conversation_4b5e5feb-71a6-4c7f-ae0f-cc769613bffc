<script setup>
import { ref, reactive, defineExpose, defineProps, defineEmits, nextTick, computed } from 'vue';
import BasicDialog from '@/components/BasicDialog.vue';
import { showDialogAlert } from '@/utils/dialog.js';
import { useSpacesStore } from '@/stores/spaces';

const props = defineProps({
  spaceId: {
    type: String,
    required: true,
  },
});

const visible = ref(false);
const formRef = ref(null);
const wechatReceiverRef = ref(null);
const phoneReceiverRef = ref(null);
const emailReceiverRef = ref(null);

const emit = defineEmits('update');

const spaceStore = useSpacesStore();

const formData = reactive({
  id: '',
  name: '',
  types: [],
  perType: 1,
  wechatRobotChatid: '',
  wechatReceiverIds: [],
  wechatReceivers: [],
  phoneReceiverIds: [],
  phoneReceivers: [],
  emailReceiverIds: [],
  emailReceivers: [],
});
const rawData = ref({});

const options = [
  { value: 1, label: '企微机器人' },
  { value: 2, label: '电话' },
  { value: 3, label: '邮件' },
];

const rules = {
  name: [{ required: true, message: '请输入名称' }, { validator: checkName, trigger: 'change' }],
  types: [{ required: true, message: '请选择告警类型' }],
  perType: [{ required: true, message: '请选择人群类型' }],
  wechatRobotChatid: [{ required: true, message: '请输入群聊id' }],
  wechatReceiverIds: [{ required: true, message: '请选择接收人' }],
  phoneReceiverIds: [{ required: true, message: '请选择接收人' }],
  emailReceiverIds: [{ required: true, message: '请选择接收人' }],
};

const disabled = computed(() => {
  const { name = '', types = [], perType, wechatReceiverIds = [], wechatRobotChatid = '', phoneReceiverIds = [], emailReceiverIds = [] } = formData;
  if (!name) return true;
  if (types.length === 0) return true;
  if (types.includes(1)) {
    if (!perType) return true;
    if (perType === 1 && wechatReceiverIds.length === 0) return true;
    if (perType === 2 && !wechatRobotChatid) return true;
  }
  if (types.includes(2) && phoneReceiverIds.length === 0) return true;
  if (types.includes(3) && emailReceiverIds.length === 0) return true;
  return false;
});

async function checkName(val) {
  if (!val) {
    return {
      result: false,
      message: '名称不能为空',
      type: 'error',
    };
  }
  // 数据没变不校验重复
  if (rawData.value.name === val) {
    return true;
  }
  const { data } = await spaceStore.checkSpaceAlarmName({
    spaceId: props.spaceId,
    alertName: val,
  });
  if (data) {
    return {
      result: false,
      message: '名称已存在',
      type: 'error',
    };
  }
  return true;
}

function handleChangeStaff(filed, staffs) {
  formData[filed] = staffs.map(item => ({
    staffId: item.StaffID,
    engName: item.EngName,
    staffName: item.StaffName,
  }));
}

function getSubData() {
  return {
    id: formData.id,
    spaceId: props.spaceId,
    name: formData.name,
    types: formData.types,
    perType: formData.perType,
    wechatRobotChatid: formData.wechatRobotChatid,
    wechatReceivers: formData.wechatReceivers,
    phoneReceivers: formData.phoneReceivers,
    emailReceivers: formData.emailReceivers,
  };
}

function staffDataToStaffData(list = []) {
  return list.map(item => ({
    StaffID: item.staffId,
    EngName: item.engName,
    StaffName: item.staffName,
  }));
}

function setFormData(data) {
  formData.id = data.id;
  formData.name = data.name;
  formData.types = data.types;
  formData.perType = data.perType;
  formData.wechatRobotChatid = data.wechatRobotChatid;
  formData.wechatReceivers = data.wechatReceivers;
  formData.phoneReceivers = data.phoneReceivers;
  formData.emailReceivers = data.emailReceivers;
  formData.wechatReceiverIds = data.wechatReceivers.map(item => item.staffId);
  formData.phoneReceiverIds = data.phoneReceivers.map(item => item.staffId);
  formData.emailReceiverIds = data.emailReceivers.map(item => item.staffId);
  nextTick(() => {
    wechatReceiverRef.value?.setSelected(staffDataToStaffData(data.wechatReceivers));
    phoneReceiverRef.value?.setSelected(staffDataToStaffData(data.phoneReceivers));
    emailReceiverRef.value?.setSelected(staffDataToStaffData(data.emailReceivers));
  });
}

function handleChangePerType(val) {
  // 如果是编辑状态，切换的时候需要尝试设置人员选择选中值
  if (formData.id && val === 1) {
    nextTick(() => {
      wechatReceiverRef.value?.setSelected(staffDataToStaffData(formData.wechatReceivers));
    });
  }
}

function reset() {
  formData.id = '';
  formData.name = '';
  formData.types = [];
  formData.perType = 1;
  formData.wechatRobotChatid = '';
  formData.wechatReceiverIds = [];
  formData.wechatReceivers = [];
  formData.phoneReceiverIds = [];
  formData.phoneReceivers = [];
  formData.emailReceiverIds = [];
  formData.emailReceivers = [];
}

async function handleTest() {
  const result = await formRef.value.validate();
  if (result === true) {
    await spaceStore.testAlarm(getSubData());
    showDialogAlert({
      title: '测试内容已发送，请同步验证是否成功',
    });
  }
}

function handleClose() {
  close();
}
async function handleConfirm() {
  const result = await formRef.value.validate();
  if (result === true) {
    const subData = getSubData();
    if (formData.id) {
      await spaceStore.updateAlarm(subData);
    } else {
      await spaceStore.addAlarm(subData);
    }
    close();
    emit('update');
  }
}

function open(data) {
  visible.value = true;
  if (data) {
    setFormData(data);
    rawData.value = data;
  }
}
function close() {
  visible.value = false;
  reset();
  rawData.value = {};
  formRef.value.reset();
}

defineExpose({ open, close });
</script>

<template>
  <BasicDialog
    class="des-space__put-alarm__dialog"
    width="560"
    :visible.sync="visible"
    :header="formData.id ? '修改告警' : '新增告警'"
    confirmButtonText="确定"
    :submitDisabled="disabled"
    @handleClose="handleClose"
    @handleConfirm="handleConfirm"
  >
    <t-form class="des-space__put-alarm" :data="formData" style="padding-right:12px;" ref="formRef" labelWidth="78px" :rules="rules">
      <t-form-item label="告警名称" name="name">
        <t-input v-model="formData.name" placeholder="请输入" :maxlength="30" show-limit-number></t-input>
      </t-form-item>
      <t-form-item label="告警类型" name="types">
        <t-checkbox-group v-model="formData.types" :options="options" />
      </t-form-item>
      <section v-if="formData.types.length" class="des-space__put-alarm__type-info-container">
        <template v-if="formData.types.includes(1)">
          <div class="des-space__put-alarm__type-info-item">
            <div class="title">企微机器人告警配置</div>
            <div class="content">
              <t-form-item label="人群类型" name="perType">
                <t-radio-group v-model="formData.perType" @change="handleChangePerType">
                  <t-radio :value="1">个人</t-radio>
                  <t-radio :value="2">群</t-radio>
                </t-radio-group>
              </t-form-item>
              <t-form-item v-if="formData.perType === 1" label="接收人" name="wechatReceiverIds">
                <sdc-staff-selector v-model="formData.wechatReceiverIds" ref="wechatReceiverRef" multiple style="width:100%;" placeholder="请选择人员" modalClass="des-sdc-modal--fix" size="small" @change="(choosed) => handleChangeStaff('wechatReceivers', choosed)"></sdc-staff-selector>
              </t-form-item>
              <t-form-item v-if="formData.perType === 2" label="chatid" name="wechatRobotChatid" help="注意：该告警配置需先拉“HR中台业务”机器人进群">
                <t-input v-model="formData.wechatRobotChatid" placheholder="请输入"></t-input>
              </t-form-item>
            </div>
          </div>
        </template>
        <template v-if="formData.types.includes(2)">
          <div class="des-space__put-alarm__type-info-item">
            <div class="title">电话告警配置</div>
            <div class="content">
              <t-form-item label="接收人" name="phoneReceiverIds">
                <sdc-staff-selector v-model="formData.phoneReceiverIds" ref="phoneReceiverRef" multiple style="width:100%;" placeholder="请选择人员" modalClass="des-sdc-modal--fix" size="small" @change="(choosed) => handleChangeStaff('phoneReceivers', choosed)"></sdc-staff-selector>
              </t-form-item>
            </div>
          </div>
        </template>
        <template v-if="formData.types.includes(3)">
          <div class="des-space__put-alarm__type-info-item">
            <div class="title">邮件告警配置</div>
            <div class="content">
              <t-form-item label="接收人" name="emailReceiverIds">
                <sdc-staff-selector v-model="formData.emailReceiverIds" ref="emailReceiverRef" multiple style="width:100%;" placeholder="请选择人员" modalClass="des-sdc-modal--fix" size="small" @change="(choosed) => handleChangeStaff('emailReceivers', choosed)"></sdc-staff-selector>
              </t-form-item>
            </div>
          </div>
        </template>
      </section>
    </t-form>
    <template #footer-left v-if="formData.types.length">
      <t-button :disabled="disabled" theme="default" variant="outline" @click="handleTest" style="margin-right:8px;">测试</t-button>
    </template>
  </BasicDialog>
</template>

<style lang="less" scoped>
.des-space__put-alarm {
  :deep(.t-form__label--right) {
    padding-right: 12px;
  }
}
.des-space__put-alarm__dialog {
  :deep(.footer-wrap) {
    justify-content: end;
  }
}
.des-space__put-alarm__type-info-container {
  display: flex;
  padding: 16px;
  flex-direction: column;
  align-items: flex-start;
  gap: 12px;
  align-self: stretch;
  border-radius: 4px;
  background: #EBF0F7;
}
.des-space__put-alarm__type-info-item {
  width: 100%;
  border-radius: 4px;
  background: #FFF;
  .title {
    padding: 8px 12px;
    border-bottom: 1px solid #EEE;
    color: #333333;
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px;
  }
  .content {
    padding: 16px 12px;
  }
}
</style>
