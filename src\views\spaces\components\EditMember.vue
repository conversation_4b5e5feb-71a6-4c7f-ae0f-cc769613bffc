<script setup>
import { ref, defineExpose, defineEmits, defineProps } from 'vue';
import BasicDialog from '@/components/BasicDialog.vue';
import { useSpacesStore } from '@/stores/spaces';
import { MessagePlugin } from 'tdesign-vue';
import { useForm } from '@/hooks';

const props = defineProps({
  spaceId: {
    type: String,
    default: '',
  },
  memberGroupOptions: {
    type: Array,
    default: () => [],
  },
  ownerGroupMembers: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits('update', 'updateOwnerGroupMembers');
const spaceStore = useSpacesStore();
const visible = ref(false);

const initFormData = {
  id: '',
  user: {},
  userGroupList: [],
};

const { form: formData, formRef, validateForm, resetForm } = useForm(initFormData);

async function handleConfirm() {
  const success = await validateForm();
  if (success) {
    // 如果是这人在负责人组且是最后一个成员，不能移除负责人组
    if (ownerGroupInfo && props.ownerGroupMembers.length === 1) {
      // 查出formData.userGroupList是否包含负责人组
      if (!formData.userGroupList.includes(ownerGroupInfo.code)) {
        MessagePlugin.error('负责人组不能移除');
        return;
      }
    }
    const params = {
      id: formData.id,
      spaceId: props.spaceId,
      memberList: [{ ...formData.user }],
      spaceUserGroupList: props.memberGroupOptions.filter(item => formData.userGroupList.includes(item.value)),
    };
    await spaceStore.editSpacesMembers(params);
    close();
    emit('update');
    emit('updateOwnerGroupMembers');
    MessagePlugin.success('修改成功');
  }
}

// 打开编辑时，保存空间负责组的信息，用于判断是否是最后一个成员
let ownerGroupInfo = null;
function open(data) {
  visible.value = true;
  formData.id = data.id;
  formData.user = data.user;
  formData.userGroupList = data.spaceUserGroupList.map(item => item.code);
  ownerGroupInfo = data.spaceUserGroupList.find(item => item.isSystemDefault === 1);
}
function close() {
  resetForm();
  ownerGroupInfo = null;
  visible.value = false;
}

defineExpose({ open, close });
</script>

<template>
  <BasicDialog
    width="640"
    :visible.sync="visible"
    header="编辑成员"
    :onClosed="close"
    @handleClose="close"
    @handleConfirm="handleConfirm"
  >
    <t-form class="des-space__edit-menber" :data="formData" style="padding-right:12px;" ref="formRef" labelWidth="98px">
      <t-form-item label="成员姓名" name="name">
        <span>{{ formData.user.staffName }}</span>
      </t-form-item>
      <t-form-item label="加入用户组" name="userGroupList">
        <t-select v-model="formData.userGroupList" :options="memberGroupOptions" multiple clearable filterable></t-select>
      </t-form-item>
    </t-form>
  </BasicDialog>
</template>

<style lang="less" scoped>
.des-space__edit-menber {
  :deep(.t-form__label--right) {
    padding-right: 12px;
  }
}
</style>
