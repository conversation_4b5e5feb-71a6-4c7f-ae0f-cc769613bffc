<script setup>
import { MESSAGE } from '../../utils/task-type.js';
import { messageRules } from '../../utils/rules.js';
import { reactive, ref, defineExpose, onMounted, defineProps } from 'vue';
// import { useDataDevelopmentStore } from '@/stores/data-development';
import BasicConfig from './common/BasicConfig.vue';
import ResourceConfig from './common/ResourceConfig.vue';
import AlarmInfo from '../AlarmInfo.vue';

defineProps({
  currentFlowName: {
    type: String,
    default: '',
  },
  readonly: {
    type: Boolean,
    default: false,
  },
});

const formRef = ref(null);
const formData = reactive({
  taskType: MESSAGE,
  id: '',
  // 节点名称
  name: '',
  // 节点标识
  code: '',
  // 节点描述
  description: '',
  // 失败重试次数
  failRetryTimes: 0,
  // 失败重试间隔
  failRetryInterval: 0,
  // 延迟执行
  delayTime: 0,
  // 自定义参数
  // params: [
  //   { key: '1', name: '参数1', relation: 'in', type: 'string', parameter: 'value' },
  // ],
  // 运行集群
  resourceGroupId: '',
  taskProperties: {},
});

const nodeId = ref('');

function getData() {
  return {
    taskType: formData.taskType,
    id: formData.id,
    name: formData.name,
    code: formData.code,
    description: formData.description,
    failRetryTimes: formData.failRetryTimes,
    failRetryInterval: formData.failRetryInterval,
    delayTime: formData.delayTime,
    resourceGroupId: formData.resourceGroupId,
    taskProperties: {},
  };
}

async function save() {
  const result = await formRef.value.validate();
  if (result !== true) {
    return;
  }
  const data = getData();
  return {
    nodeId: nodeId.value,
    data,
  };
}
// function reset() {
//   formRef.value.reset();
// }
function setData(nodeid, data) {
  nodeId.value = nodeid;
  Object.keys(data).forEach((key) => {
    formData[key] = data[key];
  });
}

const messageTypeOptions = ref([]);

defineExpose({
  setData,
  // reset,
  save,
});

onMounted(() => { });
</script>

<template>
  <div class="des-workflow-job-form__message-node">
    <t-form :data="formData" ref="formRef" label-width="117px" :rules="messageRules" :disabled="readonly">
      <basic-config :formData="formData"></basic-config>
      <resource-config :formData="formData"></resource-config>
      <section>
        <block-header title="消息配置" style="margin-bottom: 12px;" />
        <t-form-item label="消息内容" name="taskProperties.messageContent">
          <t-textarea v-model="formData.taskProperties.messageContent" :autosize="{ minRows: 4, maxRows: 4 }" style="width: 374px;" />
        </t-form-item>
        <t-form-item label="消息模版" name="taskProperties.messageType">
          <t-select v-model="formData.taskProperties.messageType" placeholder="请选择" style="width: 374px;" :options="messageTypeOptions"></t-select>
        </t-form-item>
        <div style="padding-left: 11px; width: 491px;">
          <AlarmInfo :alarmId="111"></AlarmInfo>
        </div>
      </section>
    </t-form>
  </div>
</template>

<style lang="less" scoped>
</style>
