<script setup>
// 带背景的矩形图标
import { defineProps, computed } from 'vue';
import DesIcon from './DesIcon.vue';

const props = defineProps({
  size: {
    type: [String, Number],
    required: true,
  },
  iconSize: {
    type: [String, Number],
    required: true,
  },
  name: {
    type: String,
    required: true,
  },
  background: {
    type: String,
  },
  color: {
    type: String,
  },
});
const iconStyle = computed(() => {
  const style = {};
  if (typeof props.size === 'number' || !isNaN(props.size / 1)) {
    style.width = `${props.size}px`;
    style.height = `${props.size}px`;
  }
  if (props.background) {
    style.background = props.background;
  }
  if (props.color) {
    style.color = props.color;
  }
  return style;
});
</script>

<template>
  <div class="des-rect-icon" :style="iconStyle">
    <des-icon :name="name" :size="iconSize"></des-icon>
  </div>
</template>

<style lang="less">
.des-rect-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  background: #E4EEFF;
  color: #1556FF;
}
</style>
