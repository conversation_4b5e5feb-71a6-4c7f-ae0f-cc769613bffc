<script setup>
import { defineProps } from 'vue';
defineProps({
  title: {
    type: String,
    default: '',
  },
  content: {
    type: String,
    default: '',
  },
});
</script>

<template>
  <div class="code-panel">
    <header>
      <div class="title">{{ title }}</div>
      <slot name="head-right"></slot>
    </header>
    <div class="content">{{ content }}</div>
  </div>
</template>

<style lang="less" scoped>
.code-panel {
  border-radius: 2px;
  border: 1px solid #eee;
  header {
    display: flex;
    padding: 8px 16px;
    justify-content: space-between;
    align-items: center;
    background: #EEE;
    font-family: "PingFang SC";
    line-height: 22px;
  }
  .title {
    color: #333333;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
  }
  .content {
    padding: 12px 16px;
    max-height: 242px;
    overflow: auto;
    word-break: break-all;
    white-space: break-spaces;
  }
}
</style>
