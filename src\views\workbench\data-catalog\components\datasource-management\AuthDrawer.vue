/** 授权drawer */
<template>
  <t-drawer
    :visible.sync="visible"
    header="业务数据源授权"
    size="640px"
    :closeBtn="true"
    :cancelBtn="null"
    :confirmBtn="null"
    destroyOnClose
    @close="closeDrawer"
  >
    <div class="auth-wrap">
      <AuthForm ref="formRef" :powerList="currentUserAuth" @handleAdd="handleAdd">
        <t-form-item label="业务数据源名称">
          <span>{{ currentRow.name }}</span>
        </t-form-item>
      </AuthForm>

      <t-divider></t-divider>

      <section>
        <div>已授权的成员</div>
        <t-table row-key="index" :data="list" :columns="columns" max-height="100%" />
      </section>
    </div>
  </t-drawer>
</template>

<script setup>
import { cloneDeep } from 'lodash';
import { to } from '@/utils/util';
import { MessagePlugin } from 'tdesign-vue';
import { ref, defineExpose, reactive, computed, defineEmits } from 'vue';
import { AuthForm } from '@/views/workbench/components';
import { useDataCatalogStore } from '@/stores/data-catalog';
import { useCommonStore } from '@/stores/common';
import UserName from '@/components/UserName.vue';
import { useSpacesStore } from '@/stores/spaces';
import { storeToRefs } from 'pinia';
import { useRoute } from 'vue-router/composables';

const route = useRoute();

const emit = defineEmits(['fetchData']);

const commonStore = useCommonStore();
const spacesStore = useSpacesStore();
const { isSpaceOwner } = storeToRefs(spacesStore);

const dataCatalogStore = useDataCatalogStore();
const { fetchBusiDatasourceGet, fetchBusiDatasourceGrant } = dataCatalogStore;

const currentRow = reactive({});
const setCurrentRow = (obj) => {
  Object.assign(currentRow, obj);
};

const currentUserAuth = computed(() => {
  const staff = list.value.find(item => item.staffId === commonStore.staffId);
  return staff?.powerList.map(item => item.operateType) || [];
});

const getAuth = (powerList = []) => powerList?.map(item => ({ edit: '编辑', use: '使用', grant: '授予' }[item.operateType])).join('、');
const delDisabled = () => {
  if (list.value.length === 1) {
    return true;
  }
  if (isSpaceOwner.value) {
    return false;
  }
  if (currentUserAuth.value.includes('grant')) {
    return false;
  }
  return true;
};
const columns = ref([
  {
    title: '成员',
    colKey: 'staffDisplayName',
    width: 188,
    cell: (h, { row }) => (<UserName fullName={row.staffDisplayName}></UserName>),
  },
  { title: '权限', colKey: 'powerList', width: 296, cell: (h, { row }) => getAuth(row.powerList) },
  {
    title: '操作',
    colKey: 'operation',
    width: 108,
    cell: (h, { row, rowIndex }) => (<t-link hover="color" theme="danger" onClick={() => handleDel(rowIndex)} disabled={ delDisabled(row) }>移除</t-link>),
  },
]);

const list = ref([]);
const initData = async () => {
  const [err, data] = await to(fetchBusiDatasourceGet({ datasourceId: currentRow.id }));
  if (err) {
    return;
  }
  list.value = data.grantList;
};

const visible = ref(false);
const openDrawer = async (obj) => {
  setCurrentRow(obj);
  initData();
  visible.value = true;
};

const formRef = ref(null);
const closeDrawer = () => {
  formRef.value.resetForm();
  visible.value = false;
  emit('fetchData');
};

const handleDel = (rowIndex) => {
  list.value.splice(rowIndex, 1);

  handleGrantAuth('del');
};

// 提交前检查权限的方法
async function checkPermissionsBeforeSubmit(spaceId, staffList) {
  for (const staff of staffList) {
    const { data } = await spacesStore.checkSpaceIsOwnerByStaff(spaceId, staff.StaffID);
    if (data) {
      MessagePlugin.error(`${staff.StaffName}是空间负责人，默认已有全部权限，不需要添加`);
      return false;
    }
  }
  return true;
}

const handleAdd = async (data, callback) => {
  const { form, selectStaffList } = data;

  const formData = cloneDeep(form);
  const staffList = cloneDeep(selectStaffList);

  const canSubmit = await checkPermissionsBeforeSubmit(route.query.spaceId, staffList.value);
  if (!canSubmit) return;

  const array = staffList.value?.map(item => ({
    staffName: item.EngName,
    staffId: String(item.StaffID),
    staffDisplayName: item.StaffName,
    powerList: formData.type.map(item => ({ operateType: item })),
  }));

  // 遍历并处理每个要加入的对象
  array.forEach((newItem) => {
    const existingIndex = list.value.findIndex(item => item.staffId === newItem.staffId);

    if (existingIndex !== -1) {
      // 已存在该 staff，合并 powerList，去重 operateType
      const existingPowerList = list.value[existingIndex].powerList;

      const newEntries = newItem.powerList.filter(newPower => !existingPowerList.some(existingPower => existingPower.operateType === newPower.operateType));

      if (newEntries.length > 0) {
        list.value[existingIndex].powerList.push(...newItem.powerList);
      }
    } else {
      // 不存在：直接 push 新对象
      list.value.push(newItem);
    }
  });

  handleGrantAuth('add');
  callback();
};

const handleGrantAuth = async (type = 'add') => {
  const params = { businessDatasourceId: currentRow.id, grantList: list.value };
  const [err, success] = await to(fetchBusiDatasourceGrant(params));
  if (err) {
    return;
  }
  success && MessagePlugin('success', type === 'add' ? '授权成功' : '移除授权成功');
};

defineExpose({ openDrawer });
</script>

<style lang="less" scoped>
.auth-wrap {
  height: 100%;
  section {
    height: calc(100% - 339px);
  }
  .t-table {
    margin-top: 16px;
    height: calc(100% - 38px);
  }
}
</style>
