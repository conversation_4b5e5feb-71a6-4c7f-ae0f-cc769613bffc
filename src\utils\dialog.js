import { DialogPlugin } from 'tdesign-vue';

/**
 * 普通的提示弹窗
 * @param {object} options
 * @param {string} options.title 标题
 * @param {string} options.body 内容
 * @param {number} options.width 弹窗宽度
 * @param {boolean} options.autoDestroy 自动销毁弹窗
 * @param {string} options.confirmText 确认按钮文案
 * @param {function} options.onConfirm 确认按钮回调
 */
export function showDialogAlert(options = {}) {
  return new Promise((resolve) => {
    let className = 'des-common-dialog-plugin';
    if (!options.body) {
      className += ' des-common-dialog--nobody';
    }
    const dialog = DialogPlugin.alert({
      className,
      theme: 'info',
      header: options.title,
      width: options.width || '500px',
      body: options.body,
      closeBtn: false,
      confirmBtn: {
        content: options.confirmText || '我知道了',
      },
      onConfirm: () => {
        if (options.autoDestroy !== false) {
          dialog.destroy();
        } else {
          dialog.hide();
        }
        options.onConfirm?.(dialog);
        resolve({
          type: 'success',
          dialog,
        });
      },
    });
  });
}

/**
 * 带确定取消的弹窗
 * @param {object} options
 * @param {string} options.title 标题
 * @param {string} options.body 内容
 * @param {number} options.width 弹窗宽度
 * @param {boolean} options.autoDestroy 自动销毁弹窗
 * @param {function} options.onConfirm 确认按钮回调
 * @param {function} options.onCancel 取消按钮回调
 */
export function showDialogConfirm(options = {}) {
  let className = 'des-common-dialog-plugin';
  if (!options.body) {
    className += ' des-common-dialog--nobody';
  }
  return new Promise((resolve) => {
    const dialog = DialogPlugin.confirm({
      className,
      theme: 'info',
      header: options.title,
      width: options.width || '400px',
      body: options.body,
      closeBtn: false,
      cancelBtn: {
        theme: 'default',
        variant: 'outline',
        content: options.cancelText || '取消',
      },
      confirmBtn: {
        content: options.confirmText || '确定',
      },
      onConfirm: () => {
        if (options.autoDestroy !== false) {
          dialog.destroy();
        } else {
          dialog.hide();
        }
        options.onConfirm?.(dialog);
        resolve({
          type: 'success',
          dialog,
        });
      },
      onClose: () => {
        if (options.autoDestroy !== false) {
          dialog.destroy();
        } else {
          dialog.hide();
        }
        options.onClose?.(dialog);
        resolve({
          type: 'cancel',
          dialog,
        });
      },
    });
  });
}
