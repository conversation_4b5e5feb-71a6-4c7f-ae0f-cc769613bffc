<template>
  <div class="resource">
    <t-tabs v-model="actived">
      <t-tab-panel
        v-for="data in tabPanelList"
        :key="data.value"
        :value="data.value"
        :label="data.label"
        lazy
      >
        <component :is="getComp(data.value)" />
      </t-tab-panel>
    </t-tabs>
  </div>
</template>

<script setup>
import { ref, computed, watchEffect } from 'vue';
import { storeToRefs } from 'pinia';
import { useCommonStore } from '@/stores/common';
import ResourceManage from './resource-manage/MainPage.vue';
import ResourcePartition from './resource-partition/MainPage.vue';
import SrdataManage from './srdata-manage/MainPage.vue';
import SrresourceManage from './srresource-manage/MainPage.vue';

const commonStore = useCommonStore();
const { operateCodeList } = storeToRefs(commonStore);

const tabPanelList = computed(() => [
  { label: '资源群管理', value: 'ResourceManage', auth: 'Menu_resourceGroup' },
  { label: '资源划分', value: 'ResourcePartition', auth: 'Menu_resource' },
  { label: 'SR数据源管理', value: 'SrdataManage', auth: 'Menu_srDatasource' },
  { label: 'SR资源组管理', value: 'SrresourceManage', auth: 'Menu_srResourceGroup' },
].filter(item => operateCodeList.value.includes(item.auth)));

const actived = ref('');

watchEffect(() => {
  const tabs = tabPanelList.value.map(item => item.value);
  if (!tabs.includes(actived.value)) {
    // eslint-disable-next-line prefer-destructuring
    actived.value = tabs[0];
  }
});

// 组件映射
function getComp(name) {
  let comp = null;
  switch (name) {
    case 'ResourceManage':
      comp = ResourceManage;
      break;
    case 'ResourcePartition':
      comp = ResourcePartition;
      break;
    case 'SrdataManage':
      comp = SrdataManage;
      break;
    case 'SrresourceManage':
      comp = SrresourceManage;
      break;
    default:
      break;
  }
  return comp;
}
</script>

<style lang="less" scoped>
.resource {
  height: 100%;
  padding: 8px 20px;
  background-color: #fff;
  :deep(.t-tabs) {
    height: 100%;
    .t-tabs__content {
      height: calc(100% - 48px);
      .t-tab-panel {
        height: 100%;
      }
    }
  }
}
</style>
