const { defineConfig } = require('@vue/cli-service');
const MonacoWebpackPlugin = require('monaco-editor-webpack-plugin');

module.exports = defineConfig({
  transpileDependencies: true,
  runtimeCompiler: true,
  productionSourceMap: false,
  devServer: {
    client: {
      overlay: {
        warnings: true,
        errors: true,
      },
    },
    allowedHosts: 'all',
    client: {
      webSocketURL: 'auto://0.0.0.0:0/ws',
    },
  },
  configureWebpack: {
    plugins: [new MonacoWebpackPlugin()],
  },
  chainWebpack: (config) => {
    config.plugin('define').tap((args) => {
      const arg = args[0];
      Object.assign(arg['process.env'], {
        // 时间戳
        BUILD_TIMESTAMP: JSON.stringify(`${Date.now()}`),
      });
      return args;
    });
  },
});
