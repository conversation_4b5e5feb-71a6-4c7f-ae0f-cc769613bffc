// 事件总线 https://github.com/developit/mitt
import mitt from 'mitt';

export const EVENT_NAMES = {
  DATA_CATALOG: {
    REFRESH_BUSINESS_DATASOURCE: 'REFRESH_BUSINESS_DATASOURCE',  // 刷新业务数据源事件
  },
  DATA_DEVELOPMENT: {
    REFRESH_GIT_PROJECT: 'REFRESH_GIT_PROJECT', // 刷新Git项目管理事件
    REFRESH_FLOW_DEFINE: 'REFRESH_FLOW_DEFINE', // 刷新工作流定义事件
    REFRESH_FLOW_INSTANCE: 'REFRESH_FLOW_INSTANCE', // 刷新工作流实例事件
    REFRESH_TASK_INSTANCE: 'REFRESH_TASK_INSTANCE', // 刷新任务实例事件
    CHANGE_TAB: 'CHANGE_TAB', // 切换数据开发下的tab事件
  },
  WORK_FLOW_PANEL: {
    GRAPH_NODE_EDIT: 'GRAPH_NODE_EDIT', // 画布节点编辑
    GRAPH_NODE_DELETE: 'GRAPH_NODE_DELETE', // 画布节点删除
    GRAPH_NODE_VIEW: 'GRAPH_NODE_VIEW', // 画布节点查看
    GRAPH_NODE_ADD: 'GRAPH_NODE_ADD', // 画布节点新增
    GRAPH_NODE_DEBUG: 'GRAPH_NODE_DEBUG', // 调试节点
    LINK_PRE_TASK: 'LINK_PRE_TASK', // 画布前置任务连线
    GRAPH_DATA_COMPARE: 'GRAPH_DATA_COMPARE', // 画布数据对比
    GRAPH_NODE_SELECT: 'GRAPH_NODE_SELECT', // 画布节点选中
    VIEW_TASK_LOG: 'VIEW_TASK_LOG', // 查看节点任务日志
  },
};

export default mitt();
