<script setup>
// 带色块的标题，标题前方会有一个蓝色小色块
import { defineProps } from 'vue';

defineProps({
  title: String,
  desc: String,
});
</script>

<template>
  <div class="des-common-block-header">
    <h2>{{ title }}</h2>
    <p v-if="desc">{{ desc }}</p>
  </div>
</template>

<style lang="less" scoped>
.des-common-block-header {
  h2 {
    position: relative;
    padding-left: 11px;
    color: #333333;
    font-size: 14px;
    font-weight: 600;
    line-height: 22px;
    height: 22px;
    &::before {
      position: absolute;
      left: 0;
      top: 50%;
      transform: translate3d(0, -50%, 0);
      content: ' ';
      width: 3px;
      height: 14px;
      background: var(--des-color-theme);
    }
  }
  p {
    margin-top: 2px;
    padding-left: 11px;
    color: #999999;
    font-size: 12px;
    font-weight: 400;
    text-align: left;
    line-height: 20px;
  }
}
</style>
