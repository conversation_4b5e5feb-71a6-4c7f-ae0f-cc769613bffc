<script setup>
import { ref, defineExpose, defineProps, defineEmits } from 'vue';
import BasicDialog from '@/components/BasicDialog.vue';
import { useSpacesStore } from '@/stores/spaces';
import { MessagePlugin } from 'tdesign-vue';
import { useForm } from '@/hooks';

const props = defineProps({
  spaceId: {
    type: String,
    default: '',
  },
});
const staffRef = ref(null);

const emit = defineEmits('update');
const spaceStore = useSpacesStore();
const visible = ref(false);

const selectedStaffList = ref([]);
function handleChangeStaff(val) {
  selectedStaffList.value = val;
}

const initFormData = {
  id: '',
  name: '',
  code: '',
  memberList: [],
};

const initFormRules = {
  name: [{ required: true, message: '请输入用户组名称' }],
  code: [{ required: true, message: '请输入用户组标识' }],
  memberList: [{ required: true, message: '请选择用户组成员' }],
};

const { form: formData, rules, formRef, validateForm, resetForm } = useForm(initFormData, initFormRules);

async function handleConfirm() {
  const success = await validateForm();
  if (success) {
    const params = {
      id: formData.id,
      name: formData.name,
      code: formData.code,
      spaceId: props.spaceId,
      isSystemDefault: 0,
      memberList: selectedStaffList.value.map(item => ({
        staffId: item.StaffID,
        engName: item.EngName,
        staffName: item.StaffName,
      })),
    };
    await spaceStore.editSpacesUserGroup(params);
    emit('update');
    close();
    MessagePlugin.success('添加成功');
  }
}

function open(data) {
  visible.value = true;
  formData.id = data.id;
  formData.name = data.name;
  formData.code = data.code;
  formData.memberList = data.memberList || [];
  // 初始化员工选择器数据
  if (formData.memberList.length) {
    const array = formData.memberList.map(item => ({
      StaffName: item.staffName,
      StaffID: item.staffId,
      EngName: item.engName,
    }));
    staffRef.value.setSelected(array);
  }
}
function close() {
  visible.value = false;
  staffRef.value.clearSelected();
  selectedStaffList.value = [];
  resetForm();
}

defineExpose({ open, close });
</script>

<template>
  <BasicDialog
    width="560"
    :visible.sync="visible"
    header="编辑用户组"
    :onClosed="close"
    @handleClose="close"
    @handleConfirm="handleConfirm"
  >
    <t-form class="des-space__edit-menber-group" :data="formData" style="padding-right:12px;" ref="formRef" labelWidth="98px" :rules="rules">
      <t-form-item label="用户组名称" name="name">
        <t-input v-model="formData.name"></t-input>
      </t-form-item>
      <t-form-item label="用户组标识" name="code">
        <span>{{ formData.code }}</span>
      </t-form-item>
      <t-form-item label="用户组成员">
        <sdc-staff-selector ref="staffRef" v-model="formData.memberList" @change="handleChangeStaff" multiple style="width:100%;" placeholder="请选择人员" modalClass="des-sdc-modal--fix" size="small"></sdc-staff-selector>
      </t-form-item>
    </t-form>
  </BasicDialog>
</template>

<style lang="less" scoped>
.des-space__edit-menber-group {
  :deep(.t-form__label--right) {
    padding-right: 12px;
  }
}
</style>
