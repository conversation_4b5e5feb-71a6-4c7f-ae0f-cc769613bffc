<script setup>
import { ref, reactive, defineProps, computed, onMounted } from 'vue';
import UserName from '@/components/UserName.vue';
import { AddIcon, SearchIcon } from 'tdesign-icons-vue';
import AddMember from './AddMember.vue';
import EditMember from './EditMember.vue';
import AuthorizeMember from './AuthorizeMember.vue';
import AuthorizeMemberGroup from './AuthorizeMemberGroup.vue';
import AddMemberGroup from './AddMemberGroup.vue';
import EditMemberGroup from './EditMemberGroup.vue';
import { useSpacesStore } from '@/stores/spaces';
import { useCommonStore } from '@/stores/common';
import { showDialogAlert, showDialogConfirm } from '@/utils/dialog';
import { MessagePlugin } from 'tdesign-vue';
import { useRouter } from 'vue-router/composables';
import { formatTime } from '@/utils/util';
import { debounce } from 'lodash';

const router = useRouter();

const props = defineProps({
  spaceId: {
    type: String,
    default: '',
  },
  readonly: {
    type: Boolean,
    default: false,
  },
  showButton: {
    type: Boolean,
    default: false,
  },
});

const commonStore = useCommonStore();
const spaceStore = useSpacesStore();

const isMember = computed(() => actived.value === 'member');
const actived = ref('member');
const menus = ref([
  {
    label: '成员管理',
    value: 'member',
  },
  {
    label: '用户组管理',
    value: 'group',
  },
]);
function handleActived(val) {
  if (actived.value !== val) {
    actived.value = val;
    pagination.current = 1;
    pagination.pageSize = 10;
    fetchData();
  }
}

const formData = reactive({
  queryKey: '',
  userGroup: '',
});
const placeholder = computed(() => (isMember.value ? '姓名...' : '标识/名称...'));

function isMyself(row) {
  return row.user?.staffId === commonStore.staffId;
}
function isSystemDefault(group) {
  return group.isSystemDefault === 1;
}
const loading = ref(false);
const tableData = ref([]);
// 是否在空间负责人组下
function isUnderSystemDefault(row) {
  return row.spaceUserGroupList?.some(group => isSystemDefault(group));
}

const MemberColumns = computed(() => {
  const list = [
    {
      colKey: 'user.staffName',
      title: '成员姓名',
      cell: (h, { row }) => (
      <t-space size="8px">
        <UserName fullName={row.user?.staffName}></UserName>
        {isMyself(row) && <span class="page-space-detail__tag">自己</span>}
      </t-space>
      ),
    },
    { colKey: 'spaceUserGroupList', title: '已加入用户组', ellipsis: true, cell: (h, { row }) => (<span>{ row.spaceUserGroupList?.map(item => item.name).join('，') || '-' }</span>) },
    { colKey: 'createTime', title: '添加时间', cell: (h, { row }) => formatTime(row.createTime) },
  ];
  if (!props.readonly) {
    list.push({
      colKey: 'operation',
      title: '操作',
      width: 200,
      cell: (h, { row }) => (
        (
          <t-space size="16px">
            <t-link hover="color" theme="primary" onClick={() => handleEditMember(row)}>编辑</t-link>
            <t-link hover="color" theme="primary" onClick={() => handleAuthorizeMember(row)}>授权</t-link>
            <t-link hover="color" theme="primary" onClick={() => handleRemoveMember(row)} disabled={ isUnderSystemDefault(row) && ownerGroupMembers.value.length === 1 }>删除</t-link>
          </t-space>
        )
      ),
    });
  }
  return list;
});
const groupColumns = computed(() => {
  const list = [
    {
      colKey: 'name',
      title: '用户组名称',
    },
    {
      colKey: 'code',
      title: '用户组标识',
    },
    { colKey: 'createTime', title: '添加时间', cell: (h, { row }) => formatTime(row.createTime) },
  ];
  if (!props.readonly) {
    list.push({
      colKey: 'operation',
      title: '操作',
      width: 200,
      cell: (h, { row }) => (
        (
          <t-space size="16px">
            <t-link hover="color" theme="primary" disabled={ isSystemDefault(row) } onClick={() => handleEditMemberGroup(row)}>编辑</t-link>
            <t-link hover="color" theme="primary" disabled={ isSystemDefault(row) } onClick={() => handleAuthorizeMemberGroup(row)}>授权</t-link>
            <t-link hover="color" theme="primary" disabled={ isSystemDefault(row) } onClick={() => handleRemoveMemberGroup(row)}>删除</t-link>
          </t-space>
        )
      ),
    });
  }
  return list;
});
// 表格列信息
const columns = computed(() => (isMember.value ? MemberColumns.value : groupColumns.value));
const pagination = reactive({
  current: 1,
  pageSize: 10,
  showJumper: true,
  onChange: (pageInfo) => {
    pagination.current = pageInfo.current;
    if (pagination.pageSize !== pageInfo.pageSize) {
      pagination.page = 1;
    }
    pagination.pageSize = pageInfo.pageSize;
    fetchData();
  },
});

const debouncedSearch = debounce(() => {
  pagination.current = 1;
  fetchData();
}, 500);

async function fetchData() {
  loading.value = true;
  let response = {};
  try {
    if (isMember.value) {
      response = await spaceStore.getSpacesMembers({
        queryData: {
          queryKey: formData.queryKey,
          spaceId: props.spaceId,
          userGroupId: memberGroupOptions.value.find(item => item.value === formData.userGroup)?.id,
        },
        pageNum: pagination.current,
        pageSize: pagination.pageSize,
      });
    } else {
      response = await spaceStore.getSpacesUserGroupList(props.spaceId, {
        queryData: {
          queryKey: formData.queryKey,
        },
        pageNum: pagination.current,
        pageSize: pagination.pageSize,
      });
    }
    tableData.value = response.data.list;
    pagination.total = response.data.total;
  } catch (error) {}
  loading.value = false;
}

// 新增成员
const addMemberRef = ref(null);
function handleAddMember() {
  addMemberRef.value.open();
}
// 修改成员
const editMemberRef = ref(null);
function handleEditMember(row) {
  console.log('修改成员', row);
  editMemberRef.value.open(row);
}
// 授权成员
const authorizeMemberRef = ref(null);
function handleAuthorizeMember(row) {
  authorizeMemberRef.value.open(row);
}
// 删除
async function handleRemoveMember(row) {
  const result = await showDialogConfirm({
    title: `确定删除成员 ${row.user.staffName}吗？`,
    body: '删除后，该成员将无法继续访问空间及空间下的资源。',
  });
  if (result.type === 'cancel') return;
  await spaceStore.deleteSpacesMembers({
    idList: [row.id],
    spaceId: props.spaceId,
  });
  MessagePlugin.success('删除成功');
  if (isMyself(row)) {
    router.push({ name: 'spaces' });
    return;
  }
  if (tableData.value.length === 1 && pagination.current > 1) {
    pagination.current -= 1;
  }
  fetchData();
  getOwnerGroupMembers();
}
// 删除用户组
async function handleRemoveMemberGroup(row) {
  const result = await showDialogConfirm({
    title: `确定删除用户组 ${row.name}吗？`,
    body: '删除后不可恢复',
  });
  if (result.type === 'cancel') return;
  try {
    await spaceStore.deleteSpacesUserGroup({ spaceId: props.spaceId, id: row.id }, { showErrorMsg: false });
    MessagePlugin.success('删除成功');
  } catch (error) {
    showDialogAlert({
      title: error.msg,
      width: '440px',
    });
    return;
  }
  if (tableData.value.length === 1 && pagination.current > 1) {
    pagination.current -= 1;
  }
  fetchData();
}
// 新增用户组
const addMemberGroupRef = ref(null);
function handleAddMemberGroup() {
  addMemberGroupRef.value.open();
}
// 修改用户组
const eeditMemberGroupRef = ref(null);
function handleEditMemberGroup(row) {
  eeditMemberGroupRef.value.open(row);
}
// 授权用户组
const authorizeMemberGroupRef = ref(null);
function handleAuthorizeMemberGroup(row) {
  authorizeMemberGroupRef.value.open(row);
}
// 获取用户组选项
const memberGroupOptions = ref([]);
async function getMemberGroupOptions() {
  const params = {
    queryData: {
      queryKey: '',
    },
    pageNum: 1,
    pageSize: 999,
  };
  const { data } = await spaceStore.getSpacesUserGroupList(props.spaceId, params);
  memberGroupOptions.value = data.list.map(item => ({
    label: item.name,
    value: item.code,
    name: item.name,
    code: item.code,
    id: item.id,
  }));
}

// 用户选项
const userOptions = computed(() => {
  if (isMember.value) {
    return tableData.value.map(item => ({
      label: item.user?.staffName,
      value: item.user?.staffId,
      spaceUserGroupList: item.spaceUserGroupList,
      ...item.user,
    }));
  }
  return [];
});

function handleReset() {
  formData.queryKey = '';
  formData.userGroup = '';
  debouncedSearch();
}

function handleUpdate() {
  fetchData();
  getMemberGroupOptions();
}

const ownerGroupMembers = ref([]);
async function getOwnerGroupMembers() {
  const { data } = await spaceStore.getSpacesOwnerGroupMembers(props.spaceId);
  ownerGroupMembers.value = data;
}

onMounted(() => {
  fetchData();
  getMemberGroupOptions();
  getOwnerGroupMembers();
});
</script>

<template>
  <div class="block-member">
    <section class="page-space-detail__tab">
      <span v-for="item in menus" :class="{ actived: actived === item.value }"  @click="handleActived(item.value)" :key="item.value">{{ item.label }}</span>
    </section>
    <div class="filter-wrap">
      <t-space>
        <t-input v-model="formData.queryKey" :placeholder="placeholder" clearable style="width: 290px;" @change="debouncedSearch">
          <template #suffixIcon>
            <search-icon :style="{ cursor: 'pointer' }" />
          </template>
        </t-input>
        <t-select v-model="formData.userGroup" :options="memberGroupOptions" clearable filterable placeholder="用户组" @change="debouncedSearch" v-if="actived === 'member'" ></t-select>
        <t-button variant="outline" @click="handleReset">重置</t-button>
      </t-space>
      <div class="right-btn-group" v-if="!readonly && showButton">
        <t-button theme="primary" @click="handleAddMember" v-if="actived === 'member'"><add-icon slot="icon" />添加成员</t-button>
        <t-button theme="primary" v-else @click="handleAddMemberGroup"><add-icon slot="icon" />添加用户组</t-button>
      </div>
    </div>
    <div class="page-space-detail__member-list">
      <t-table row-key="id" :data="tableData" :columns="columns" :loading="loading" :pagination="pagination" />
    </div>

    <add-member ref="addMemberRef" :spaceId="spaceId" :memberGroupOptions="memberGroupOptions" @update="fetchData" @updateOwnerGroupMembers="getOwnerGroupMembers"></add-member>
    <edit-member ref="editMemberRef" :spaceId="spaceId" :memberGroupOptions="memberGroupOptions" :ownerGroupMembers="ownerGroupMembers" @update="fetchData" @updateOwnerGroupMembers="getOwnerGroupMembers"></edit-member>
    <authorize-member ref="authorizeMemberRef" :spaceId="spaceId" :memberGroupOptions="memberGroupOptions" :userOptions="userOptions"></authorize-member>
    <add-member-group ref="addMemberGroupRef" :spaceId="spaceId" @update="handleUpdate"></add-member-group>
    <edit-member-group ref="eeditMemberGroupRef" :spaceId="spaceId" @update="handleUpdate"></edit-member-group>
    <authorize-member-group ref="authorizeMemberGroupRef" :spaceId="spaceId" :memberGroupOptions="memberGroupOptions"></authorize-member-group>
  </div>
</template>

<style lang="less" scoped>
.block-member {
  background-color: #fff;
  border-radius: 4px;
  .page-space-detail__tab {
    padding: 0 20px;
    border-bottom: 1px solid #EEEEEE;
    display: flex;
    height: 46px;
    & > span {
      position: relative;
      margin-right: 24px;
      text-align: center;
      font-family: "PingFang SC";
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 46px;
      color: #666666;
      cursor: pointer;
      &.actived {
        color: #3464e0;
        font-weight: 600;
        &::after {
          position: absolute;
          left: 0;
          right: 0;
          bottom: 0;
          height: 2px;
          background-color: #3464E0;
          content: ' ';
        }
      }
      &[disabled] {
        cursor: not-allowed;
      }
    }
  }
  .filter-wrap {
    display: flex;
    height: 56px;
    padding: 0 20px;
    align-items: center;
    justify-content: space-between;
  }
  h2 {
    color: #333333;
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
  }
  .search-content {
    display: flex;
    padding: 0 20px;
    margin-bottom: 16px;
  }
  .page-space-detail__member-list {
    padding: 0 20px;
    :deep(.t-table__pagination) {
      padding: 12px 0;
    }
  }
}
</style>
