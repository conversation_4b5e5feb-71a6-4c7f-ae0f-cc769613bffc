<template>
  <div class="des-flex-align-center operate-wrap">
    <SelectWithIcon
      style="width: 140px"
      v-model="value"
      :options="options"
      clearable
      placeholder="全部数据源"
      @change="selectChange"
    ></SelectWithIcon>
    <DropdownMenu @change="dropdownChange"></DropdownMenu>
  </div>
</template>

<script setup>
import { ref, onMounted, defineEmits } from 'vue';
import SelectWithIcon from '@/components/SelectWithIcon.vue';
import { DropdownMenu } from '../components';

const emit = defineEmits(['selectChange', 'dropdownChange']);

const value = ref('');
const options = ref([
  {
    label: 'StarRock1',
    value: '1',
  },
  {
    label: 'StarRock2',
    value: '2',
  },
]);

const selectChange = (val) => {
  emit('selectChange', val);
};

const dropdownChange = (val) => {
  emit('dropdownChange', val);
};

const init = () => {

};

onMounted(() => {
  init();
});
</script>

<style lang="less" scoped>
.operate-wrap {
  justify-content: space-between;
  margin-bottom: 12px;
}
</style>
