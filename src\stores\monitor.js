import { defineStore, acceptHMRUpdate } from 'pinia';
import axios from '@/server/client';
import {
  API_MONITOR_SQL_EXECUTION_LIST,
} from '@/server/api/monitor';

export const useMonitorStore = defineStore('monitor', {
  state: () => ({}),
  actions: {
    // 按条件查看SQL执行记录
    fetchSqlExecutionList(datasourceId, data) {
      return axios({
        method: 'post',
        url: API_MONITOR_SQL_EXECUTION_LIST(datasourceId),
        data,
      });
    },
  },
});

if (import.meta.webpackHot) {
  import.meta.webpackHot.accept(acceptHMRUpdate(useMonitorStore, import.meta.webpackHot));
}
