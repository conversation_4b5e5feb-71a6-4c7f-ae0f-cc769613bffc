<template>
  <div class="advanced-info__wrap">
    <div class="index-info">
      <p class="title">索引信息</p>
      <div>
        <span class="label">Bitmapsu索引</span><span class="content">retail_e_commer1</span>
      </div>
      <div>
        <span class="label">Bloom Filter索引</span><span class="content">retail_e_commer1</span>
      </div>
    </div>
    <div>
      <p class="title">其他信息</p>
      <div><span class="label">分桶数量</span><span class="content">retail_e_commer1</span></div>
      <div><span class="label">压缩方式</span><span class="content">retail_e_commer1</span></div>
      <div class="advanced">
        <span class="label">高级参数</span>
        <span class="content">retail_agfdshrthtsrhsge_commer1</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from 'vue';

defineProps({
  data: Object,
});
</script>

<style lang="less" scoped>
.advanced-info__wrap {
  height: 100%;
  overflow-y: auto;

  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  .title {
    text-align: left;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333333;
  }
  span {
    margin-bottom: 8px;
  }
  .label {
    display: inline-block;
    margin-right: 8px;
    color: #999999;
  }
  .content {
    display: inline-block;
    color: #333333;
  }
  .index-info {
    margin-bottom: 12px;
  }
  .advanced {
    display: flex;
    align-items: start;
    .label {
      flex-basis: 52px;
    }
    .content {
      flex: 1;
      word-break: break-word;
      white-space: normal;
      overflow-wrap: break-word;
    }
  }
}
</style>
