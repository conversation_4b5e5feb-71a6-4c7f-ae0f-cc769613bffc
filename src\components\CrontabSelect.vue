<script setup>
// 基于vue-crontab-ui自定义面板，https://github.com/jingjingke/crontab
import { defineProps, defineEmits } from 'vue';
import { ChevronDownIcon } from 'tdesign-icons-vue';

defineProps({
  value: {
    type: String,
    default: '',
  },
});
const emit = defineEmits(['input']);
function onInputChange(val) {
  emit('input', val);
}
</script>

<template>
  <t-select-input
    :value="value"
    @input-change="onInputChange"
    placeholder="请选择"
    :popupProps="{
      overlayClassName: 'des-crontab',
      destroyOnClose: true,
    }">
    <template #panel>
      <crontab :value="value" @input="onInputChange" style="width: 600px;" />
    </template>
    <template #suffixIcon>
      <chevron-down-icon />
    </template>
  </t-select-input>
</template>

<style lang="less">
.des-crontab {
  .t-popup__content {
    padding: 0 0 24px 0;
  }
  .crontab-input-warp {
    input[type='radio'] {
      position: relative;
      top: 0;
      display: inline-block;
      width: 16px;
      height: 16px;
      vertical-align: middle;
      border-radius: var(--td-radius-circle);
      border: 1px solid var(--td-border-level-2-color);
      background-color: var(--td-bg-color-container);
      transition: border .2s cubic-bezier(0,0,.15,1);
      cursor: pointer;
    }

    input[type='radio']:checked {
      border-color: var(--td-brand-color);
    }

    input[type='radio']:checked::after {
      content: "";
      position: absolute;
      top: 50%;
      left: 50%;
      width: 16px;
      height: 16px;
      margin-top: -8px;
      margin-left: -8px;
      transform: scale(.5);
      border-radius: var(--td-radius-circle);
      background-color: var(--td-brand-color);
      transition: all .2s cubic-bezier(0,0,.15,1);
    }
    input[type='checkbox'] {
      position: relative;
      top: 0;
      display: inline-block;
      width: 16px;
      height: 16px;
      vertical-align: middle;
      border: 1px solid var(--td-border-level-2-color);
      border-radius: var(--td-radius-default);
      background-color: var(--td-bg-color-container);
      box-sizing: border-box;
      cursor: pointer;
    }
    input[type='checkbox']:checked {
      border-color: var(--td-brand-color);
      background-color: var(--td-brand-color);
      transition: background-color .2s cubic-bezier(.82,0,1,.9);
    }

    input[type='checkbox']:checked::after {
      content: "";
      position: absolute;
      box-sizing: border-box;
      opacity: 1;
      top: 6px;
      left: 3px;
      width: 5px;
      height: 9px;
      border: 2px solid var(--td-text-color-anti);
      border-radius: 0 0 1px;
      border-top: 0;
      border-left: 0;
      transform: rotate(45deg) scale(1) translate(-50%, -50%);
      background: transparent;
    }
    input[type='number'] {
      border: 1px solid var(--td-border-level-2-color);
      border-radius: var(--td-radius-default);
    }
  }
  .crontab-check-warp {
    label {
      width: auto;
      min-width: 52px;
      line-height: 24px;
      vertical-align: middle;
    }
  }
  .crontab-popup-main {
    .crontab-popup-result {
      &:last-child {
        display: none;
      }
    }
  }
}
</style>
