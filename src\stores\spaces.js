import { defineStore, acceptHMRUpdate } from 'pinia';
import axios from '@/server/client';
import {
  API_SPACES_OPERATE,
  API_SPACES_MY,
  API_SPACES_LIST,
  API_SPACES_HASDATA,
  API_SPACES_CHECK_CODE,
  API_SPACES_IS_OWNER,
  API_SPACES_IS_OWNER_BY_STAFF,
  API_SPACES_PROCESSES_CHECKNAME,
  API_ALARM_OPERATE,
  API_ALARM_TEST,
  API_ALARM_LIST,
  API_SPACES_DOMAIN_LIST,
  API_SPACES_MEMBERS,
  API_SPACES_MEMBERS_ADD,
  API_SPACES_MEMBERS_EDIT,
  API_SPACES_MEMBERS_DELETE,
  API_SPACES_MEMBERS_TRANSFER,
  API_SPACES_ALARM_CHECK_NAME,
  API_SPACES_USER_GROUP_OPERATE,
  API_SPACES_USER_GROUP_LIST,
  API_SPACES_FLOW_AUTH_DETAIL,
  API_SPACES_GIT_AUTH_DETAIL,
  API_SPACES_FLOW_AUTH,
  API_SPACES_GIT_AUTH,
  API_SPACES_PRIVILEGE_MIGRATION,
  API_SPACES_OWNER_GROUP_MEMBERS,
  API_SPACES_USER_GROUP_CHECK_NAME,
} from '@/server/api/spaces';

export const useSpacesStore = defineStore('spaces', {
  state: () => ({
    currentSpace: '', // 当前选中的空间
    isSpaceOwner: false, // 是否当前选中的空间负责人
    mySpaces: [], // 我的空间
    allOrgAreas: [], // 领域列表
  }),
  getters: {
    currentSpaceName(state) {
      return state.mySpaces.find(item => item.id === state.currentSpace)?.name;
    },
  },
  actions: {
    setCurrentSpace(value) {
      this.currentSpace = value;
    },
    setMySpaces(value) {
      this.mySpaces = value;
    },
    // 空间详情
    getSpaceDetail(id = '') {
      return axios({
        url: API_SPACES_OPERATE(id),
        method: 'get',
      });
    },
    // 更新空间
    updateSpace(data = {}) {
      return axios({
        url: API_SPACES_OPERATE(data.id),
        method: 'put',
        data,
      });
    },
    // 删除空间
    deleteSpace(id = '') {
      return axios({
        url: API_SPACES_OPERATE(id),
        method: 'delete',
      });
    },
    // 新增空间
    addSpace(data = {}) {
      return axios({
        url: API_SPACES_OPERATE(),
        method: 'post',
        data,
      });
    },
    // 查询我的空间列表
    async getMySpaces(data = {}) {
      return axios({
        url: API_SPACES_MY(),
        method: 'post',
        data,
      });
    },
    // 空间列表
    async getSpacesList(data = {}) {
      return axios({
        url: API_SPACES_LIST(),
        method: 'post',
        data,
      });
    },
    // 查询空间是否存在开发数据
    async checkSpaceHasData(id = '') {
      return axios({
        url: API_SPACES_HASDATA(id),
        method: 'get',
        loading: false,
      });
    },
    // 检查code是否已经使用
    async checkSpaceCode(code = '') {
      return axios({
        url: API_SPACES_CHECK_CODE(code),
        method: 'get',
        params: { code },
        loading: false,
      });
    },
    // 判断是否是owner
    async checkSpaceIsOwner(id = '') {
      const { data } = await axios({
        url: API_SPACES_IS_OWNER(id),
        method: 'get',
        loading: false,
      });
      this.isSpaceOwner = data;
      return data;
    },
    // 判断某个用户是否某个空间负责人
    async checkSpaceIsOwnerByStaff(spaceId, staffId) {
      return axios({
        url: API_SPACES_IS_OWNER_BY_STAFF(spaceId, staffId),
        method: 'get',
        loading: false,
      });
    },
    // 判断该空间下的工作流名称是否已使用
    async checkSpaceProcessName(spaceId, name) {
      return axios({
        url: API_SPACES_PROCESSES_CHECKNAME(spaceId, name),
        method: 'get',
        loading: false,
      });
    },
    // 查询告警详情
    async getAlarmDetail(id = '') {
      return axios({
        url: API_ALARM_OPERATE(id),
        method: 'get',
        loading: false,
      });
    },
    // 更新告警
    async updateAlarm(data = {}) {
      return axios({
        url: API_ALARM_OPERATE(data.id),
        method: 'put',
        data,
      });
    },
    // 删除告警
    async deleteAlarm(id = '') {
      return axios({
        url: API_ALARM_OPERATE(id),
        method: 'delete',
      });
    },
    // 新增告警
    async addAlarm(data = {}) {
      return axios({
        url: API_ALARM_OPERATE(),
        method: 'post',
        data,
      });
    },
    // 测试告警
    async testAlarm(data = {}) {
      return axios({
        url: API_ALARM_TEST(data.id),
        method: 'post',
        data,
      });
    },
    // 查询告警列表
    async getAlarmList(data = {}) {
      return axios({
        url: API_ALARM_LIST(data),
        method: 'post',
        data,
        loading: false,
      });
    },
    // 查询领域列表
    async getAllOrgAreas() {
      const res = await axios({
        url: API_SPACES_DOMAIN_LIST(),
        method: 'get',
        loading: false,
      });
      this.allOrgAreas = (res.data || []).map(item => ({ orgArea: item.org_area, orgManager: item.org_manager, uniqueId: `${item.unique_id}` }));
      return res;
    },
    // 查询空间成员列表
    async getSpacesMembers(data = {}) {
      return axios({
        url: API_SPACES_MEMBERS(data),
        method: 'post',
        data,
        loading: false,
      });
    },
    // 新增空间成员
    async addSpacesMembers(data = {}) {
      return axios({
        url: API_SPACES_MEMBERS_ADD(),
        method: 'post',
        data,
      });
    },
    // 编辑空间成员
    async editSpacesMembers(data = {}) {
      return axios({
        url: API_SPACES_MEMBERS_EDIT(),
        method: 'post',
        data,
      });
    },
    // 删除空间成员
    async deleteSpacesMembers(data = {}) {
      return axios({
        url: API_SPACES_MEMBERS_DELETE(),
        method: 'post',
        data,
      });
    },
    // 转让空间负责人
    async transferSpacesMembers(data = {}) {
      return axios({
        url: API_SPACES_MEMBERS_TRANSFER(),
        method: 'post',
        data,
      });
    },
    // 检查告警名称是否被使用
    async checkSpaceAlarmName(params) {
      return axios({
        url: API_SPACES_ALARM_CHECK_NAME(),
        method: 'get',
        params,
        loading: false,
      });
    },
    // 添加用户组
    async addSpacesUserGroup(data = {}) {
      return axios({
        url: API_SPACES_USER_GROUP_OPERATE(data.spaceId),
        method: 'post',
        data,
      });
    },
    // 编辑用户组
    async editSpacesUserGroup(data = {}) {
      return axios({
        url: API_SPACES_USER_GROUP_OPERATE(data.spaceId, data.id),
        method: 'put',
        data,
      });
    },
    // 删除用户组
    async deleteSpacesUserGroup(data = {}, options = {}) {
      return axios({
        url: API_SPACES_USER_GROUP_OPERATE(data.spaceId, data.id),
        method: 'delete',
        ...options,
      });
    },
    // 查询用户组列表
    async getSpacesUserGroupList(spaceId, data = {}) {
      return axios({
        url: API_SPACES_USER_GROUP_LIST(spaceId),
        method: 'post',
        data,
        loading: false,
      });
    },

    // 工作流授权详情
    async getSpacesFlowAuthDetail(spaceId, permissionKey, params = {}) {
      return axios({
        url: API_SPACES_FLOW_AUTH_DETAIL(spaceId, permissionKey),
        method: 'get',
        params,
        loading: false,
      });
    },
    // Git授权详情
    async getSpacesGitAuthDetail(spaceId, permissionKey, params = {}) {
      return axios({
        url: API_SPACES_GIT_AUTH_DETAIL(spaceId, permissionKey),
        method: 'get',
        params,
        loading: false,
      });
    },
    // 工作流授权
    async setSpacesFlowAuth(data = {}) {
      return axios({
        url: API_SPACES_FLOW_AUTH(data.spaceId),
        method: 'post',
        data,
      });
    },
    // Git授权
    async setSpacesGitAuth(data = {}) {
      return axios({
        url: API_SPACES_GIT_AUTH(data.spaceId),
        method: 'post',
        data,
      });
    },
    // 权限迁移
    async privilegeMigration(data = {}) {
      return axios({
        url: API_SPACES_PRIVILEGE_MIGRATION(),
        method: 'post',
        data,
      });
    },
    // 获取当前空间的负责人组下的成员
    async getSpacesOwnerGroupMembers(spaceId) {
      return axios({
        url: API_SPACES_OWNER_GROUP_MEMBERS(spaceId),
        method: 'get',
        loading: false,
      });
    },
    // 判断用户组名称是否已使用
    async checkSpacesUserGroupName(spaceId, name) {
      return axios({
        url: API_SPACES_USER_GROUP_CHECK_NAME(spaceId, name),
        method: 'get',
        loading: false,
      });
    },
  },
  // 开启持久化配置
  persist: {
    key: 'spaces',
    pick: ['currentSpace'],
    storage: localStorage,
  },
});

if (import.meta.webpackHot) {
  import.meta.webpackHot.accept(acceptHMRUpdate(useSpacesStore, import.meta.webpackHot));
}
