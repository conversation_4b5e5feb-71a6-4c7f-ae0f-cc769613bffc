<script setup>
import { ref, reactive, defineExpose, defineEmits } from 'vue';
import BasicDialog from '@/components/BasicDialog.vue';
// import EllipsisWithTooltip from '@/components/EllipsisWithTooltip.vue';
import { uuid } from '@/utils/util';
import { useSpacesStore } from '@/stores/spaces';
import { debounce, cloneDeep } from 'lodash';
import { useRoute } from 'vue-router/composables';
import BlockHeader from '@/components/BlockHeader.vue';
import AlarmInfo from './AlarmInfo.vue';
import { MessagePlugin } from 'tdesign-vue';

const visible = ref(false);
const formRef = ref(null);
const emit = defineEmits(['ok']);
const route = useRoute();
const spaceStore = useSpacesStore();

const formData = reactive({
  id: '',
  // 工作流名称
  name: '',
  // 工作流标识
  code: '',
  // 数仓层
  warehouseLayer: '',
  // 业务
  businessType: '',
  // 描述
  description: '',
  // 告警渠道
  alertChannels: [],
  // 超时时长
  timeout: '',
  // 告警模板
  alertConfigId: [],
  // 执行策略
  executionType: '',
  // 全局变量
  globalParams: [],
});

const warehouseLayerOptions = ref([
  { label: 'ods', value: 'ods' },
  { label: 'dwd', value: 'dwd' },
  { label: 'dwm', value: 'dwm' },
  { label: 'dws', value: 'dws' },
  { label: 'ads', value: 'ads' },
  { label: 'dim', value: 'dim' },
]);

const executionTypeOptions = ref([
  { label: '并行', value: 'PARALLEL' },
  { label: '串行等待', value: 'SERIAL_WAIT' },
  { label: '串行抛弃', value: 'SERIAL_DISCARD' },
  { label: '串行优先', value: 'SERIAL_PRIORITY' },
]);

const rules = {
  name: [{ required: true, message: '请输入工作流名称' }],
  code: [{ required: true, message: '请输入工作流标识' }],
  timeout: [{ required: true, message: '请输入超时时长' }],
  alertConfigId: [{ required: true, message: '请选择告警模板' }],
  executionType: [{ required: true, message: '请选择执行策略' }],
};

function checkParam(item) {
  if (item.prop === '' || item.value === '') {
    return {
      result: false,
      message: '键值不能为空',
      type: 'error',
    };
  }
  const reg = /^[A-Za-z0-9_-]+$/;
  if (!reg.test(item.prop)) {
    return {
      result: false,
      message: '只允许包含英文字母、数字、下划线和中划线',
      type: 'error',
    };
  }

  return {
    result: true,
  };
}

function handleAddParam() {
  formData.globalParams.push({ id: uuid(), prop: '', value: '' });
}
function handleDeleteParam(index) {
  formData.globalParams.splice(index, 1);
}

function handleClose() {
  close();
}
function resetData() {
  formData.id = '';
  formData.name = '';
  formData.code = '';
  formData.warehouseLayer = '';
  formData.businessType = '';
  formData.description = '';
  formData.alertChannels = [];
  formData.timeout = '';
  formData.alertConfigId = [];
  formData.executionType = '';
  formData.globalParams = [];
}
function getSubData() {
  return {
    id: formData.id,
    name: formData.name,
    code: formData.code,
    warehouseLayer: formData.warehouseLayer,
    businessType: formData.businessType,
    description: formData.description,
    alertChannels: formData.alertChannels,
    timeout: formData.timeout,
    alertConfigId: formData.alertConfigId,
    executionType: formData.executionType,
    globalParams: formData.globalParams.map(item => ({ prop: item.prop, value: item.value })),
  };
}

async function checkProcessNameIsRepeat() {
  if (backupFormData.name === formData.name) {
    return false;
  }
  const { data } = await spaceStore.checkSpaceProcessName(route.query.spaceId, formData.name);
  if (data) {
    MessagePlugin.error('工作流名称重复');
  }
  return data;
}

function handleConfirm() {
  formRef.value.validate().then(async (result) => {
    if (result === true) {
      const isRepeat =  await checkProcessNameIsRepeat();
      if (isRepeat) return;
      emit('ok', getSubData());
    }
  });
}
let backupFormData = {};
function open(data) {
  visible.value = true;
  if (data) {
    formData.id = data.id;
    formData.name = data.name;
    formData.code = data.code;
    formData.warehouseLayer = data.warehouseLayer;
    formData.businessType = data.businessType;
    formData.description = data.description;
    formData.alertChannels = data.alertChannels;
    formData.timeout = data.timeout;
    formData.alertConfigId = data.alertConfigId;
    formData.executionType = data.executionType;
    formData.globalParams = data.globalParams.map(item => ({ id: uuid(), prop: item.prop, value: item.value }));

    backupFormData = cloneDeep(formData);
  }
  alarmRemoteMethod('');
}
function close() {
  visible.value = false;
  resetData();
  formRef.value.reset();
}

const alertOptions = [
  { label: '超时告警', value: 'TIMEOUT' },
  { label: '失败告警', value: 'FAILED' },
];

const alarmOptions = ref([]);
const alarmRemoteMethod = debounce((query) => {
  // if (!query) {
  //   return;
  // }
  spaceStore.getAlarmList({
    queryData: {
      queryKey: query,
      spaceId: route.query.spaceId,
      // alertType: alarmSearchData.alertType,
    },
    pageNum: 1,
    pageSize: 20,
  }).then((res) => {
    alarmOptions.value = res.data.list.map(item => ({
      label: item.name,
      value: item.id,
      ...item,
    }));
  });
}, 500);

function alterChannelsChange(val) {
  if (val.length === 0) {
    formData.timeout = '';
    formData.alertConfigId = [];
    return;
  }
  if (!val.includes('TIMEOUT')) {
    formData.timeout = '';
  }
  if (!val.includes('FAILED')) {
    formData.alertConfigId = [];
  }
}

defineExpose({ open, close });
</script>

<template>
  <BasicDialog
    width="560"
    :visible.sync="visible"
    header="提交工作流"
    @handleClose="handleClose"
    @handleConfirm="handleConfirm"
  >
    <t-form class="des-work-flow__save-work-flow" :data="formData" style="padding-right:12px;" ref="formRef" labelWidth="106px" :rules="rules">
      <section>
        <block-header title="基本信息" style="margin-bottom: 12px;" />
        <t-form-item label="工作流名称" name="name">
          <t-input v-model="formData.name" :maxlength="100" show-limit-number />
        </t-form-item>
        <t-form-item label="工作流标识" name="code">
          <t-input v-model="formData.code" :disabled="!!formData.id" :maxlength="100" show-limit-number />
        </t-form-item>
        <t-form-item label="数仓层" name="warehouseLayer">
          <t-select v-model="formData.warehouseLayer" :options="warehouseLayerOptions"></t-select>
        </t-form-item>
        <t-form-item label="业务" name="businessType">
          <t-input v-model="formData.businessType"></t-input>
        </t-form-item>
        <t-form-item label="描述" name="description">
          <t-textarea v-model="formData.description" :autosize="{ minRows: 3, maxRows: 5 }" :maxlength="200" placeholder="请输入描述内容"></t-textarea>
        </t-form-item>
        <t-form-item label="执行策略" name="executionType">
          <t-select v-model="formData.executionType" :options="executionTypeOptions"></t-select>
        </t-form-item>
        <section class="global-params-setting">
          <t-form-item
            v-for="(param, index) in formData.globalParams"
            :key="param.id"
            :label="`全局变量${index + 1}`"
            :name="`globalParams[${index}]`"
            :rules="[{required: true }, { validator: () => checkParam(param) }]">
            <div class="params-row">
              <t-input v-model.trim="param.prop" placeholder="键"></t-input>
              <t-input v-model.trim="param.value" placeholder="值"></t-input>
              <div class="delete-btn" @click="handleDeleteParam(index)">
                <des-icon name="des-icon-shanshu" size="14"></des-icon>
              </div>
            </div>
          </t-form-item>
          <div style="margin-bottom:29px;">
            <t-link hover="color" theme="primary" @click="handleAddParam"><des-icon name="des-icon-jiaxiao" size="14px"></des-icon> 添加全局变量</t-link>
          </div>
        </section>
      </section>
      <section>
        <block-header title="告警配置" style="margin-bottom: 12px;" />
        <t-form-item label="告警渠道">
          <t-select v-model="formData.alertChannels" :options="alertOptions" multiple @change="alterChannelsChange"></t-select>
        </t-form-item>
        <t-form-item label="超时时长" name="timeout" v-if="formData.alertChannels.includes('TIMEOUT')">
          <t-input-number v-model="formData.timeout" :min="0" theme="column" placeholder="请输入" style="width: 100%;">
            <template #suffix><span>分钟</span></template>
          </t-input-number>
        </t-form-item>
        <t-form-item label="告警模板" name="alertConfigId" v-if="formData.alertChannels.length > 0">
          <t-select
            v-model="formData.alertConfigId"
            :max="5"
            multiple
            clearable
            filterable
            :onSearch="alarmRemoteMethod"
            :options="alarmOptions"></t-select>
        </t-form-item>
        <template v-for="id in formData.alertConfigId">
          <AlarmInfo :alarmId="id" :key="id"></AlarmInfo>
        </template>
      </section>
    </t-form>
  </BasicDialog>
</template>

<style lang="less" scoped>
.des-work-flow__save-work-flow {
  .params-row {
      display: flex;
      gap: 12px;
      align-items: center;
      .delete-btn {
        width: 24px;
        height: 24px;
        cursor: pointer;
        flex-shrink: 0;
        &:hover {
          color: var(--des-color-theme);
        }
      }
    }
  .global-params-setting {
    margin-top: 24px;
    & > h3 {
      color: #333333;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px;
      margin-bottom: 8px;
    }
    :deep(.t-form__item) {
      padding: 8px 0;
      border-radius: 4px;
      background: #F4F4F4;
      .t-is-error {
        .t-input__extra {
          bottom: -28px;
        }
      }
    }
  }
  :deep(.alarm-template-info) {
    background: #f4f4f4;
  }
}
</style>
