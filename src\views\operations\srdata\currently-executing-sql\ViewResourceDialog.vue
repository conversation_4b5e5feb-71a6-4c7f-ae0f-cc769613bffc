<template>
  <BasicDialog
    width="560"
    :visible.sync="visible"
    :showConfirmButton="false"
    header="资源消耗查看"
    :onClosed="onClose"
    @handleClose="onClose"
  >
    <section class="section-timeline-wrap">
      <div class="timeline-wrap" v-for="(node, index) in nodes" :key="index">
        <ResourceNode
          :data="node"
          :actived="actived"
          :lastNode="index === nodes.length - 1"
          @click.native="handleClick(node)"
        ></ResourceNode>
      </div>
    </section>
    <section class="section-descriptions-wrap">
      <t-row :gutter="[0, 8]">
        <t-col class="des-flex-align-center" :span="6"><span class="label">扫描数据大小</span><span class="content">11.3kb</span></t-col>
        <t-col class="des-flex-align-center" :span="6"><span class="label">扫描数据行数</span><span class="content">45</span></t-col>
        <t-col class="des-flex-align-center" :span="6"><span class="label">CPU时间</span><span class="content">1h</span></t-col>
        <t-col class="des-flex-align-center" :span="6"><span class="label">内存使用量</span><span class="content">11.3kb</span></t-col>
      </t-row>
    </section>
  </BasicDialog>
</template>

<script setup>
import { defineExpose, ref } from 'vue';
import { useDialog } from '@/views/operations/hooks';
import { ResourceNode } from '@/views/operations/components';
import BasicDialog from '@/components/BasicDialog.vue';

const nodes = ref([
  {
    id: 1,
    label: '节点1',
  },
  {
    id: 2,
    label: '节点2',
  },
  {
    id: 3,
    label: '节点3',
  },
  {
    id: 4,
    label: '节点4',
  },
  {
    id: 5,
    label: '节点5',
  },
  {
    id: 6,
    label: '节点6',
  },
  {
    id: 7,
    label: '节点7',
  },
]);

const actived = ref(1);
const handleClick = ({ id }) => {
  actived.value = id;
  console.log('id:', id);
};

// 弹窗数据
const { visible, openAddDialog, openEditDialog, onClose } = useDialog();

defineExpose({ openAddDialog, openEditDialog });
</script>

<style lang="less" scoped>
.section-timeline-wrap {
  display: flex;
  overflow-x: auto;
  flex-wrap: nowrap;
  padding: 10px 0 20px 0;
  .timeline-wrap {
    flex-shrink: 0;
    display: flex;
  }
  .line {
    width: 78px;
    height: 1px;
    background-color: var(--des-color-theme);
    margin: 17px 4px;
  }
}
.section-descriptions-wrap {
  margin-top: 20px;
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  padding: 16px;
  background: #eee;
  border-radius: 4px;
  .label {
    width: 84px;
    display: inline-block;
    margin-right: 8px;
    color: #999999;
    text-align: left;
  }
  .content {
    display: inline-block;
    color: #333333;
  }
}
</style>
