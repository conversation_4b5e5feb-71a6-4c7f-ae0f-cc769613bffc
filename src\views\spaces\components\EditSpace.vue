<script setup>
import { ref, reactive, defineExpose, defineEmits } from 'vue';
import { storeToRefs } from 'pinia';
import BasicDialog from '@/components/BasicDialog.vue';
import { useSpacesStore } from '@/stores/spaces';
import { useOperationsStore } from '@/stores/operations';

const visible = ref(false);
const formRef = ref(null);
const emit = defineEmits(['update']);
const formData = reactive({
  id: '',
  name: '',
  code: '',
  orgArea: '',
  datasourceList: [],
  description: '',
});

const rules = {
  name: [{ required: true, message: '空间名称不能为空' }],
  orgArea: [{ required: true, message: '所属领域不能为空' }],
  description: [{ required: true, message: '空间描述不能为空' }],
};

const spaceStore = useSpacesStore();
spaceStore.getAllOrgAreas();
const { allOrgAreas } = storeToRefs(spaceStore);

const operationsStore = useOperationsStore();
operationsStore.fetchDatasourcesAllList();
const { allDataSources } = storeToRefs(operationsStore);

function handleClose() {
  close();
}

function getOrgAreaItem(id = '') {
  return allOrgAreas.value.find(item => item.uniqueId === id);
}
function getDataSourceItems(ids = []) {
  return allDataSources.value.filter(item => ids.includes(item.id));
}
async function handleConfirm() {
  const result = await formRef.value.validate();
  if (result === true) {
    console.log('表单校验通过');
    const subData = {
      id: formData.id,
      name: formData.name,
      code: formData.code,
      orgArea: getOrgAreaItem(formData.orgArea),
      datasourceList: getDataSourceItems(formData.datasourceList),
      description: formData.description,
    };
    await spaceStore.updateSpace(subData);
    close();
    emit('update');
  }
}

function open(data) {
  visible.value = true;
  if (data) {
    formData.id = data.id;
    formData.name = data.name;
    formData.code = data.code;
    formData.orgArea = data.orgArea.uniqueId;
    formData.description = data.description;
    formData.datasourceList = (data.datasourceList || []).map(item => item.id);
  }
}
function close() {
  visible.value = false;
  formData.id = '';
  formData.name = '';
  formData.code = '';
  formData.orgArea = '';
  formData.datasourceList = [];
  formData.description = '';
  formRef.value.reset();
}

defineExpose({ open, close });
</script>

<template>
  <BasicDialog
    width="640"
    :visible.sync="visible"
    header="编辑空间"
    @handleClose="handleClose"
    @handleConfirm="handleConfirm"
  >
    <t-form :data="formData" style="padding-right:12px;" ref="formRef" labelWidth="117px" :rules="rules">
      <t-form-item label="空间名称" name="name">
        <t-input v-model="formData.name" placeholder="请输入" :maxlength="20" show-limit-number></t-input>
      </t-form-item>
      <t-form-item label="所属领域" name="orgArea">
        <t-select v-model="formData.orgArea" placeholder="请选择">
          <t-option v-for="item in allOrgAreas" :key="item.uniqueId" :label="item.orgArea" :value="item.uniqueId"></t-option>
        </t-select>
      </t-form-item>
      <t-form-item label="SR数据源" name="datasourceList">
        <t-select v-model="formData.datasourceList" multiple placeholder="请选择">
          <t-option v-for="item in allDataSources" :key="item.id" :label="item.name" :value="item.id"></t-option>
        </t-select>
      </t-form-item>
      <t-form-item label="空间描述" name="description">
        <t-textarea  v-model="formData.description" placeholder="请简要描述空间的用途或适用范围，方便后续您或其他用户查看"  :maxlength="200" show-limit-number></t-textarea>
      </t-form-item>
    </t-form>
  </BasicDialog>
</template>

<style lang="less" scoped>
</style>
