<script setup>
import { ref, defineProps, defineEmits, computed } from 'vue';
import { debounce } from 'lodash';
import EllipsisWithTooltip from './EllipsisWithTooltip.vue';

const props = defineProps({
  value: {
    type: String,
    default: '',
  },
  onSearch: {
    type: Function,
    default: () => {},
  },
  disabled: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(['input']);
const desFilePathSelectRef = ref(null);
const visible = ref(false);
const options = ref([]);
const filterText = ref('');

const calcOptions = computed(() => {
  if (filterText.value) {
    return options.value.filter(item => item.includes(filterText.value));
  }
  return options.value;
});

function queryFile(query) {
  return props.onSearch(query).then((res) => {
    options.value = res.data || [];
  })
    .catch(() => {});
}
const handleSearch = debounce((query) => {
// 只有最后一个/时或者内容是空时才需要搜索
  if (query.endsWith('/') || query === '') {
    queryFile(query);
  }
}, 300);

function hanleChange(val) {
  handleSearch(val);
  emit('input', val);
}
function handleChoose(partPath) {
  const newPath = (props.value.endsWith('/') || props.value === '') ? `${props.value}${partPath}` : `${props.value}/${partPath}`;
  queryFile(newPath);
  emit('input', newPath);
  filterText.value = '';
}
function handleBack() {
  const pathArr = props.value.split('/');
  pathArr.pop();
  let newPath = pathArr.join('/');
  if (props.value.indexOf('/' === 0) && newPath === '') {
    newPath = '/';
  }
  queryFile(newPath);
  emit('input', newPath);
}
function handleFoucs() {
  visible.value = true;
  if (!props.value) {
    queryFile(props.value);
  }
}
function handleBlur() {
  visible.value = false;
}
function getAttach() {
  return desFilePathSelectRef.value;
}
</script>

<template>
  <div ref="desFilePathSelectRef" v-clickoutside="handleBlur">
    <t-popup
      placement="bottom"
      :visible="visible"
      :disabled="disabled"
      :overlayInnerStyle="(triggerElem) => ({ width: `${triggerElem.offsetWidth}px` })"
      :attach="getAttach">
      <t-input :value="value" @change="hanleChange" @focus="handleFoucs" :disabled="disabled"></t-input>
      <template #content>
        <ul class="des-file-path-select__list">
          <li style="padding: 0 4px;">
            <t-input v-model="filterText" size="small" clearable placeholder="请输入搜索"></t-input>
          </li>
          <li v-if="value && value !== '/'" @click="handleBack">返回上一级</li>
          <li v-for="partPath in calcOptions" :key="partPath" @click="handleChoose(partPath)">
            <ellipsis-with-tooltip :text="`${partPath}`"></ellipsis-with-tooltip>
          </li>
        </ul>
      </template>
    </t-popup>
  </div>
</template>

<style lang="less">
.des-file-path-select__list {
  padding: var(--td-pop-padding-m);
  max-height: 300px;
  overflow: auto;
  li {
    display: flex;
    align-items: center;
    border-radius: var(--td-radius-default);
    height: var(--td-comp-size-s);
    font: var(--td-font-body-medium);
    cursor: pointer;
    padding: 0 var(--td-comp-paddingLR-s);
    color: var(--td-text-color-primary);
    transition: background-color .2s cubic-bezier(.38,0,.24,1);
    box-sizing: border-box;
    &:hover {
      background-color: var(--td-bg-color-container-hover);
    }
  }
}
</style>
