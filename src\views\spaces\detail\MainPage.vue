<script setup>
import { ref, computed } from 'vue';
import { storeToRefs } from 'pinia';
import { useRouter, useRoute } from 'vue-router/composables';
import dayjs from 'dayjs';
import PageLayout from '@/components/PageLayout.vue';
import EllipsisWithTooltip from '@/components/EllipsisWithTooltip.vue';
import UserName from '@/components/UserName.vue';
import EditSpace from '../components/EditSpace.vue';
import BlockMenber from '../components/BlockMenber.vue';
import BlockAlarm from '../components/BlockAlarm.vue';
import { useSpacesStore } from '@/stores/spaces';
import { useCommonStore } from '@/stores/common';

const router = useRouter();
const route = useRoute();
const spacesStore = useSpacesStore();
const { allOrgAreas } = storeToRefs(spacesStore);
const commonStore = useCommonStore();
const { operateCodeList, staffId } = storeToRefs(commonStore);

const isEdit = computed(() => route.params.type === 'edit');
const spaceDetail = ref({});

function getAreaName(id) {
  return allOrgAreas.value.find(item => `${item.uniqueId}` === id)?.orgArea;
}

initData();
// 初始化数据
async function initData() {
  const res = await spacesStore.getSpaceDetail(route.params.id);
  spaceDetail.value = res.data;
}
const datasourceListName = computed(() => (spaceDetail.value.datasourceList || []).map(item => item.name).join('、') || '-');
function handleBack() {
  router.push({
    name: 'spaces',
  });
}
// 空间
// 编辑空间
const editSpaceRef = ref(null);
function handleEditSpace() {
  editSpaceRef.value.open(spaceDetail.value);
}

const title = computed(() => (isEdit.value ? '管理空间' : '查看空间'));

const showButton = computed(() => {
  // 编辑状态处理
  if (isEdit.value) {
    if (operateCodeList.value.includes('Menu_spaceCreate') && operateCodeList.value.includes('Menu_spaceEdit') && operateCodeList.value.includes('Menu_spaceDel')) {
      return true;
    }
    if (spaceDetail.value.manageRoleList?.some(item => item.staffId === staffId.value)) {
      return true;
    }
  }
  return false;
});
</script>

<template>
  <page-layout class="page-space-detail" :title="title" :back="handleBack">
    <section class="page-space-detail__info">
      <div v-auth="'Menu_spaceEdit'" v-if="isEdit" class="edit-btn" @click="handleEditSpace">
        <des-icon name="des-icon-bianjixiugai" size="16"></des-icon>
        <span>编辑</span>
      </div>
      <des-rect-icon class="page-space-detail__info-icon" name="des-icon-kongjianfuzerenbiaoshi" iconSize="32" size="48"></des-rect-icon>
      <div class="page-space-detail__info-main">
        <div class="page-space-detail__info-simple">
          <h2>{{ spaceDetail.name }}</h2>
          <p>
            <ellipsis-with-tooltip :text="spaceDetail.description"></ellipsis-with-tooltip>
          </p>
        </div>
        <div class="page-space-detail__info-detail">
          <t-row :gutter="[20, 8]">
            <t-col class="des-flex-align-center" :span="4"><span class="label">空间标识</span><span class="content">{{ spaceDetail.code }}</span></t-col>
            <t-col class="des-flex-align-center" :span="4"><span class="label">所属领域</span><span class="content">{{ getAreaName(spaceDetail.orgArea?.uniqueId) }}</span></t-col>
            <t-col class="des-flex-align-center" :span="4"><span class="label">数据源</span><ellipsis-with-tooltip class="content" :text="datasourceListName"></ellipsis-with-tooltip></t-col>
            <t-col class="des-flex-align-center" :span="4"><span class="label">创建人</span><span class="content"><user-name :full-name="spaceDetail.createUser"></user-name></span></t-col>
            <t-col class="des-flex-align-center" :span="4"><span class="label">创建时间</span><span class="content">{{ dayjs(spaceDetail.createTime).format('YYYY-MM-DD HH:mm:ss') }}</span></t-col>
          </t-row>
        </div>
      </div>
    </section>
    <block-menber style="margin-bottom: 8px;" :spaceId="route.params.id" :spaceName="spaceDetail.name" :readonly="!isEdit" :showButton="showButton"></block-menber>
    <!-- 告警管理 -->
    <block-alarm :spaceId="route.params.id" :readonly="!isEdit" :showButton="showButton"></block-alarm>

    <edit-space ref="editSpaceRef" @update="initData"></edit-space>
  </page-layout>
</template>

<style lang="less" scoped>
.page-space-detail {
  section {
    background-color: #fff;
    border-radius: 4px;
    margin-bottom: 8px;
  }
  h2 {
    color: #333333;
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
  }
}
.page-space-detail__info {
  display: flex;
  padding: 16px 20px;
  position: relative;
  .edit-btn {
    position: absolute;
    right: 20px;
    top: 16px;
    display: flex;
    align-items: center;
    color: #3464e0;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    cursor: pointer;
    span {
      margin-left: 2px;
    }
  }
}
.page-space-detail__info-icon {
  margin-right: 8px;
  flex-shrink: 0;
}
.page-space-detail__info-main {
  flex-grow: 1;
  overflow: hidden;
}
.page-space-detail__info-simple {
  p {
    color: #666666;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    margin-bottom: 16px;
  }
}
.page-space-detail__info-detail {
  .label {
    display: inline-block;
    width: 70px;
    margin-right: 8px;
    color: #999999;
    text-align: right;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
  }
  .content {
    display: inline-block;
    color: #333333;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    width: calc(~'100% - 78px');
  }
}
:deep(.page-space-detail__tag) {
  display: flex;
  height: 22px;
  padding: 0 4px;
  align-items: center;
  border-radius: 3px;
  background: #EFF3FD;
  color: #3464e0;
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
}
</style>
