<template>
  <div class="srdata">
    <t-tabs v-model="actived">
      <t-tab-panel
        v-for="data in tabPanelList"
        :key="data.value"
        :value="data.value"
        :label="data.label"
        lazy
      >
        <component :is="getComp(data.value)" :datasourceOptions="datasourceOptions" />
      </t-tab-panel>
    </t-tabs>
  </div>
</template>

<script setup>
import { ref, computed, watchEffect, onMounted } from 'vue';
import { storeToRefs } from 'pinia';
import { useCommonStore } from '@/stores/common';
import { useOperationsStore } from '@/stores/operations';
import { to } from '@/utils/util';
import BlacklistSql from './blacklist-sql/MainPage.vue';
import CurrentlyExecutingSql from './currently-executing-sql/MainPage.vue';
import DynamicParameter from './dynamic-parameter/MainPage.vue';

const commonStore = useCommonStore();
const { operateCodeList } = storeToRefs(commonStore);
const operationsStore = useOperationsStore();

const tabPanelList = computed(() => [
  { label: '黑名单SQL管理', value: 'BlacklistSql', auth: 'Menu_blacklistSQL' },
  { label: '当前执行SQL管理', value: 'CurrentlyExecutingSql', auth: 'Menu_execSql' },
  { label: '动态参数展示与修改', value: 'DynamicParameter', auth: 'Menu_dynamicParameter' },
].filter(item => operateCodeList.value.includes(item.auth)));

const actived = ref('');

watchEffect(() => {
  const tabs = tabPanelList.value.map(item => item.value);
  if (!tabs.includes(actived.value)) {
    // eslint-disable-next-line prefer-destructuring
    actived.value = tabs[0];
  }
});

// 组件映射
function getComp(name) {
  let comp = null;
  switch (name) {
    case 'BlacklistSql':
      comp = BlacklistSql;
      break;
    case 'CurrentlyExecutingSql':
      comp = CurrentlyExecutingSql;
      break;
    case 'DynamicParameter':
      comp = DynamicParameter;
      break;
    default:
      break;
  }
  return comp;
}

const datasourceOptions = ref([]);
const getDataSourceOptions = async () => {
  const data = { pageNum: 1, pageSize: 100, queryData: { queryKey: '' } };
  const [err, result] = await to(operationsStore.fetchDatasourcesList(data));
  if (err) {
    return;
  }
  datasourceOptions.value = result.list.map(item => ({
    ...item,
    label: item.name,
    value: item.id,
  }));
};

onMounted(() => {
  getDataSourceOptions();
});
</script>

<style lang="less" scoped>
.srdata {
  height: 100%;
  padding: 8px 20px;
  background-color: #fff;
  :deep(.t-tabs) {
    height: 100%;
    .t-tabs__content {
      height: calc(100% - 48px);
      .t-tab-panel {
        height: 100%;
      }
    }
  }
}
</style>
