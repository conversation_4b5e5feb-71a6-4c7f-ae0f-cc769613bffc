<script setup>
import { reactive, ref, defineExpose, watch, defineProps } from 'vue';
import BlockHeader from '@/components/BlockHeader.vue';
import BasicConfig from './common/BasicConfig.vue';
import ResourceConfig from './common/ResourceConfig.vue';
import AlarmConfig from './common/AlarmConfig.vue';
import PreTask from './common/PreTask.vue';
import { STARROCKS_MATERIALIZED_VIEW } from '../../utils/task-type.js';
import { srMaterializedViewRules } from '../../utils/rules.js';
import { useOperationsStore } from '@/stores/operations';
import { useRoute } from 'vue-router/composables';

defineProps({
  currentFlowName: {
    type: String,
    default: '',
  },
  readonly: {
    type: Boolean,
    default: false,
  },
});

const formRef = ref(null);
const route = useRoute();
const formData = reactive({
  taskType: STARROCKS_MATERIALIZED_VIEW,
  id: '',
  // 节点名称
  name: '',
  // 节点标识
  code: '',
  // 节点描述
  description: '',
  // 失败重试次数
  failRetryTimes: 0,
  // 失败重试间隔
  failRetryInterval: 0,
  // 延迟执行
  delayTime: 0,
  // 自定义参数
  // params: [
  //   { key: '1', name: '参数1', relation: 'in', type: 'string', parameter: 'value' },
  // ],
  // 运行集群
  resourceGroupId: '',
  // cpu
  minCpuCores: 0.5,
  // 内存
  minMemorySpace: 512,
  // 超时告警开关
  timeoutFlag: false,
  // 超时时长
  timeout: 0,
  // 超时是否失败
  timeoutFailed: false,
  taskProperties: {
    // starrocks集群
    srDataSourceId: '',
    // 物化视图
    materializedViewName: '',
  },
});
const nodeId = ref('');

// function resetData() {
//   formData.taskType = STARROCKS_MATERIALIZED_VIEW;
//   formData.name = '';
//   formData.description = '';
//   formData.failRetryTimes = 0;
//   formData.failRetryInterval = 0;
//   formData.delayTime = 0;
//   formData.resourceGroupId = '';
//   formData.minCpuCores = 1;
//   formData.minMemorySpace = 512;
//   formData.timeoutFlag = false;
//   formData.timeout = 0;
//   formData.timeoutFailed = false;
//   formData.taskProperties = {
//     srDataSourceId: '',
//     materializedViewName: '',
//   };
//   nodeId.value = '';
// }
function getData() {
  return {
    taskType: formData.taskType,
    id: formData.id,
    name: formData.name,
    code: formData.code,
    description: formData.description,
    failRetryTimes: formData.failRetryTimes,
    failRetryInterval: formData.failRetryInterval,
    delayTime: formData.delayTime,
    resourceGroupId: formData.resourceGroupId,
    minCpuCores: formData.minCpuCores,
    minMemorySpace: formData.minMemorySpace,
    timeoutFlag: formData.timeoutFlag,
    timeout: formData.timeout,
    timeoutFailed: formData.timeoutFailed,
    taskProperties: {
      srDataSourceId: formData.taskProperties.srDataSourceId,
      materializedViewName: formData.taskProperties.materializedViewName,
    },
  };
}

const operationsStore = useOperationsStore();
// 集群
const srDataSourceOptions = ref([]);
async function initSrDataSourceOptions() {
  const res = await operationsStore.fetchDatasourcesBySpaceId({ spaceId: route.query.spaceId });
  srDataSourceOptions.value = res.data.map(item => ({
    label: item.name,
    value: item.id,
  }));
}
watch(() => route.query.spaceId, () => {
  initSrDataSourceOptions();
}, { immediate: true });

async function save() {
  const result = await formRef.value.validate();
  if (result !== true) {
    return;
  }
  const data = getData();
  return {
    nodeId: nodeId.value,
    data,
  };
}
// function reset() {
//   resetData();
//   formRef.value.clearValidate();
// }
function setData(nodeid, data) {
  nodeId.value = nodeid;
  Object.keys(data).forEach((key) => {
    formData[key] = data[key];
  });
}
defineExpose({
  setData,
  // reset,
  save,
});
</script>

<template>
  <div class="des-workflow-job-form__starrocks-materialized-view">
    <t-form :data="formData" ref="formRef" label-width="117px" :rules="srMaterializedViewRules" :disabled="readonly">
      <basic-config :formData="formData"></basic-config>
      <resource-config :formData="formData"></resource-config>
      <alarm-config :formData="formData"></alarm-config>
      <section>
        <block-header title="更多设置" style="margin-bottom: 12px;" />
        <t-form-item label="Starrocks集群" name="taskProperties.srDataSourceId">
          <t-select v-model="formData.taskProperties.srDataSourceId" placeholder="请选择" style="width: 374px;" :options="srDataSourceOptions"></t-select>
        </t-form-item>
        <t-form-item label="物化视图" name="taskProperties.materializedViewName">
          <t-input v-model="formData.taskProperties.materializedViewName" placeholder="请选择" style="width: 374px;"></t-input>
        </t-form-item>
      </section>
      <section>
        <block-header title="任务关联" style="margin-bottom: 12px;" />
        <!-- 前置任务 -->
        <pre-task :currentId="formData._id" :currentFlowName="currentFlowName"></pre-task>
      </section>
    </t-form>
  </div>
</template>
