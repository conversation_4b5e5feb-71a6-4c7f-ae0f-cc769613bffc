<script setup>
import { ref, reactive, computed, defineExpose, defineEmits } from 'vue';
import { storeToRefs } from 'pinia';
import { MessagePlugin } from 'tdesign-vue';
import BlockHeader from '@/components/BlockHeader.vue';
import BasicDialog from '@/components/BasicDialog.vue';
import { useSpacesStore } from '@/stores/spaces';
import { useOperationsStore } from '@/stores/operations';

const emit = defineEmits(['success']);

const visible = ref(false);
const formRef = ref(null);
const manageStaffRef = ref(null);
// const techStaffRef = ref(null);
const formData = reactive({
  name: '',
  code: '',
  orgArea: '',
  datasourceList: [],
  description: '',
  manageRoleList: [],
  techRoleList: [],
});
const rules = {
  name: [{ required: true, message: '空间名称不能为空' }],
  code: [{ required: true, message: '空间标识不能为空' }, { validator: checkCode, trigger: 'change' }],
  orgArea: [{ required: true, message: '所属领域不能为空' }],
  description: [{ required: true, message: '空间描述不能为空' }],
  manageRoleList: [{ required: true, message: '空间负责组成员不能为空' }],
  // techRoleList: [{ required: true, message: '数据开发人员不能为空' }],
};

async function checkCode(val) {
  if (!val) {
    return {
      result: false,
      message: '空间标识不能为空',
      type: 'error',
    };
  }
  const { data } = await spaceStore.checkSpaceCode(val);
  if (data) {
    return {
      result: false,
      message: '空间标识已存在',
      type: 'error',
    };
  }
  return true;
}

const spaceStore = useSpacesStore();
spaceStore.getAllOrgAreas();
const { allOrgAreas } = storeToRefs(spaceStore);

const operationsStore = useOperationsStore();
operationsStore.fetchDatasourcesAllList();
const { allDataSources } = storeToRefs(operationsStore);

const selectedManageRole = ref([]);
const selectedTechRole = ref([]);
function handleChangeManageRole(list) {
  selectedManageRole.value = list;
}

// function handleChangeTechRole(list) {
//   selectedTechRole.value = list;
// }

function getManageRoleItems() {
  return selectedManageRole.value.map(item => ({
    staffId: item.StaffID,
    engName: item.EngName,
    staffName: item.StaffName,
  }));
}

function getTechRoleItems() {
  return selectedTechRole.value.map(item => ({
    staffId: item.StaffID,
    engName: item.EngName,
    staffName: item.StaffName,
  }));
}
function getOrgAreaItem(id = '') {
  return allOrgAreas.value.find(item => item.uniqueId === id);
}

function getSubData() {
  return {
    name: formData.name,
    code: formData.code,
    orgArea: getOrgAreaItem(formData.orgArea),
    datasourceList: formData.datasourceList.map(id => ({ id })),
    description: formData.description,
    manageRoleList: getManageRoleItems(),
    techRoleList: getTechRoleItems(),
  };
}

function handleConfirm() {
  formRef.value.validate().then((result) => {
    if (result === true) {
      const subData = getSubData();
      spaceStore.addSpace(subData).then(() => {
        // MessagePlugin.success('提交成功，请耐心等待工作人员审核');
        MessagePlugin.success('新建成功');
        close();
        emit('success');
      });
    }
  });
}

function handleClose() {
  close();
}

function open() {
  visible.value = true;
}
function close() {
  visible.value = false;
  manageStaffRef.value.clearSelected();
  // techStaffRef.value.clearSelected();
  formRef.value.reset();
  // formRef.value.clearValidate();
}

const allowSub = computed(() => formData.name && formData.code && formData.orgArea && formData.description && formData.manageRoleList.length);

defineExpose({ open, close });
</script>

<template>
  <BasicDialog
    width="640"
    :visible.sync="visible"
    header="新建空间"
    @handleClose="handleClose"
    @handleConfirm="handleConfirm"
    :submitDisabled="!allowSub"
  >
  <t-form :data="formData" style="padding-right:12px;" ref="formRef" labelWidth="117px" :rules="rules">
    <section>
      <block-header title="基本信息" style="margin-bottom: 12px;" />
      <t-form-item label="空间名称" name="name">
        <t-input v-model="formData.name" placeholder="请输入" :maxlength="20" show-limit-number></t-input>
      </t-form-item>
      <t-form-item label="空间标识" name="code">
        <t-input v-model="formData.code" placeholder="请填写"></t-input>
      </t-form-item>
      <t-form-item label="所属领域" name="orgArea">
        <t-select v-model="formData.orgArea" placeholder="请选择">
          <t-option v-for="item in allOrgAreas" :key="item.uniqueId" :label="item.orgArea" :value="item.uniqueId"></t-option>
        </t-select>
      </t-form-item>
      <t-form-item label="SR数据源" name="datasourceList">
        <t-select v-model="formData.datasourceList" placeholder="请选择" multiple>
          <t-option v-for="item in allDataSources" :key="item.id" :label="item.name" :value="item.id"></t-option>
        </t-select>
      </t-form-item>
      <t-form-item label="空间描述" name="description">
        <t-textarea  v-model="formData.description" placeholder="请简要描述空间的用途或适用范围，方便后续您或其他用户查看"  :maxlength="200" show-limit-number></t-textarea>
      </t-form-item>
    </section>
    <section>
      <block-header title="空间成员" style="margin-bottom: 12px;" />
      <t-form-item label="空间负责组成员" name="manageRoleList">
        <sdc-staff-selector v-model="formData.manageRoleList" ref="manageStaffRef" @change="handleChangeManageRole" multiple style="width:100%;" placeholder="请选择人员" modalClass="des-sdc-modal--fix" size="small"></sdc-staff-selector>
      </t-form-item>
      <!-- <t-form-item label="数据开发人员" name="techRoleList">
        <sdc-staff-selector v-model="formData.techRoleList" ref="techStaffRef" @change="handleChangeTechRole" multiple style="width:100%;" placeholder="请选择人员" modalClass="des-sdc-modal--fix" size="small"></sdc-staff-selector>
      </t-form-item> -->
    </section>
  </t-form>
  </BasicDialog>
</template>

<style lang="less" scoped>
</style>
