<script setup>
import { reactive, ref, defineExpose, defineProps, onMounted, defineEmits } from 'vue';
import BlockHeader from '@/components/BlockHeader.vue';
import BasicConfig from './common/BasicConfig.vue';
import ResourceConfig from './common/ResourceConfig.vue';
import AlarmConfig from './common/AlarmConfig.vue';
import PreTask from './common/PreTask.vue';
import { SUBPROCESS } from '../../utils/task-type.js';
import { subProcessRules } from '../../utils/rules.js';
import { useDataDevelopmentStore } from '@/stores/data-development';
import { debounce } from 'lodash';
import { useRoute } from 'vue-router/composables';

const emit = defineEmits(['updateProps']);

const props = defineProps({
  currentFlowName: {
    type: String,
    default: '',
  },
  nodeList: {
    type: Array,
    default: () => [],
  },
  readonly: {
    type: Boolean,
    default: false,
  },
});
const dataDevelopmentStore = useDataDevelopmentStore();
const formRef = ref(null);
const route = useRoute();
const formData = reactive({
  taskType: SUBPROCESS,
  id: '',
  // 节点名称
  name: '',
  // 节点标识
  code: '',
  // 节点描述
  description: '',
  // 失败重试次数
  failRetryTimes: 0,
  // 失败重试间隔
  failRetryInterval: 0,
  // 延迟执行
  delayTime: 0,
  // 自定义参数
  // params: [
  //   { key: '1', name: '参数1', relation: 'in', type: 'string', parameter: 'value' },
  // ],
  // 运行集群
  resourceGroupId: '',
  // cpu
  minCpuCores: 0.5,
  // 内存
  minMemorySpace: 512,
  // 超时告警开关
  timeoutFlag: false,
  // 超时时长
  timeout: 0,
  // 超时是否失败
  timeoutFailed: false,
  taskProperties: {
    // 子节点
    processDefinitionName: '',
  },
});
const nodeId = ref('');
const processOptions = ref([]);
const loading = ref(false);

function getData() {
  return {
    taskType: formData.taskType,
    id: formData.id,
    name: formData.name,
    code: formData.code,
    description: formData.description,
    failRetryTimes: formData.failRetryTimes,
    failRetryInterval: formData.failRetryInterval,
    delayTime: formData.delayTime,
    resourceGroupId: formData.resourceGroupId,
    minCpuCores: formData.minCpuCores,
    minMemorySpace: formData.minMemorySpace,
    timeoutFlag: formData.timeoutFlag,
    timeout: formData.timeout,
    timeoutFailed: formData.timeoutFailed,
    taskProperties: {
      processDefinitionName: formData.taskProperties.processDefinitionName,
    },
  };
}

const remoteMethod = debounce((query) => {
  loading.value = true;
  dataDevelopmentStore.fetchFlowDefineList(route.query.spaceId, {
    queryData: {
      spaceId: route.query.spaceId,
      searchType: 'PER_DRAW',
      queryKey: query,
      releaseState: null,
      scheduleState: null,
    },
    pageNum: 1,
    pageSize: 20,
  }, { loading: false }).then((res) => {
    processOptions.value = res.data.list.map(item => ({
      label: item.code,
      value: item.code,
      disabled: props.currentFlowName === item.name,
    }));
  })
    .finally(() => {
      loading.value = false;
    });
}, 500);
async function save() {
  const result = await formRef.value.validate();
  if (result !== true) {
    return;
  }
  const data = getData();
  return {
    nodeId: nodeId.value,
    data,
  };
}
// function reset() {
//   formRef.value.reset();
// }
function setData(nodeid, data) {
  nodeId.value = nodeid;
  Object.keys(data).forEach((key) => {
    formData[key] = data[key];
  });
}

function handleChange(val) {
  emit('updateProps', val);
}

// 获取子节点的所有数据
async function getSubFlowData() {
  const code = formData.taskProperties.processDefinitionName;
  const { data } = await dataDevelopmentStore.fetchFlowDefineDetail(route.query.spaceId, code);
  return {
    id: data.id,
    name: data.name,
    code: data.code,
    publishState: data.publishState,
    permissionList: data.permissionList,
  };
}

onMounted(() => {
  remoteMethod();
});

defineExpose({
  setData,
  // reset,
  save,
  getSubFlowData,
});
</script>

<template>
  <div class="des-workflow-job-form__starrocks-materialized-view">
    <t-form :data="formData" ref="formRef" label-width="117px" :rules="subProcessRules" :disabled="readonly">
      <basic-config :formData="formData"></basic-config>
      <resource-config :formData="formData"></resource-config>
      <alarm-config :formData="formData"></alarm-config>
      <section>
        <!-- 这个子任务应该属于“更多” -->
        <block-header title="任务关联" style="margin-bottom: 12px;" />
        <t-form-item label="子节点" name="taskProperties.processDefinitionName">
          <t-select
            v-model="formData.taskProperties.processDefinitionName"
            placeholder="请输入关键字"
            filterable
            :onSearch="remoteMethod"
            :loading="loading"
            :options="processOptions"
            @change="handleChange"
            style="width: 374px;"></t-select>
        </t-form-item>
        <!-- 前置任务 -->
        <pre-task :currentId="formData._id" :currentFlowName="currentFlowName"></pre-task>
      </section>
    </t-form>
  </div>
</template>
