<template>
  <BasicDialog
    width="560"
    :visible.sync="visible"
    :header="header"
    :submitDisabled="submitDisabled"
    :onClosed="onClose"
    @handleClose="onClose"
    @handleConfirm="onConfirm"
  >
    <t-form ref="formRef" :data="form" :rules="rules" label-width="107px">
      <t-form-item label="SR数据源名称" name="name">
        <t-input v-model="form.name" placeholder="请输入" @change="() => changeFlag()"/>
      </t-form-item>
      <t-form-item label="IP主机名" name="ip">
        <t-input v-model="form.ip" placeholder="请输入" @change="() => changeFlag()"/>
      </t-form-item>
      <t-form-item label="端口" name="port">
        <t-input v-model="form.port" placeholder="请输入" @change="() => changeFlag()"/>
      </t-form-item>
      <t-form-item label="用户名" name="userName">
        <t-input v-model="form.userName" placeholder="请输入" @change="() => changeFlag()"/>
      </t-form-item>
      <t-form-item label="密码" name="passWord">
        <PasswrodInput v-model="form.passWord" :mode="mode" :inputing="inputing" @updateInputing="setInputing(true)" @change="() => changeFlag()"></PasswrodInput>
      </t-form-item>
      <t-form-item label="数据库名" name="databaseName">
        <t-input v-model="form.databaseName" placeholder="请输入" @change="() => changeFlag()"/>
      </t-form-item>
      <t-form-item label="jdbc连接参数" name="connectionParams">
        <t-input v-model="form.connectionParams" placeholder="请输入" />
      </t-form-item>
      <t-form-item label="初始化脚本" name="initScript">
        <t-textarea
          v-model="form.initScript"
          placeholder="请输入"
          :autosize="{ minRows: 4, maxRows: 4 }"
        />
      </t-form-item>
    </t-form>

    <template #footer-left>
      <t-link theme="primary" hover="color" @click="handleTest">测试连通性</t-link>
    </template>
  </BasicDialog>
</template>

<script setup>
import BasicDialog from '@/components/BasicDialog.vue';
import PasswrodInput from '@/components/PasswrodInput.vue';
import { useForm, usePassword } from '@/hooks';
import { cloneDeep, omit } from 'lodash';
import { MessagePlugin } from 'tdesign-vue';
import { to } from '@/utils/util';
import { useDialog } from '@/views/operations/hooks';
import { useOperationsStore } from '@/stores/operations';
import { defineExpose, computed, defineEmits } from 'vue';

const emit = defineEmits(['fetchData']);
const { fetchDatasourcesTest, fetchDatasourcesAdd, fetchDatasourcesUpdate } = useOperationsStore();

const { inputing, setInputing } = usePassword();

// 测试联通标识
let flag = false;
const changeFlag = (val = false) => {
  flag = val;
};

const initFormData = {
  name: '',
  ip: '',
  port: '',
  userName: '',
  passWord: '',
  databaseName: '',
  connectionParams: '',
  initScript: '',
};

const checkPassWord = (val) => {
  if ((mode.value === 'ADD' || inputing.value) && !val) {
    return {
      result: false,
      message: '请输入',
      type: 'error',
    };
  }
  return true;
};

const getRule = (array = []) => [
  {
    required: true,
    message: '必填',
    type: 'error',
    trigger: 'change',
  },
  {
    whitespace: true,
    message: '不能为空',
  },
  ...array,
];
const initFormRules = {
  name: getRule(),
  ip: getRule(),
  port: getRule(),
  databaseName: getRule(),
  userName: getRule(),
  passWord: [
    { whitespace: true, message: '不能为空' },
    { validator: checkPassWord, trigger: 'change' },
  ],
};

// 表单数据
const { form, rules, formRef, validateForm, resetForm } = useForm(
  initFormData,
  initFormRules,
);

// 提交按钮禁用状态
const submitDisabled = computed(() => {
  let flag = form.name && form.ip && form.port && form.userName && form.databaseName;
  if (mode.value === 'ADD' || inputing.value) {
    flag = flag && form.passWord;
  }
  return !flag;
});

// 弹窗标题
const header = computed(() => (mode.value === 'ADD' ? '新增SR数据源' : '编辑SR数据源'));

const editCb = (data) => {
  Object.assign(form, cloneDeep(data));
};

const closeCb = () => {
  setInputing(false);
  resetForm();
};

const getPostData = () => {
  if (form.connectionParams) return { ...form, clazz: 1, type: 'starrocks' };
  return { ...omit(form, ['connectionParams']), clazz: 1, type: 'starrocks' };
};

const confirmCb = async () => {
  const valid = await validateForm();
  if (!valid) return;
  if (!flag) {
    MessagePlugin('error', '请测试连通性');
    return;
  }
  const data = getPostData();
  const api = mode.value === 'ADD' ? fetchDatasourcesAdd : fetchDatasourcesUpdate;
  const [err] = await to(api(data));
  if (err) {
    return;
  }
  emit('fetchData');
  MessagePlugin('success', '操作成功');
  return true;
};

// 弹窗数据
const { mode, visible, openAddDialog, openEditDialog, onConfirm, onClose } = useDialog({
  editCb,
  confirmCb,
  closeCb,
});

const handleTest = async () => {
  const valid = await validateForm();
  if (!valid) return;
  const data = getPostData();
  const [err] = await to(fetchDatasourcesTest(data));
  if (err) {
    return;
  }
  changeFlag(true);
  MessagePlugin('success', '测试连接成功');
};

defineExpose({ openAddDialog, openEditDialog });
</script>

<style lang="less" scoped>

</style>
