<script setup>
import { useRoute } from 'vue-router/composables';
import { ref, defineExpose, defineEmits } from 'vue';
import BasicDialog from '@/components/BasicDialog.vue';
import { useDataDevelopmentStore } from '@/stores/data-development';

const route = useRoute();

const emit = defineEmits(['refresh']);

const dataDevelopmentStore = useDataDevelopmentStore();

const visible = ref(false);
// 批次id
const batchId = ref('');

const tableData = ref([]);

const loading = ref(false);

let timerId = null;

function statusCell(state) {
  const stateMap = {
    FAILED: '失败',
    SUCCESS: '成功',
    PUBLISHING: '发布中',
  };
  const colorMap = {
    SUCCESS: 'des-green-dot',
    FAILED: 'des-red-dot',
    PUBLISHING: 'des-blue-dot',
  };
  const text = stateMap[state];
  const className = colorMap[state];
  if (text) {
    return <div class={className}>{text}</div>;
  }
  return <div>-</div>;
}

const columns = ref([
  { colKey: 'serial-number', title: '序号', width: 60 },
  { colKey: 'processName', title: '工作流名称', width: 177, ellipsis: true },
  { colKey: 'status', title: '状态', width: 105, cell: (h, { row }) => statusCell(row.status) },
  { colKey: 'duration', title: '用时', width: 105, ellipsis: true, cell: (h, { row }) => (row.duration ? `${(row.duration / 1000).toFixed(1)}s` : '-') },
  { colKey: 'completeTime', title: '完成时间', width: 170, ellipsis: true, cell: (h, { row }) => row.completeTime || '-' },
  { colKey: 'errMsg', title: '错误信息', width: 200, ellipsis: true, cell: (h, { row }) => row.errMsg || '-' },
]);

function handleClose() {
  close();
  clearInterval(timerId);
  if (tableData.value.length && tableData.value.every(item => item.status !== 'PUBLISHING')) {
    emit('refresh');
  }
}
function close() {
  visible.value = false;
}
function open(id) {
  visible.value = true;
  batchId.value = id;
  fetchData();
  timerId = setInterval(() => {
    if (tableData.value.length && tableData.value.every(item => item.status !== 'PUBLISHING')) {
      clearInterval(timerId);
      return;
    }
    fetchData();
  }, 3000);
}
async function fetchData() {
  loading.value = true;
  const { data } = await dataDevelopmentStore.fetchFlowDefineBatchPublishResult(route.query.spaceId, { key: batchId.value });
  loading.value = false;
  tableData.value = data;
}

defineExpose({ open, close });
</script>

<template>
  <BasicDialog
    width="864"
    :visible.sync="visible"
    header="批量发布工作流"
    :footer="null"
    :showFooter="null"
    :onClosed="handleClose"
    @handleClose="handleClose"
  >
    <p style="margin-bottom: 16px;">关闭弹窗后需手动刷新页面以刷新工作流的发布状态</p>
    <t-table row-key="id" :data="tableData" :columns="columns" :loading="loading" maxHeight="500" />
  </BasicDialog>
</template>

<style lang="less" scoped>
</style>
