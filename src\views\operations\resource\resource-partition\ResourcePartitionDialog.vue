<template>
  <BasicDialog
    width="560"
    :visible.sync="visible"
    :header="header"
    :submitDisabled="submitDisabled"
    :onClosed="onClose"
    @handleClose="onClose"
    @handleConfirm="onConfirm"
  >
    <t-form ref="formRef" :data="form" :rules="rules" label-width="102px">
      <t-form-item label="资源类型" name="cluster.type">
        <t-select v-model="form.cluster.type" :options="typeOptions" :popupProps="popupProps" filterable placeholder="请选择" />
      </t-form-item>
      <t-form-item label="资源集群" name="cluster.id">
        <t-select v-model="form.cluster.id" :options="clusterOptions" :popupProps="popupProps" :keys="{ label: 'code', value: 'id' }" filterable placeholder="请选择"  @change="() => changeFlag()"/>
      </t-form-item>
      <t-form-item label="资源标识" name="code">
        <t-input v-model="form.code" placeholder="请输入" :maxlength="30" show-limit-number :disabled="!isAddType" />
      </t-form-item>
      <t-form-item label="命名空间" name="resourceDetail">
        <t-input v-model="form.resourceDetail" placeholder="请输入" :maxlength="30" show-limit-number @change="() => changeFlag()"/>
      </t-form-item>
    </t-form>

    <template #footer-left>
      <t-link theme="primary" hover="color" @click="handleTest">测试连通性</t-link>
    </template>
  </BasicDialog>
</template>

<script setup>
import BasicDialog from '@/components/BasicDialog.vue';
import { useForm } from '@/hooks';
import { cloneDeep } from 'lodash';
import { MessagePlugin } from 'tdesign-vue';
import { to } from '@/utils/util';
import { useDialog } from '@/views/operations/hooks';
import { useOperationsStore } from '@/stores/operations';
import { ref, defineExpose, computed, onMounted, defineEmits, reactive } from 'vue';

const emit = defineEmits(['fetchData']);
const { fetchClustersList, fetchClustersType, fetchResourceGroupsCheckCode, fetchResourceGroupsPost, fetchResourceGroupsPut, fetchResourceGroupsTest } = useOperationsStore();

// 测试联通标识
let flag = false;
const changeFlag = (val = false) => {
  flag = val;
};

const initFormData = {
  cluster: { id: '', type: '' },
  code: '',
  resourceDetail: '',
};
const initFormRules = {
  type: [
    {
      required: true,
      message: '必选',
      type: 'error',
      trigger: 'change',
    },
  ],
  clusterId: [
    {
      required: true,
      message: '必选',
      type: 'error',
      trigger: 'change',
    },
  ],
  code: [
    {
      required: true,
      message: '必填',
      type: 'error',
      trigger: 'change',
    },
    {
      whitespace: true,
      message: '不能为空',
    },
  ],
  resourceDetail: [
    {
      required: true,
      message: '必填',
      type: 'error',
      trigger: 'change',
    },
    {
      whitespace: true,
      message: '不能为空',
    },
  ],
};

// 表单数据
const { form, rules, formRef, validateForm, resetForm, clearValidate } = useForm(
  initFormData,
  initFormRules,
);

// 提交按钮禁用状态
const submitDisabled = computed(() => !(form.cluster.type && form.code && form.resourceDetail));

const isAddType = computed(() => mode.value === 'ADD');
// 弹窗标题
const header = computed(() => (isAddType.value ? '新增资源' : '编辑资源'));

const editCb = (data) => {
  if (!data.cluster) {
    Object.assign(data, { cluster: { id: '', type: '' } });
  }
  Object.assign(form, cloneDeep(data));
};

const closeCb = () => {
  resetForm();
  clearValidate();
};

const checkCodeRepeat = async () => {
  const params = { code: form.code, clusterId: form.cluster.id };
  const [err, repeat] = await to(fetchResourceGroupsCheckCode(params));
  if (err) {
    return;
  }
  repeat && MessagePlugin('error', '资源标识重复');
  return repeat;
};

const addGroup = async () => {
  const repeat = await checkCodeRepeat();
  if (repeat) return;
  const params = { ...form };
  const [err] = await to(fetchResourceGroupsPost(params));
  if (err) {
    return;
  }
  return true;
};

const updateGroup = async () => {
  const params = { ...form };
  const [err] = await to(fetchResourceGroupsPut(params));
  if (err) {
    return;
  }
  return true;
};

const confirmCb = async () => {
  const valid = await validateForm();
  if (!valid) return;
  if (!flag) {
    MessagePlugin('error', '请测试连通性');
    return;
  }
  const func = isAddType.value ? addGroup : updateGroup;
  const result = await func();
  if (!result) return;
  emit('fetchData');
  MessagePlugin('success', '操作成功');
  return true;
};

// 弹窗数据
const { mode, visible, openAddDialog, openEditDialog, onConfirm, onClose } = useDialog({
  editCb,
  confirmCb,
  closeCb,
});

// 资源类型选择器数据
const typeOptions = ref([]);
const initTypeOptions = async () => {
  const [err, data] = await to(fetchClustersType(false));
  if (err) {
    return;
  }
  typeOptions.value = data.map(item => ({ label: item, value: item }));
};

// 资源集群选择器数据
const clusterOptions = ref([]);
const initClusterOptions = async () => {
  const params = { pageNum: 1, pageSize: 999 };
  const [err, data] = await to(fetchClustersList(params, false));
  if (err) {
    return;
  }
  clusterOptions.value = data.list;
};

// 自定义下拉选项宽度为300px
const popupProps = reactive({
  overlayInnerStyle: {
    width: '416px',
  },
});

const handleTest = async () => {
  const valid = await validateForm(['cluster.id', 'resourceDetail']);
  if (!valid) return;
  const params = { clusterId: form.cluster.id, namespace: form.resourceDetail };
  const [err, success] = await to(fetchResourceGroupsTest(params));
  if (err) {
    return;
  }
  changeFlag(success);
  if (success) {
    MessagePlugin('success', '测试连接成功');
    return;
  }
  MessagePlugin('error', '测试连接失败');
};

onMounted(() => {
  initTypeOptions();
  initClusterOptions();
});

defineExpose({ openAddDialog, openEditDialog });
</script>

<style lang="less" scoped>

</style>

