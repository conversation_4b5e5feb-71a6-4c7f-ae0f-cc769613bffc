<script setup>
// 详情页的通用布局，定义了页面的头部和主体
import { defineProps } from 'vue';
import PageHeader from './PageHeader.vue';

defineProps({
  title: String,
  showBack: {
    type: Boolean,
    default: true,
  },
  back: Function,
});
</script>

<template>
  <div class="des-common-page-layout">
    <page-header class="des-common-page-layout__head" :title="title" :showIcon="showBack" :back="back"></page-header>
    <section class="des-common-page-layout__body">
      <slot></slot>
    </section>
  </div>
</template>

<style lang="less" scoped>
.des-common-page-layout {
  width: 1200px;
  margin: 0 auto;
  padding-top: 12px;
  height: 100%;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}
.des-common-page-layout__head {
  margin-bottom: 8px;
}
.des-common-page-layout__body {
  flex-grow: 1;
  overflow: auto;
}
</style>
