<template>
  <t-layout style="position: relative">
    <t-aside width="240px" class="aside" v-show="expand">
      <!-- 数据开发操作区 -->
      <WorkbenchTitle title="数据开发">
        <WorkbenchTabs @change="changeHandler"></WorkbenchTabs>
      </WorkbenchTitle>

      <!-- 工作流操作树 加载子节点需传入load-->
      <WorkbenchTree ref="flowTree" :data="flowArray" :keys="{ label: 'nodeName', value: 'nodeId' }" :load="load" @click="handleGoFlow" @search="getFlowDefineNavTree" @refresh="getFlowDefineNavProcess" v-show="activeTab === 'flow'"></WorkbenchTree>

      <!-- 数据目录操作树 -->
      <SplitZone v-show="activeTab === 'menu'">
        <!-- 操作树 -->
        <WorkbenchTree style="height: 100%" :data="items2" @click="handleGoFlow">
          <DatasourceOperate></DatasourceOperate>
        </WorkbenchTree>
        <!-- 信息区 -->
        <template #bottom>
          <WorkbenchInfo :data="{}"></WorkbenchInfo>
        </template>
      </SplitZone>
    </t-aside>
    <!-- 隐藏工具按钮 -->
    <CollapseTool :visible.sync="expand"></CollapseTool>
    <t-layout class="layout-right">
      <t-content>
        <main-content></main-content>
      </t-content>
    </t-layout>
  </t-layout>
</template>

<script setup lang="jsx">
import { to } from '@/utils/util';
import { ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { useCommonStore } from '@/stores/common';
import { useRoute } from 'vue-router/composables';
import { editFlow } from './main-tabs/work-flow-panel/utils/flow';
import { useDataDevelopmentStore } from '@/stores/data-development';
import {
  WorkbenchTitle,
  WorkbenchTabs,
  WorkbenchTree,
  CollapseTool,
  DatasourceOperate,
  SplitZone,
  WorkbenchInfo,
} from '../components';
import MainContent from './MainContent.vue';

const flowTree = ref(null);

const route = useRoute();

const commonStore = useCommonStore();
const { expand } = storeToRefs(commonStore);
const dataDevelopmentStore = useDataDevelopmentStore();

const flowArray = ref([]);

const items2 = ref([
  {
    label: 'retail_e_com',
    type: 'shujuku',
    hasRight: true,
    children: [
      {
        label: '表',
        type: 'biao',
      },
      {
        label: '视图',
        type: 'shitu',
      },
    ],
  },
]);

const handleGoFlow = ({ context }) => {
  const { node: { data } } = context;
  if (!data.hasRight) return;
  if (data.type === 'FLOW') {
    const row = {
      id: data.nodeId,
      name: data.nodeName,
      code: data.nodeCode,
      publishState: data.publishState,
    };
    editFlow(row, route.query.spaceId);
    return;
  }
  const { parent } = data;
  const row = {
    id: parent.nodeId,
    name: parent.nodeName,
    code: parent.nodeCode,
    publishState: parent.publishState,
  };
  // 打开工作流看板并选中对应节点 defaultSelectNodeId
  editFlow(row, route.query.spaceId, { defaultSelectNodeId: data.nodeId });
};

const activeTab = ref('flow');
const changeHandler = (val) => {
  activeTab.value = val;
};

// 获取工作流定义导航树
async function getFlowDefineNavTree(searchValue) {
  const [err, data] = await to(dataDevelopmentStore.fetchFlowDefineNavTree(route.query.spaceId, { searchValue }));
  if (err) {
    return;
  }
  flowArray.value = data.map(item => ({
    ...item,
    type: 'FLOW', // 用于显示图标
    hasRight: item.permissionList?.includes('EDIT'), // 控权用
    children: item.childRenList?.map(child => ({
      ...child,
      parent: item,
      type: 'TASK', // 用于显示图标
      taskType: child.nodeType, // 用于显示颜色
      hasRight: item.permissionList?.includes('EDIT'), // 控权用
    })),
  }));
}
// 获取工作流定义导航树(第一层)
async function getFlowDefineNavProcess() {
  const [err, data] = await to(dataDevelopmentStore.fetchFlowDefineNavTreeProcess(route.query.spaceId));
  if (err) {
    return;
  }
  flowArray.value = data.map(item => ({
    ...item,
    type: 'FLOW', // 用于显示图标
    hasRight: item.permissionList?.includes('EDIT'), // 控权用
    children: true,
  }));
}
// 加载第二层数据
async function load(node) {
  console.log('加载节点:', node);
  if (node.data.type === 'TASK') {
    return;
  }
  // 调用接口获取子节点数据
  const [err, data] = await to(dataDevelopmentStore.fetchFlowDefineNavTreeTask({ spaceId: route.query.spaceId, code: node.data.nodeCode }));
  if (err) {
    return [];
  }
  if (data.length === 0) {
    flowTree.value?.resetTreeStyle();
    return [];
  }
  const nodes = data.map(item => ({
    ...item,
    parent: node.data, // 保留父节点引用
    type: 'TASK',
    taskType: item.nodeType,
    children: null,
    hasRight: node.data.permissionList?.includes('EDIT'),
  }));

  // 重置树样式
  flowTree.value?.resetTreeStyle();
  return nodes;
}

watch(
  () => route.query.spaceId,
  (val) => {
    if (val) {
      const searchValue = flowTree.value?.getSearchValue();
      if (searchValue) {
        getFlowDefineNavTree(searchValue);
      } else {
        getFlowDefineNavProcess();
      }
    }
  },
  { immediate: true },
);
</script>

<style lang="less" scoped>
.t-layout {
  height: 100%;
  .aside {
    flex: 0 0 240px;
  }
  .layout-right {
    padding: 12px 16px;
    width: 100%;
    overflow: hidden;
    :deep(.t-layout__content) {
      width: 100%;
      overflow: auto;
      border-radius: 4px;
    }
  }
}
</style>
