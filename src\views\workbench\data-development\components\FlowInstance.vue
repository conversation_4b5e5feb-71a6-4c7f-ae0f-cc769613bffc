<template>
  <div class="page-development__flow-instance">
    <div class="search-wrap">
      <BasicSearch v-model.trim="form.jobInstanceId" clearable placeholder="工作流实例标识" @reset="handleReset" @change="searchChange">
        <template #suffix>
          <t-input v-model.trim="form.jobNames[0]" placeholder="工作流标识" clearable @change="searchChange" style="width: 209px;">
            <template #suffixIcon>
              <search-icon :style="{ cursor: 'pointer' }" />
            </template>
          </t-input>
          <t-input v-model.trim="form.jobName" placeholder="工作流名称" clearable @change="searchChange" style="width: 209px;">
            <template #suffixIcon>
              <search-icon :style="{ cursor: 'pointer' }" />
            </template>
          </t-input>
          <t-select class="des-common-select-placeholder" v-model="form.jobStates" :options="stateOptions" placeholder="全部状态" multiple clearable :minCollapsedNum="1" @change="jobStatesChange" style="width:160px;"></t-select>
          <t-date-range-picker v-model="form.time" :presets="presets" allow-input clearable enable-time-picker :placeholder="['开始时间', '结束时间']" separator="至" @change="handleTimeChange" style="width: 388px;">
            <template #suffixIcon>
              <time-icon />
            </template>
          </t-date-range-picker>
        </template>
        <template #button-behind>
          <t-button variant="outline" theme="default" @click="fetchData">刷新</t-button>
        </template>
      </BasicSearch>
    </div>

    <!-- 数据表格 -->
    <t-table row-key="code" :data="tableData" :columns="columns"  :pagination="pagination" max-height="100%"/>
    <ViewLog ref="dialogRef" :getScheduleApi="getScheduleLog" :getExecuteApi="getExecuteLog"></ViewLog>
  </div>
</template>

<script setup lang="jsx">
import { to, formatTime, secondConvertTime } from '@/utils/util';
import { useRoute } from 'vue-router/composables';
import { showDialogConfirm } from '@/utils/dialog';
import emitter, { EVENT_NAMES } from '@/utils/emitter';
import { useForm, useSearch, usePagination } from '@/hooks';
import { useDataDevelopmentStore } from '@/stores/data-development';
import { LabelWithDot, ViewLog } from '@/views/workbench/components';
import { TimeIcon, ChevronDownIcon, SearchIcon } from 'tdesign-icons-vue';
import { ref, watch, nextTick, defineProps, defineEmits, reactive, onMounted, onBeforeUnmount } from 'vue';
import { viewFlow, editFlow } from '../main-tabs/work-flow-panel/utils/flow';
import { hasAuth } from '../utils/auth';
import BasicSearch from '@/components/BasicSearch.vue';
import { MessagePlugin } from 'tdesign-vue';
import { without } from 'lodash';
import dayjs from 'dayjs';

const route = useRoute();

const emit = defineEmits(['switch']);
const props = defineProps({
  current: String,
});

const dataDevelopmentStore = useDataDevelopmentStore();
const { fetchFlowInstanceList, fetchFlowInstanceOperate, fetchFlowInstanceLog, fetchFlowInstancePodLog } = dataDevelopmentStore;

const otherStates = [
  'unknown',
  'pause',
  'ready_pause',
  'delay_execution',
  'ready_block',
  'block',
  'serial_wait',
  'stop',
  'ready_stop',
];
const fetchData = async () => {
  const { current: pageNum, pageSize } = pagination;
  const query = Object.entries(form).reduce((prev, item) => {
    const [key, value] = item;
    if (value && value?.length && value[0] !== '') {
      if (key === 'time') {
        const [startTime, endTime] = value;
        Object.assign(prev, { startTime, endTime });
      } else if (key === 'jobStates') {
        if (value.includes('other')) {
          Object.assign(prev, { jobStates: [...without(value, 'other'), ...otherStates] });
        } else {
          Object.assign(prev, { [key]: value });
        }
      } else {
        Object.assign(prev, { [key]: value });
      }
    }
    return prev;
  }, {});
  const queryData = {
    ...query,
    spaceId: route.query.spaceId,
  };
  const params = { pageNum, pageSize, queryData };
  const [err, data] = await to(fetchFlowInstanceList(params));
  if (err) {
    return;
  }
  const { total, list } = data;
  setPaginationTotal(total);
  tableData.value = list || [];
};

function setFormData(data) {
  Object.assign(form, data);
}

function handleRefreshFlowInstance(data = {}) {
  const [start, end] = getRecentlyTime(1);
  setFormData({ jobInstanceId: '', jobName: '', jobState: [], time: [formatTime(start), formatTime(end)], ...data });
  fetchData();
}
onMounted(() => {
  emitter.on(EVENT_NAMES.DATA_DEVELOPMENT.REFRESH_FLOW_INSTANCE, handleRefreshFlowInstance);
});
onBeforeUnmount(() => {
  emitter.off(EVENT_NAMES.DATA_DEVELOPMENT.REFRESH_FLOW_INSTANCE, handleRefreshFlowInstance);
});

const { pagination, setPaginationCurrent, setPaginationSize, setPaginationTotal } = usePagination(fetchData);

const { onSearch } = useSearch(fetchData);

const searchChange = () => {
  setPaginationCurrent(1);
  onSearch();
};

const jobStatesChange = () => {
  setPaginationCurrent(1);
  fetchData();
};

const getRecentlyTime = (n) => {
  // 获取当前时间
  const now = dayjs();

  // 计算n天前的0点0分0秒
  const startDay = now.subtract(n, 'day').startOf('day');

  // 获取今天的23点59分59秒999毫秒
  const endDay = now.endOf('day');

  return [startDay.toDate(), endDay.toDate()];
};


// 表单数据
const initFormData = {
  jobInstanceId: '',
  // jobInstanceNameRegex: '',
  jobNames: [''],
  jobName: '',
  jobStates: [],
  time: getRecentlyTime(1).map(item => formatTime(item)),
};
const { form, resetData } = useForm(initFormData);

const handleReset = () => {
  setPaginationCurrent(1);
  resetData();
  fetchData();
};

const presets = reactive({
  最近7天: getRecentlyTime(6),
  最近3天: getRecentlyTime(2),
  今天: getRecentlyTime(0),
});

const stateOptions = ref([
  { value: 'running', label: '运行中' },
  { value: 'submit', label: '提交' },
  { value: 'failed', label: '失败' },
  { value: 'success', label: '成功' },
  { value: 'other', label: '其它' },
]);

const handleTimeChange = () => {
  setPaginationCurrent(1);
  fetchData();
};

const tableData = ref([]);

const handleViewInstance = (row) => {
  console.log('查看工作流实例', row);
  const data = {
    id: row.processId,
    name: row.jobNameNew,
    code: row.jobName,
    processInstanceId: row.jobInstanceId,
    processInstanceState: row.jobState,
  };
  dataDevelopmentStore.goViewFlowInstance(data);
};

const handleView = (row) => {
  console.log('查看工作流', row);
  const data = {
    id: row.processId,
    name: row.jobNameNew,
    code: row.jobName,
  };
  viewFlow(data);
};

const handleEdit = (row) => {
  console.log('编辑工作流', row);
  const data = {
    id: row.processId,
    name: row.jobNameNew,
    code: row.jobName,
  };
  editFlow(data);
};

const getOption = row => [
  {
    content: '查看工作流',
    value: 'flow',
  },
  {
    content: '查看任务实例',
    value: 'task',
    divider: true,
  },
  {
    content: '重跑失败任务',
    value: 'rerun',
    disabled: row.jobState !== 'failed' || !hasAuth(row, 'RUN'),
  },
];

const getJobCommandTypeLabel = type => ({
  START_PROCESS: '启动工作流',
  START_CURRENT_TASK_PROCESS: '从当前节点开始执行',
  RECOVER_TOLERANCE_FAULT_PROCESS: '恢复被容错的工作流',
  RECOVER_SUSPENDED_PROCESS: '恢复运行流程',
  START_FAILURE_TASK_PROCESS: '从失败节点开始执行',
  COMPLEMENT_DATA: '补数',
  SCHEDULER: '调度执行',
  REPEAT_RUNNING: '重跑',
  PAUSE: '暂停',
  STOP: '停止',
  RECOVER_WAITING_THREAD: '恢复等待线程',
  RECOVER_SERIAL_WAIT: '串行恢复',
}[type] || '/');
const getStatusLabel = status => ({
  unknown: '未知',
  running: '运行中',
  submit: '提交',
  failed: '失败',
  success: '成功',
  pause: '暂停',
  ready_pause: '准备暂停',
  delay_execution: '推迟执行',
  ready_block: '准备阻塞',
  block: '阻塞',
  serial_wait: '串行等待',
  stop: '停止',
  ready_stop: '准备停止',
}[status]);
const getStatusColor = status => ({
  unknown: undefined,
  running: '#3464E0',
  submit: '#50BA5E',
  failed: '#F81D22',
  success: '#50BA5E',
  pause: '#F81D22',
  ready_pause: '#F81D22',
  delay_execution: '#F81D22',
  ready_block: '#F81D22',
  block: '#F81D22',
  serial_wait: '#F81D22',
  stop: '#F81D22',
  ready_stop: '#F81D22',
}[status]);
const columns = ref([
  { title: '序号', colKey: 'serial-number', width: 60, fixed: 'left', align: 'center' },
  { title: '工作流实例标识', colKey: 'jobInstanceId', width: 130, cell: (h, { row }) => (<t-link hover="color" theme="primary" onClick={() => handleViewInstance(row)} disabled={!hasAuth(row, 'READ')}>{row.jobInstanceId}</t-link>) },
  { title: '工作流标识', colKey: 'jobName', width: 250 },
  { title: '工作流名称', colKey: 'jobNameNew', width: 250, cell: (h, { row }) => (<t-link hover="color" theme="primary" onClick={() => handleView(row)} disabled={!hasAuth(row, 'READ')}>{row.jobNameNew}</t-link>) },
  { title: '运行类型', colKey: 'jobCommandType', width: 142, ellipsis: true, cell: (h, { row }) => getJobCommandTypeLabel(row.jobCommandType) },
  {
    title: '运行状态',
    colKey: 'jobState',
    width: 124,
    cell: (h, { row }) => (
      <LabelWithDot color={getStatusColor(row.jobState)} label={getStatusLabel(row.jobState)}></LabelWithDot>
    ),
  },
  { title: '调度时间', colKey: 'scheduleTime', width: 198, cell: (h, { row }) => formatTime(row.scheduleTime) },
  { title: '开始时间', colKey: 'startTime', width: 198, cell: (h, { row }) => formatTime(row.startTime) },
  { title: '结束时间', colKey: 'endTime', width: 198, cell: (h, { row }) => formatTime(row.endTime) },
  { title: '运行时长', colKey: 'costTime', width: 116, ellipsis: true, cell: (h, { row }) => secondConvertTime(row.costTime / 1000) },
  { title: '运行次数', colKey: 'runTimes', width: 116, ellipsis: true },
  {
    title: '操作',
    colKey: 'operation',
    width: 340,
    fixed: 'right',
    cell: (h, { row }) => (
      <t-space>
        <t-link theme="primary" hover="color" disabled={ row.jobState === 'running' || !hasAuth(row, 'RUN')} onClick={() => handleRerun(row, 'restart')}>
          重跑
        </t-link>
        <t-link theme="primary" hover="color" disabled={ row.jobState !== 'running' || !hasAuth(row, 'RUN')} onClick={() => handleStop(row, 'stop')}>
          停止
        </t-link>
        <t-link theme="primary" hover="color" onClick={() => handleViewLog(row)}>
          查看日志
        </t-link>
        <t-link theme="primary" hover="color" disabled={ !hasAuth(row, 'EDIT')} onClick={() => handleEdit(row)}>
          编辑工作流
        </t-link>
        <t-dropdown options={getOption(row)} onClick={val => dropdownClick(row, val)}>
          <t-link theme="primary" hover="color">
            更多
            <ChevronDownIcon size="16" />
          </t-link>
        </t-dropdown>
      </t-space>
    ),
  },
]);

const dialogRef = ref(null);
const dropdownClick = (row, { value }) => {
  if (value === 'rerun') {
    handleRerun(row, 'restart_fails');
    return;
  }
  if (value === 'flow') {
    handleGoFlowDefine(row);
    return;
  }
  if (value === 'task') {
    handleGoTaskInstance(row);
    return;
  }
};

const handleGoFlowDefine = (row) => {
  emit('switch', 'FlowDefine', {});
  // 传参工作流标识
  emitter.emit(EVENT_NAMES.DATA_DEVELOPMENT.REFRESH_FLOW_DEFINE, { code: row.jobName });
};
const handleGoTaskInstance = (row) => {
  emit('switch', 'TaskInstance', {});
  // 传参工作流实例标识
  emitter.emit(EVENT_NAMES.DATA_DEVELOPMENT.REFRESH_TASK_INSTANCE, { jobCode: row.jobInstanceId });
};

const getScheduleLog = async (row) => {
  const { jobInstanceId } = row;
  const [err, data] = await to(fetchFlowInstanceLog({ jobInstanceId }));
  if (err) {
    return;
  }
  return data;
};
const getExecuteLog = async (row) => {
  const { jobInstanceId, jobName } = row;
  const [err, data] = await to(fetchFlowInstancePodLog({ spaceId: route.query.spaceId, processCode: jobName, jobInstanceId }));
  if (err) {
    return;
  }
  return data;
};

const handleRerun = async (row, opType) => {
  const text = opType === 'restart_fails' ? '失败任务' : '';
  const result = await showDialogConfirm({
    title: `确定重跑工作流实例「${row.jobInstanceName}」${text}吗？`,
    width: '440px',
  });
  if (result.type === 'cancel') return;
  operateFunc(row, opType);
};

const handleStop = async (row, opType) => {
  const result = await showDialogConfirm({
    title: `确定停止工作流实例「${row.jobInstanceName}」吗？`,
    width: '440px',
  });
  if (result.type === 'cancel') return;
  operateFunc(row, opType);
};

const operateFunc = async (row, opType) => {
  const params = {
    spaceId: route.query.spaceId,
    jobInstanceIds: [row.jobInstanceId],
    opType,
    jobCode: row.jobName,
  };
  const [err, data] = await to(fetchFlowInstanceOperate(params));
  if (err) {
    return;
  }
  if (data) {
    fetchData();
    MessagePlugin('success', '操作成功');
  };
};

const handleViewLog = (row) => {
  dialogRef.value.open(row);
};

watch(() => props.current, (val) => {
  if (val !== 'FlowInstance') return;
  nextTick(() => fetchData());
}, { immediate: true });

watch(() => route.query.spaceId, (val) => {
  if (!val) return;
  if (props.current !== 'FlowInstance') return;
  setPaginationCurrent(1);
  setPaginationSize(10);
  fetchData();
});
</script>

<style lang="less" scoped>
.page-development__flow-instance {
  height: 100%;
  .search-wrap {
    padding: 16px 0;
    :deep(.basic-search-input) {
      width: 209px;
    }
  }
  :deep(.t-table) {
    height: calc(100% - 108px);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
}
</style>
