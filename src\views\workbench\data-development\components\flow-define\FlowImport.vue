<script setup>
import { pick, omit } from 'lodash';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue';
import { useRoute } from 'vue-router/composables';
import { ref, defineExpose, defineEmits } from 'vue';
import FlowImportResult from './FlowImportResult.vue';
import BasicDialog from '@/components/BasicDialog.vue';
import { useDataDevelopmentStore } from '@/stores/data-development';
import { findDependencyChain } from '../../utils/task';

const emit = defineEmits(['refresh']);

const route = useRoute();

const dataDevelopmentStore = useDataDevelopmentStore();

const flowImportResultRef = ref(null);

const visible = ref(false);

const uploadData = ref([]);

const tableData = ref([]);

const columns = ref([
  { colKey: 'serial-number', title: '序号', width: 60, align: 'center' },
  { colKey: 'name', title: '文件名称', width: 205, ellipsis: true },
  {
    colKey: 'valid',
    title: '预校验状态',
    width: 165,
    cell: (h, { row }) => (
      <t-space size={4} align="center">
        <des-icon
          name={row.valid ? 'des-icon-fill-wanchengdagouzhengque' : 'des-icon-guanbiyuanxing'}
          style={{ color: row.valid ? '#0AD0B6' : '#F81D22' }}
          size="16"
        ></des-icon>
        <span>{row.valid ? '' : '数据不能为空'}</span>
      </t-space>
    ),
  },
  {
    colKey: 'operate',
    title: '操作',
    width: 88,
    cell: (h, { rowIndex }) => (
      <t-link hover="color" theme="primary" onClick={() => handleDel(rowIndex)}>
        删除
      </t-link>
    ),
  },
]);

function handleClose() {
  close();
}

// 创建进度条弹窗
function createProgressDialog(title) {
  return DialogPlugin.alert({
    theme: 'info',
    header: title,
    body: () => <t-progress percentage={0} />,
    className: 'des-common-dialog-plugin',
    confirmBtn: null,
    cancelBtn: null,
    closeBtn: false,
    width: 440,
  });
}
// 更新进度条
function updateProgressDialog(dialog, current, total) {
  const percentage = Math.floor((current / total) * 100);
  dialog.update({
    body: () => <t-progress percentage={percentage} />,
  });
}

function getPosition(index, lineType) {
  // 水平
  if (lineType === 'HORIZONTAL') {
    return {
      x: 470 + (500 * index),
      y: 220,
    };
  }
  // 垂直
  if (lineType === 'VERTICAL') {
    return {
      x: 470,
      y: 220 + (150 * index),
    };
  }
}

function getSubData(obj) {
  const { jsonData } = obj;
  // 保存和编辑提交的数据一致
  const props = ['id', 'name', 'code', 'description', 'version', 'globalParams', 'timeoutFlag', 'timeout', 'executionType', 'alertType', 'alertConfigId', 'lineType', 'warehouseLayer', 'businessType', 'timeoutNotifyStrategies'];
  return {
    ...pick(jsonData, [...props]),
    spaceId: route.query.spaceId,
    taskDefinitions: jsonData.taskDefinitions.map(item => ({
      ...omit(item, ['id', 'createUser', 'updateUser', 'createTime', 'updateTime', 'spaceId', 'flag', 'processDefinitionCode', 'processDefinitionId', 'resourceIds', 'minCpuCores', 'minMemorySpace']),
    })),
    taskRelations: jsonData.taskRelations.map(item => ({
      ...omit(item, ['id', 'spaceId', 'processDefinitionId']),
    })),
    locations: findDependencyChain(jsonData.taskRelations).map((taskCode, index) => {
      const position = getPosition(index, jsonData.lineType);
      return {
        taskCode,
        ...position,
      };
    }),
  };
}

async function handleConfirm() {
  // 创建进度条弹窗
  const progressDialog = createProgressDialog('工作流导入中');
  // 用于跟踪当前处理项的索引
  let currentIndex = 0;
  // 用于存储导入结果
  const importArray = [];

  const validData = tableData.value.filter(item => item.valid);

  if (validData.length === 0) {
    return;
  }

  for (const item of validData) {
    const subData = getSubData(item);
    const { data } = await dataDevelopmentStore.fetchFlowDefineImport(route.query.spaceId, subData);
    importArray.push({ processName: subData.name, publishState: data.publishState, version: data.version, status: data.status, type: data.type, errMsg: data.errMsg });
    // eslint-disable-next-line no-plusplus
    updateProgressDialog(progressDialog, ++currentIndex, validData.length);
    // 添加延迟，避免进度条更新太快
    await new Promise(resolve => setTimeout(resolve, 200));
  }
  progressDialog.destroy();
  // 刷新列表页数据
  emit('refresh');
  // 接口全部结束后打开弹窗
  flowImportResultRef.value.open(importArray);
  handleClose();
}

function open() {
  visible.value = true;
}
function close() {
  handleReset();
  visible.value = false;
}

async function getFileRawData(raw) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsText(raw, 'utf-8');
    reader.onload = function (event) {
      try {
        const jsonData = JSON.parse(event.target.result);
        resolve(jsonData);
      } catch (error) {
        reject(error);
      }
    };
    reader.onerror = error => reject(error);
  });
}

async function change(val) {
  const processedItems = [];
  for (const item of val) {
    try {
      const jsonData = await getFileRawData(item.raw);
      processedItems.push({ ...item, jsonData, valid: true });
    } catch (error) {
      processedItems.push({ ...item, jsonData: null, valid: false });
    }
  }
  tableData.value = processedItems;
}

function handleDel(rowIndex) {
  tableData.value.splice(rowIndex, 1);
  uploadData.value.splice(rowIndex, 1);
}

function handleReset() {
  tableData.value = [];
  uploadData.value = [];
}

function beforeUpload(file) {
  if (file.type !== 'application/json') {
    MessagePlugin.error('只能上传json文件');
    return false;
  }
  return true;
}

defineExpose({ open, close });
</script>

<template>
  <BasicDialog
    width="560"
    :visible.sync="visible"
    header="导入工作流"
    @handleClose="handleClose"
    @handleConfirm="handleConfirm"
  >
    <t-space :size="16" align="center">
      <t-upload
        action="/"
        theme="custom"
        accept=".json"
        :autoUpload="false"
        :multiple="true"
        v-model="uploadData"
        useMockProgress
        :beforeUpload="beforeUpload"
        @change="change"
      ></t-upload>

      <t-link hover="color" theme="primary" @click="handleReset">重置</t-link>
    </t-space>

    <t-table
      row-key="index"
      :data="tableData"
      :columns="columns"
      max-height="400"
      v-if="tableData.length"
      style="margin-top: 24px"
    />
    <FlowImportResult ref="flowImportResultRef"></FlowImportResult>
  </BasicDialog>
</template>

<style lang="less" scoped></style>
