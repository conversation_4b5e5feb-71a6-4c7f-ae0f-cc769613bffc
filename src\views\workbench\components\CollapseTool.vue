<template>
  <div class="collapse-tool" @click="handleClick" :style="{ left: visible ? '240px' : 0 }" @mouseenter="mouseenter" @mouseleave="mouseenter(false)">
    <template v-if="visible">
      <img src="@/assets/hover-left.svg" v-if="hover" />
      <img src="@/assets/nor-left.svg" v-else />
    </template>
    <template v-else>
      <img src="@/assets/hover-right.svg" v-if="hover" />
      <img src="@/assets/nor-right.svg" v-else />
    </template>
  </div>
</template>

<script setup>
import { defineEmits, defineProps, ref } from 'vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: true,
  },
});
const emit = defineEmits('change', 'update:visible');

function handleClick() {
  const expand = !props.visible;
  emit('update:visible', expand);
  emit('change', expand);
}

const hover = ref(false);

function mouseenter(bol = true) {
  hover.value = bol;
}
</script>

<style lang="less" scoped>
.collapse-tool {
  cursor: pointer;
  position: absolute;
  top: 120px;
}
</style>
