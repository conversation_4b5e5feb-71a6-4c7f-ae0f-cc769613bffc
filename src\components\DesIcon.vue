<script setup>
import { defineProps, computed } from 'vue';

const props = defineProps({
  name: {
    type: String,
    required: true,
  },
  size: {
    type: [String, Number],
  },
});

const iconStyle = computed(() => {
  const style = {};
  if (typeof props.size === 'number' || !isNaN(props.size / 1)) {
    style.fontSize = `${props.size}px`;
  }
  return style;
});
</script>

<template>
  <svg class="des-icon" aria-hidden="true" :style="iconStyle">
    <use :xlink:href="`#${name}`"></use>
  </svg>
</template>

<style lang="less">
.des-icon {
  display: inline-block;
  vertical-align: middle;
  width: 1em;
  height: 1em;
}
</style>
