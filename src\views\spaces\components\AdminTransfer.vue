<script setup>
import { ref, reactive, computed, defineExpose, defineProps } from 'vue';
import BasicDialog from '@/components/BasicDialog.vue';
import { useSpacesStore } from '@/stores/spaces';
import { MessagePlugin } from 'tdesign-vue';
import { useRouter } from 'vue-router/composables';

const props = defineProps({
  spaceId: {
    type: String,
    default: '',
  },
  spaceName: {
    type: String,
    default: '',
  },
});

const router = useRouter();
// const emit = defineEmits('update');
const spaceStore = useSpacesStore();
const visible = ref(false);
const formRef = ref(null);
const staffRef = ref(null);

const formData = reactive({
  id: '',
  staff: '',
  reason: '',
  permissionType: 1,
});
const selected = ref({});

const rules = {
  staff: [{ required: true, message: '请选择人员' }],
  reason: [{ required: true, message: '请输入转让原因' }],
};

const allowSub = computed(() => formData.staff && formData.reason);

function handleChangeStaff(item) {
  console.log(item);
  selected.value = item;
}

function handleClose() {
  close();
}
async function handleConfirm() {
  const result = await formRef.value.validate();
  if (result === true) {
    const data = {
      id: formData.id,
      spaceId: props.spaceId,
      user: {
        staffId: selected.value.StaffID,
        engName: selected.value.EngName,
        staffName: selected.value.StaffName,
      },
      reason: formData.reason,
      permissionType: formData.permissionType,
    };
    await spaceStore.transferSpacesMembers(data);
    MessagePlugin.success('转让成功');
    close();
    // emit('update');
    router.push({
      name: 'spaces',
    });
  }
}

function open(row) {
  visible.value = true;
  formData.id = row.id;
}
function close() {
  visible.value = false;
  formData.id = '';
  formData.staff = '';
  formData.reason = '';
  formData.permissionType = 1;
  selected.value = {};
  staffRef.value.clearSelected();
  formRef.value.reset();
}

defineExpose({ open, close });
</script>

<template>
  <BasicDialog
    width="604"
    :visible.sync="visible"
    header="转让"
    :submitDisabled="!allowSub"
    @handleClose="handleClose"
    @handleConfirm="handleConfirm"
  >
    <t-alert theme="info">
      <template #message>
        <p>正在将空间的负责人权限转让给其他人，转让后，您将<span style="color: #FF7548;">不能再管理和查看该空间</span>，请谨慎操作</p>
      </template>
    </t-alert>
    <t-form class="des-space__admin-transfer" :data="formData" style="padding-right:12px;" ref="formRef" labelWidth="78px" :rules="rules">
      <t-form-item label="空间名称" name="name">
        <div style="color: #333;">{{ spaceName }}</div>
      </t-form-item>
      <t-form-item label="转让对象" name="staff">
        <sdc-staff-selector v-model="formData.staff" ref="staffRef" @change="handleChangeStaff" style="width:100%;" placeholder="请选择人员" modalClass="des-sdc-modal--fix" size="small"></sdc-staff-selector>
      </t-form-item>
      <t-form-item label="授权角色">
        <div style="color: #333;">空间负责人</div>
      </t-form-item>
      <t-form-item label="转让原因" name="reason">
        <t-textarea  v-model="formData.reason" placeholder="请输入转让原因"  :maxlength="200" show-limit-number></t-textarea>
      </t-form-item>
    </t-form>
  </BasicDialog>
</template>

<style lang="less" scoped>
.des-space__admin-transfer {
  :deep(.t-form__label--right) {
    padding-right: 12px;
  }
}
:deep(.t-alert--info) {
  background: #f0f4ff;
}
</style>
