<template>
  <BasicDialog
    width="1240"
    :visible.sync="visible"
    :showConfirmButton="false"
    header="查看"
    :onClosed="onClose"
    @handleClose="onClose"
  >
    <BlockHeader title="基本信息"></BlockHeader>
    <div class="view-record-detail-info">
      <t-row :gutter="[12, 12]">
        <t-col class="des-flex-align-center" :span="4" ><span class="label">queryId</span><ellipsis-with-tooltip class="content" :text="form.queryId"></ellipsis-with-tooltip></t-col>
        <t-col class="des-flex-align-center" :span="4" ><span class="label">timestamp</span><span class="content">{{ form.timestamp }}</span></t-col>
        <t-col class="des-flex-align-center" :span="4" ><span class="label">queryType</span><span class="content">{{ form.queryType }}</span></t-col>
        <t-col class="des-flex-align-center" :span="4" ><span class="label">clientIp</span><span class="content">{{ form.clientIp }}</span></t-col>
        <t-col class="des-flex-align-center" :span="4" ><span class="label">user</span><span class="content">{{ form.user }}</span></t-col>
        <t-col class="des-flex-align-center" :span="4" ><span class="label">authorizedUser</span><span class="content">{{ form.authorizedUser }}</span></t-col>
        <t-col class="des-flex-align-center" :span="4" ><span class="label">resourceGroup</span><span class="content">{{ form.resourceGroup }}</span></t-col>
        <t-col class="des-flex-align-center" :span="4" ><span class="label">catalog</span><span class="content">{{ form.catalog }}</span></t-col>
        <t-col class="des-flex-align-center" :span="4" ><span class="label">db</span><span class="content">{{ form.db }}</span></t-col>
        <t-col class="des-flex-align-center" :span="4" ><span class="label">state</span><span class="content">{{ form.state }}</span></t-col>
        <t-col class="des-flex-align-center" :span="4" ><span class="label">errorCode</span><span class="content">{{ form.errorCode }}</span></t-col>
        <t-col class="des-flex-align-center" :span="4" ><span class="label">queryTime</span><span class="content">{{ form.queryTime }}</span></t-col>
        <t-col class="des-flex-align-center" :span="4" ><span class="label">scanBytes</span><span class="content">{{ form.scanBytes }}</span></t-col>
        <t-col class="des-flex-align-center" :span="4" ><span class="label">scanRows</span><span class="content">{{ form.scanRows }}</span></t-col>
        <t-col class="des-flex-align-center" :span="4" ><span class="label">returnRows</span><span class="content">{{ form.returnRows }}</span></t-col>
        <t-col class="des-flex-align-center" :span="4" ><span class="label">cpuCostNs</span><span class="content">{{ form.cpuCostNs }}</span></t-col>
        <t-col class="des-flex-align-center" :span="4" ><span class="label">memCostBytes</span><span class="content">{{ form.memCostBytes }}</span></t-col>
        <t-col class="des-flex-align-center" :span="4" ><span class="label">stmtId</span><span class="content">{{ form.stmtId }}</span></t-col>
        <t-col class="des-flex-align-center" :span="4" ><span class="label">isQuery</span><span class="content">{{ form.isQuery }}</span></t-col>
        <t-col class="des-flex-align-center" :span="4" ><span class="label">feIp</span><ellipsis-with-tooltip class="content" :text="form.feIp"></ellipsis-with-tooltip></t-col>
        <t-col class="des-flex-align-center" :span="4" ><span class="label">stmt</span><ellipsis-with-tooltip class="content" :text="form.stmt"></ellipsis-with-tooltip></t-col>
        <t-col class="des-flex-align-center" :span="4" ><span class="label">digest</span><span class="content">{{ form.digest }}</span></t-col>
        <t-col class="des-flex-align-center" :span="4" ><span class="label">planCpuCosts</span><span class="content">{{ form.planCpuCosts }}</span></t-col>
        <t-col class="des-flex-align-center" :span="4" ><span class="label">planMemCosts</span><span class="content">{{ form.planMemCosts }}</span></t-col>
        <t-col class="des-flex-align-center" :span="4" ><span class="label">pendingTimeMs</span><span class="content">{{ form.pendingTimeMs }}</span></t-col>
        <t-col class="des-flex-align-center" :span="4" ><span class="label">candidateMVs</span><span class="content">{{ form.candidateMVs }}</span></t-col>
        <t-col class="des-flex-align-center" :span="4" ><span class="label">hitMvs</span><span class="content">{{ form.hitMvs }}</span></t-col>
      </t-row>
    </div>
  </BasicDialog>
</template>

<script setup>
import { defineExpose, reactive, defineProps } from 'vue';
import { useDialog } from '@/views/operations/hooks';
import BasicDialog from '@/components/BasicDialog.vue';
import BlockHeader from '@/components/BlockHeader.vue';
import EllipsisWithTooltip from '@/components/EllipsisWithTooltip.vue';
import { cloneDeep } from 'lodash';

defineProps({
  getDatasourceLabel: {
    type: Function,
    default: () => () => '',
  },
});

// "queryId": "string",
// "timestamp": "string",
// "queryType": "string",
// "clientIp": "string",
// "user": "string",
// "authorizedUser": "string",
// "resourceGroup": "string",
// "catalog": "string",
// "db": "string",
// "state": "string",
// "errorCode": "string",
// "queryTime": 0,
// "scanBytes": 0,
// "scanRows": 0,
// "returnRows": 0,
// "cpuCostNs": 0,
// "memCostBytes": 0,
// "stmtId": 0,
// "isQuery": 0,
// "feIp": "string",
// "stmt": "string",
// "digest": "string",
// "planCpuCosts": 0,
// "planMemCosts": 0,
// "pendingTimeMs": 0,
// "candidateMVs": "string",
// "hitMvs": "string",
// "warehouse": "string",
// "datasourceId": "string"
const form = reactive({});

const editCb = (data) => {
  Object.assign(form, cloneDeep(data));
};
// 弹窗数据
const { visible, openAddDialog, openEditDialog, onClose } = useDialog({
  editCb,
});

defineExpose({ openAddDialog, openEditDialog });
</script>

<style lang="less" scoped>
.view-record-detail-info {
  margin-top: 12px;
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  .label {
    display: inline-block;
    width: 134px;
    margin-right: 8px;
    color: #999999;
    text-align: right;
  }
  .content {
    display: inline-block;
    color: #333333;
    width: calc(~"100% - 142px");
  }
}
</style>
