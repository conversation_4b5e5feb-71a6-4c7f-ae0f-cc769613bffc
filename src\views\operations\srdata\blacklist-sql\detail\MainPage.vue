<template>
  <div class="blacklist-sql">
    <page-header class="des-common-page-layout__head" :title="title" showIcon :back="() => router.back()"></page-header>
    <section class="blacklist-sql-content">
      <t-row class="page-space-detail-info" v-if="isEdit">
        <t-col class="des-flex-align-center" :span="4"><span class="label">黑名单 ID</span><span class="content">{{ form.id }}</span></t-col>
        <t-col class="des-flex-align-center" :span="4"><span class="label">SR数据源</span><span class="content">{{ form.datasourceName }}</span></t-col>
      </t-row>

      <t-form ref="formRef" :data="form" :rules="rules" label-width="127px" style="width: 400px" v-else>
        <t-form-item label="SR数据源" name="datasourceId">
          <t-select v-model="form.datasourceId" :options="datasourceOptions" filterable placeholder="请选择" />
        </t-form-item>
      </t-form>

      <div class="page-space-blacklist-sql__wrap">
        <BasicSqlEditor v-model="form.sqlRegex" title="黑名单 SQL">
          <t-link theme="primary" hover="color" @click="handleTest">测试黑名单</t-link>
        </BasicSqlEditor>
      </div>
    </section>
    <BasicPageFooter class="page-space-footer" @confirm="handleConfirm" @cancel="() => router.back()"></BasicPageFooter>

    <BlacklistDialog ref="dialogRef" :sql="form.sqlRegex"></BlacklistDialog>
  </div>
</template>

<script setup>
import { omit } from 'lodash';
import { to } from '@/utils/util';
import { onMounted, ref } from 'vue';
import { useForm } from '@/hooks';
import { useRoute, useRouter } from 'vue-router/composables';
import BlacklistDialog from './BlacklistDialog.vue';
import PageHeader from '@/components/PageHeader.vue';
import BasicSqlEditor from '@/components/BasicSqlEditor.vue';
import BasicPageFooter from '@/components/BasicPageFooter.vue';
import { useOperationsStore } from '@/stores/operations';
import { MessagePlugin } from 'tdesign-vue';

const operationsStore = useOperationsStore();

const route = useRoute();
const router = useRouter();
// 黑名单id
const { id } = route.query;
const isEdit = Boolean(id);
const title = isEdit ? '编辑黑名单' : '添加黑名单';

const initFormData = { datasourceId: '', sqlRegex: '' };
const initFormRules = {
  datasourceId: [
    {
      required: true,
      message: '必选',
      type: 'error',
      trigger: 'change',
    },
  ],
};

const datasourceOptions = ref([]);
// 表单数据
const { form, rules, formRef, validateForm } = useForm(initFormData, initFormRules);

const handleConfirm = async () => {
  if (!isEdit) {
    const valid = await validateForm();
    if (!valid) return;
  }
  if (!form.sqlRegex) {
    MessagePlugin.error('请输入黑名单 SQL');
    return;
  }
  const params = { ...form };
  const api = isEdit ? operationsStore.fetchBlacklistEdit : operationsStore.fetchBlacklistAdd;
  const [err] = await to(api(params));
  if (err) {
    return;
  }
  MessagePlugin.success('操作成功');
  goList();
};

const dialogRef = ref(null);
const handleTest = () => {
  if (!form.sqlRegex) {
    MessagePlugin.error('请先输入黑名单 SQL');
    return;
  }
  dialogRef.value.openAddDialog();
};

const init = async () => {
  if (!id) return;
  const [err, data] = await to(operationsStore.fetchBlacklistDetail(id));
  if (err) {
    return;
  }
  const obj = {
    ...omit(data, ['datasource']),
    datasourceId: data.datasource,
  };
  Object.assign(form, obj);
};

const getDataSourceOptions = async () => {
  if (id) return;
  const data = { pageNum: 1, pageSize: 999, queryData: { queryKey: '' } };
  const [err, result] = await to(operationsStore.fetchDatasourcesList(data));
  if (err) {
    return;
  }
  datasourceOptions.value = result.list.map(item => ({
    ...item,
    label: item.name,
    value: item.id,
  }));
};

const goList = () => {
  router.push({
    name: 'srdata',
    params: {
      datasourceId: form.datasourceId,
    },
  });
};

onMounted(() => {
  init();
  getDataSourceOptions();
});
</script>

<style lang="less" scoped>
.blacklist-sql {
  height: 100%;
  width: 1200px;
  margin: 0 auto;
  padding: 12px 0;

  display: flex;
  flex-direction: column;

  .blacklist-sql-content {
    flex: 1;
    padding: 20px;
    margin-top: 8px;
    background: #fff;

    display: flex;
    flex-direction: column;

    .page-space-detail-info {
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      .label {
        display: inline-block;
        margin-right: 8px;
        color: #999999;
        text-align: right;
      }
      .content {
        display: inline-block;
        color: #333333;
      }
    }

    .page-space-blacklist-sql__wrap {
      flex: 1;
      min-height: 0;
      margin-top: 16px;
      overflow-y: auto;
    }
  }

  .page-space-footer {
    flex-shrink: 0;
  }
}
</style>
