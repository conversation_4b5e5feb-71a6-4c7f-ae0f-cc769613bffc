<script setup>
import { ref, defineProps, defineEmits } from 'vue';
import BasicSearch from '@/components/BasicSearch.vue';

defineProps({
  data: {
    type: Array,
    default: () => [],
  },
  name: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['check', 'reset']);

function handleCheckAll() {
  emit('check');
}
function handleReset() {
  emit('reset');
}

const treeRef = ref(null);
const filter = ref(null);
const searchKey = ref('');
function filterMethod() {
  return filter.value = searchKey.value ? node => node.data.label.indexOf(searchKey.value) >= 0 : null;
}

function resetSearch() {
  searchKey.value = '';
  filterMethod();
}
</script>

<template>
  <div class="des-spaces__authorize-tree">
    <div class="search-wrap">
      <basic-search v-model="searchKey" placeholder="请输入关键词" clearable style="margin-bottom: 12px;" @change="filterMethod" @reset="resetSearch"></basic-search>
      <slot name="tips"></slot>
    </div>
    <div class="tree-header">
      <div class="tree-header--col" style="width: 308px;">
        <div>{{ name }}</div>
      </div>
      <div class="tree-header--col" style="flex-shrink: 0; width: 452px;">
        <div>权限</div>
        <div>
          <t-space :size="20">
            <t-link hover="color" theme="primary" @click="handleCheckAll"><des-icon name="des-icon-quanxuan" size="16px" style="margin-right: 2px;"></des-icon>全选</t-link>
            <t-link hover="color" theme="primary" @click="handleReset"><des-icon name="des-icon-shuaxin" size="16px" style="margin-right: 2px;"></des-icon>重置</t-link>
          </t-space>
        </div>
      </div>
    </div>
    <t-tree
      ref="treeRef"
      :data="data"
      hover
      expand-all
      :filter="filter"
      :maxHeight="460"
      :expand-on-click-node="false"
      :line="true"
    >
      <template #label="scope">
        <slot name="label" v-bind="scope"></slot>
      </template>
      <template #operations="scope">
        <div style="width:452px;padding-left: 12px;">
          <slot name=operations v-bind="scope"></slot>
        </div>
      </template>
    </t-tree>
  </div>
</template>

<style lang="less" scoped>
.des-spaces__authorize-tree {
  .search-wrap {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }
  .tree-header {
    display: flex;
    height: 40px;
    align-items: center;
    background: #F5F7F9;
    .tree-header--col {
      display: flex;
      height: 100%;
      align-items: center;
      justify-content: space-between;
      padding: 0 22px 0 12px;
      &:not(:last-child) {
        box-shadow: 1px 0 0 0 #eee;
      }
    }
  }
  :deep(.t-tree__item) {
    box-shadow: 0 1px 0 0 #eee;
    .t-tree__label:hover {
      background-color: #fff;
    }
    .t-tree__label {
      box-shadow: 1px 0 0 0 #eee;
      border-radius: 0;
      height: 40px;
      padding-top: 0;
      padding-bottom: 0;
      line-height: 40px;
    }
  }
}
</style>
