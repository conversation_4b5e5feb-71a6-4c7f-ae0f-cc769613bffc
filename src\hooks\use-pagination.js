import { reactive } from 'vue';

export function usePagination(fn, options = {}) {
  const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 10,
    showJumper: true,
    onChange: ({ current, pageSize }) => {
      setPaginationCurrent(current);
      if (pagination.pageSize !== pageSize) {
        setPaginationCurrent(1);
      }
      setPaginationSize(pageSize);
      fn();
    },
    ...options,
  });
  const setPaginationCurrent = (current) => {
    Object.assign(pagination, { current });
  };
  const setPaginationSize = (pageSize) => {
    Object.assign(pagination, { pageSize });
  };
  const setPaginationTotal = (total) => {
    Object.assign(pagination, { total });
  };
  return { pagination, setPaginationCurrent, setPaginationSize, setPaginationTotal };
};
