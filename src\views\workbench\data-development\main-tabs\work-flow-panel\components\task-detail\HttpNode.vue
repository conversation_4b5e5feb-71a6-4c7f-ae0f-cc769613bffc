<script setup>
import { HTTP } from '../../utils/task-type.js';
import { httpRules } from '../../utils/rules.js';
import { reactive, ref, defineExpose, onMounted, defineProps } from 'vue';
// import { useDataDevelopmentStore } from '@/stores/data-development';
import BlockHeader from '@/components/BlockHeader.vue';
import BasicConfig from './common/BasicConfig.vue';
import ResourceConfig from './common/ResourceConfig.vue';
import AlarmConfig from './common/AlarmConfig.vue';
import PreTask from './common/PreTask.vue';
import { uuid } from '@/utils/util.js';

defineProps({
  currentFlowName: {
    type: String,
    default: '',
  },
  readonly: {
    type: Boolean,
    default: false,
  },
});

const formRef = ref(null);
const formData = reactive({
  taskType: HTTP,
  id: '',
  // 节点名称
  name: '',
  // 节点标识
  code: '',
  // 节点描述
  description: '',
  // 失败重试次数
  failRetryTimes: 0,
  // 失败重试间隔
  failRetryInterval: 0,
  // 延迟执行
  delayTime: 0,
  // 自定义参数
  // params: [
  //   { key: '1', name: '参数1', relation: 'in', type: 'string', parameter: 'value' },
  // ],
  // 运行集群
  resourceGroupId: '',
  // cpu
  minCpuCores: 0.5,
  // 内存
  minMemorySpace: 512,
  // 超时告警开关
  timeoutFlag: false,
  // 超时时长
  timeout: 0,
  // 超时是否失败
  timeoutFailed: false,
  taskProperties: {
    // 请求地址
    url: '',
    // 请求类型
    requestMethod: '',
    // 请求header
    headers: [
      { key: uuid(), key: '', value: '' },
    ],
    // 请求body
    body: '',
    // 预置脚本
    preScript: '',
    // 返回值判断
    resultCheckType: '',
    // 返回值校验值
    resultCheckValue: '',
    // 连接超时
    connectTimeout: 60,
    // Socket超时
    socketTimeout: 600,
  },
});

const nodeId = ref('');

function getData() {
  return {
    taskType: formData.taskType,
    id: formData.id,
    name: formData.name,
    code: formData.code,
    description: formData.description,
    failRetryTimes: formData.failRetryTimes,
    failRetryInterval: formData.failRetryInterval,
    delayTime: formData.delayTime,
    resourceGroupId: formData.resourceGroupId,
    minCpuCores: formData.minCpuCores,
    minMemorySpace: formData.minMemorySpace,
    timeoutFlag: formData.timeoutFlag,
    timeout: formData.timeout,
    timeoutFailed: formData.timeoutFailed,
    taskProperties: {
      url: formData.taskProperties.url,
      requestMethod: formData.taskProperties.requestMethod,
      headers: JSON.stringify(formData.taskProperties.headers.filter(item => item.key && item.value)),
      body: formData.taskProperties.body,
      preScript: formData.taskProperties.preScript,
      resultCheckType: formData.taskProperties.resultCheckType,
      resultCheckValue: formData.taskProperties.resultCheckValue,
      connectTimeout: formData.taskProperties.connectTimeout,
      socketTimeout: formData.taskProperties.socketTimeout,
    },
  };
}

async function save() {
  const result = await formRef.value.validate();
  if (result !== true) {
    return;
  }
  const data = getData();
  return {
    nodeId: nodeId.value,
    data,
  };
}
// function reset() {
//   formRef.value.reset();
// }
function setData(nodeid, data) {
  nodeId.value = nodeid;
  Object.keys(data).forEach((key) => {
    formData[key] = data[key];
    if (key === 'taskProperties') {
      const headers = JSON.parse(data.taskProperties.headers);
      formData.taskProperties.headers = headers.length ? headers.map(item => ({ key: uuid(), ...item })) : [{ key: uuid(), key: '', value: '' }];
    }
  });
}

const requestMethodOptions = [
  { label: 'GET', value: 'GET' },
  { label: 'POST', value: 'POST' },
  { label: 'PUT', value: 'PUT' },
  { label: 'DELETE', value: 'DELETE' },
  // { label: 'PATCH', value: 'PATCH' },
];
const resultCheckTypeOptions = [
  { label: '响应码', value: 'RESPONSE_CODE' },
  { label: '内容包含', value: 'CONTENT_INCLUDE' },
  { label: '内容不包含', value: 'CONTENT_EXCLUDE' },
];

function handleAddParam() {
  formData.taskProperties.headers.push({ key: uuid(), key: '', value: '' });
}
function handleDeleteParam(index) {
  formData.taskProperties.headers.splice(index, 1);
}

function resultCheckTypeChange(value) {
  if (value === 'RESPONSE_CODE') {
    formData.taskProperties.resultCheckValue = '200';
  } else {
    formData.taskProperties.resultCheckValue = '';
  }
}

defineExpose({
  setData,
  // reset,
  save,
});

onMounted(() => { });
</script>

<template>
  <div class="des-workflow-job-form__http-node">
    <t-form :data="formData" ref="formRef" label-width="132px" :rules="httpRules" :disabled="readonly">
      <basic-config :formData="formData"></basic-config>
      <resource-config :formData="formData"></resource-config>
      <alarm-config :formData="formData"></alarm-config>
      <section>
        <block-header title="更多设置" style="margin-bottom: 12px;" />
        <t-form-item label="请求地址" name="taskProperties.url">
          <t-input v-model="formData.taskProperties.url" style="width: 374px;"></t-input>
        </t-form-item>
        <t-form-item label="请求类型" name="taskProperties.requestMethod">
          <t-select v-model="formData.taskProperties.requestMethod" :options="requestMethodOptions" filterable style="width: 374px;"></t-select>
        </t-form-item>
        <section class="global-params-setting">
          <h3>请求header</h3>
          <t-form-item
            v-for="(param, index) in formData.taskProperties.headers"
            :key="param.id"
            :label="`header${index + 1}`"
            :name="`params[${index}]`">
            <div class="params-row">
              <t-input v-model.trim="param.key" placeholder="key" style="width: 140px;"></t-input>
              <t-input v-model.trim="param.value" placeholder="value" style="width: 140px;"></t-input>
              <div class="delete-btn" @click="handleDeleteParam(index)">
                <des-icon name="des-icon-shanshu" size="14"></des-icon>
              </div>
            </div>
          </t-form-item>
          <div style="margin-bottom: 18px;">
            <t-link hover="color" theme="primary" @click="handleAddParam"><des-icon name="des-icon-jiaxiao" size="14px"></des-icon> 添加header</t-link>
          </div>
        </section>
        <t-form-item label="请求body">
          <t-textarea v-model="formData.taskProperties.body" :autosize="{ minRows: 4, maxRows: 4 }" style="width: 374px;" />
        </t-form-item>
        <t-form-item label="预置脚本">
          <t-textarea v-model="formData.taskProperties.preScript" :autosize="{ minRows: 4, maxRows: 4 }" style="width: 374px;" />
        </t-form-item>
        <t-form-item label="返回值判断" name="taskProperties.resultCheckType">
          <t-select v-model="formData.taskProperties.resultCheckType" :options="resultCheckTypeOptions" filterable @change="resultCheckTypeChange" style="width: 374px;"></t-select>
        </t-form-item>
        <template v-if="formData.taskProperties.resultCheckType">
          <t-form-item label="校验响应码" name="taskProperties.resultCheckValue" v-if="formData.taskProperties.resultCheckType === 'RESPONSE_CODE'">
            <t-input v-model="formData.taskProperties.resultCheckValue" style="width: 374px;" placeholder="请输入响应码" />
          </t-form-item>
          <t-form-item label="校验字符串" name="taskProperties.resultCheckValue" v-else>
            <t-textarea v-model="formData.taskProperties.resultCheckValue" :autosize="{ minRows: 2, maxRows: 2 }" style="width: 374px;" placeholder="请输入用户仅校验的字符串，此判断仅支持返回字符串是否包含输入内容，更多复杂内容请自行处理" />
          </t-form-item>
        </template>
        <t-form-item label="连接超时" name="taskProperties.connectTimeout">
          <t-input-number v-model="formData.taskProperties.connectTimeout" :min="0" theme="column" style="width: 374px;">
            <template #suffix><span>秒</span></template>
          </t-input-number>
        </t-form-item>
        <t-form-item label="Socket超时" name="taskProperties.socketTimeout">
          <t-input-number v-model="formData.taskProperties.socketTimeout" :min="0" theme="column" style="width: 374px;">
            <template #suffix><span>秒</span></template>
          </t-input-number>
        </t-form-item>
      </section>
      <section>
        <block-header title="任务关联" style="margin-bottom: 12px;" />
        <!-- 前置任务 -->
        <pre-task :currentId="formData._id" :currentFlowName="currentFlowName"></pre-task>
      </section>
    </t-form>
  </div>
</template>

<style lang="less" scoped>
.des-workflow-job-form__http-node {
  .global-params-setting {
    width: 505px;
    padding-left: 44px;
    & > h3 {
      color: #333333;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px;
      margin-bottom: 8px;
    }
    :deep(.t-form__item) {
      padding: 8px 0;
      border-radius: 4px;
      background: #F4F4F4;
      margin-bottom: 8px;
      .t-is-error {
        .t-input__extra {
          bottom: -28px;
        }
      }
    }
    .params-row {
      display: flex;
      gap: 12px;
      align-items: center;
      .delete-btn {
        width: 24px;
        height: 24px;
        cursor: pointer;
        flex-shrink: 0;
        &:hover {
          color: var(--des-color-theme);
        }
      }
    }
  }
}
</style>
