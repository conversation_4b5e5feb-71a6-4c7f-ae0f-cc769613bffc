<template>
  <div class="basic-page-footer__wrap des-flex-align-center" :style="style">
    <!-- 自定义按钮 -->
    <slot v-if="hasDefaultSlot"></slot>
    <!-- 默认按钮 -->
    <t-space v-else>
      <t-button theme="primary" @click="emit('confirm')">{{ confirmText }}</t-button>
      <t-button theme="default" variant="outline" @click="emit('cancel')">{{ cancelText }}</t-button>
      <slot name="behind"></slot>
    </t-space>
  </div>
</template>

<script setup>
import { defineProps, defineEmits, useSlots, computed } from 'vue';

const props = defineProps({
  confirmText: {
    type: String,
    default: '提交',
  },
  cancelText: {
    type: String,
    default: '取消',
  },
  height: {
    type: [String, Number],
    default: 48,
  },
});

const emit = defineEmits(['confirm', 'cancel']);

// 获取插槽对象
const slots = useSlots();
const hasDefaultSlot = !!slots.default;

const style = computed(() => {
  const obj = {};
  if (typeof props.height === 'number' || !isNaN(props.height / 1)) {
    obj.height = `${props.height}px`;
  }
  return obj;
});
</script>

<style lang="less" scoped>
.basic-page-footer__wrap {
  justify-content: center;
  background: #fff;
  box-shadow: 0 -4px 4px 0 #0000000a;
}
</style>
