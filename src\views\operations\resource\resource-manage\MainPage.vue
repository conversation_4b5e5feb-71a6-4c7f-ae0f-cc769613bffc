<template>
  <div style="height: 100%">
    <PageLayout>
      <template #search>
        <BasicSearch
          v-model="searchKey"
          @reset="handleReset"
          @change="searchChange"
        ></BasicSearch>
      </template>

      <template #button>
        <t-button theme="primary" @click="handleAdd"><add-icon slot="icon" />新增资源群</t-button>
      </template>

      <template #table>
        <t-table
          rowKey="id"
          :data="tableData"
          :columns="columns"
          :pagination="pagination"
          max-height="68vh"
        ></t-table>
      </template>
    </PageLayout>

    <ResourceDialog ref="dialogRef" @fetchData="fetchData"></ResourceDialog>
  </div>
</template>

<script setup lang="jsx" name="resource-manage">
import { to, formatTime } from '@/utils/util';
import { ref, onMounted } from 'vue';
import { MessagePlugin } from 'tdesign-vue';
import { AddIcon } from 'tdesign-icons-vue';
import { useSearch, usePagination } from '@/hooks';
import { showDialogConfirm, showDialogAlert } from '@/utils/dialog';
import { useOperationsStore } from '@/stores/operations';
import { PageLayout } from '@/views/operations/components';
import ResourceDialog from './ResourceDialog.vue';
import BasicSearch from '@/components/BasicSearch.vue';
import UserName from '@/components/UserName.vue';

const { fetchClustersList, fetchClustersDel, fetchClustersCheckDel } = useOperationsStore();

// 表格列
const columns = ref([
  { colKey: 'serial-number', width: 61, title: '序号', fixed: 'left' },
  {
    colKey: 'code',
    title: '集群标识',
    width: 265,
    ellipsis: true,
  },
  {
    colKey: 'type',
    title: '集群类型',
    width: 265,
    cell: (h, { row }) => row.type,
  },
  {
    colKey: 'createUser',
    title: '创建人',
    width: 265,
    cell: (h, { row }) => <UserName fullName={row.createUser}></UserName>,
  },
  {
    colKey: 'updateTime',
    title: '更新时间',
    width: 265,
    cell: (h, { row }) => formatTime(row.updateTime),
  },
  {
    title: '操作',
    colKey: 'operate',
    width: 150,
    cell: (h, { row }) => (
      <t-space>
        <t-link theme="primary" hover="color" onClick={() => handleEdit(row)}>
          编辑
        </t-link>
        <t-link theme="primary" hover="color" onClick={() => handleDel(row)}>
          删除
        </t-link>
      </t-space>
    ),
  },
]);

// 表格数据
const tableData = ref([]);

const fetchData = async () => {
  const { current: pageNum, pageSize } = pagination;
  const params = { pageNum, pageSize, queryData: { queryKey: searchKey.value } };
  const [err, data] = await to(fetchClustersList(params));
  if (err) {
    return;
  }
  const { total, list } = data;
  setPaginationTotal(total);
  tableData.value = list;
};

const { pagination, setPaginationCurrent, setPaginationTotal } = usePagination(fetchData);
const { searchKey, setSearchKey, onSearch } = useSearch(fetchData);

const searchChange = () => {
  setPaginationCurrent(1);
  onSearch();
};

const handleReset = () => {
  setSearchKey('');
  setPaginationCurrent(1);
  fetchData();
};

const fetchDel = async (row) => {
  const [err, data] = await to(fetchClustersDel(row.id));
  if (err) return;
  if (data) {
    fetchData();
    MessagePlugin('success', '操作成功');
  }
};

const handleDel = async (row) => {
  const result = await showDialogConfirm({
    title: `确定删除资源群「${row.code}」吗？`,
    body: '删除后不可恢复',
    width: '440px',
  });
  if (result.type === 'cancel') return;
  // 预校验
  const [err, data] = await to(fetchClustersCheckDel(row.id));
  if (err) return;
  if (data) {
    fetchDel(row);
    return;
  }
  showDialogAlert({
    title: `不可删除资源群「${row.code}」`,
    body: '当前资源群已存在资源划分，不可删除',
    width: '440px',
  });
};

const dialogRef = ref(null);

// 新增操作
const handleAdd = () => {
  dialogRef.value.openAddDialog();
};

// 编辑操作
const handleEdit = (row) => {
  dialogRef.value.openEditDialog({ ...row });
};

// 生命周期钩子
onMounted(() => {
  fetchData();
});
</script>

<style lang="less" scoped></style>
