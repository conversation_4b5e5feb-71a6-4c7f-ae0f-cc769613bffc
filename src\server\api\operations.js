// SR数据源管理
export const API_OPERATIONS_DATASOURCES_LIST = '/api/datasources/pageList';
export const API_OPERATIONS_DATASOURCES_ALL_LIST = '/api/datasources/list';
export const API_OPERATIONS_DATASOURCES_CANCEL = '/api/datasources/cancel';
export const API_OPERATIONS_DATASOURCES_ADD = '/api/datasources/saveDatasource';
export const API_OPERATIONS_DATASOURCES_UPDATE = '/api/datasources/updateDatasource';
export const API_OPERATIONS_DATASOURCES_TEST = '/api/datasources/test';
export const API_OPERATIONS_DATASOURCES_RELATED = '/api/datasources/getBy';
// 通过空间ID获取SR数据源
export const API_OPERATIONS_DATASOURCES_BY_SPACEID = '/api/datasources/listBySpaceId';

// 资源群管理
export const API_OPERATIONS_CLUSTERS_LIST = '/api/clusters/list';
export const API_OPERATIONS_CLUSTERS_CHECKCODE = '/api/clusters/checkCode';
export const API_OPERATIONS_CLUSTERS_TYPE = '/api/clusters/types';
// 资源群管理 删除预校验
export const API_OPERATIONS_CLUSTERS_CHECK_DEL = id => `/api/clusters/${id}/checkDelete`;
// 资源群管理（新增/修改/删除/详情，除新增外需要增加id作为参数）
export const API_OPERATIONS_CLUSTERS = (id = '') => (id ? `/api/clusters/${id}` : '/api/clusters');

// 资源划分管理
export const API_OPERATIONS_RESOURCE_GROUPS_LIST = '/api/resource-groups/list';
export const API_OPERATIONS_RESOURCE_GROUPS_CHECKCODE = '/api/resource-groups/checkCode';
// 通过空间ID获取资源划分
export const API_OPERATIONS_RESOURCE_GROUPS_BY_SPACEID = '/api/resource-groups/listBySpaceId';
// 资源划分 测试连通性
export const API_OPERATIONS_RESOURCE_GROUPS_TEST = '/api/resource-groups/testConnectivity';
// 资源划分管理 删除预校验
export const API_OPERATIONS_RESOURCE_GROUPS_CHECK_DEL = id => `/api/resource-groups/${id}/checkDelete`;
// 资源划分管理（新增/修改/删除/详情，除新增外需要增加id作为参数）
export const API_OPERATIONS_RESOURCE_GROUPS = (id = '') => (id ? `/api/resource-groups/${id}` : '/api/resource-groups');

// 资源划分管理 - 权限
export const API_OPERATIONS_RESOURCE_GRANT_LIST = '/api/resourceGrant/list';
export const API_OPERATIONS_RESOURCE_GRANT_AUTH = '/api/resourceGrant/grant';

// 黑名单管理
// 分页列表
export const API_OPERATIONS_BLACKLIST_LIST = '/api/blacklist/list';
// 新增/删除/编辑
export const API_OPERATIONS_BLACKLIST = id => (id ? `/api/blacklist/${id}` : '/api/blacklist');
// 测试黑名单正则
export const API_OPERATIONS_BLACKLIST_CHECK = '/api/blacklist/check';

// 当前执行SQL管理
// 分页列表
export const API_OPERATIONS_CUR_QUERY_LIST = datasourceId => `/api/${datasourceId}/curQuery/list`;
// 终止SQL执行
export const API_OPERATIONS_CUR_QUERY = (datasourceId, id) => `/api/${datasourceId}/curQuery/${id}`;

// 资源组管理
// 分页列表
export const API_OPERATIONS_SOURCE_GROUP_LIST = datasourceId => `/api/${datasourceId}/sourceGroup/list`;
// 新增
export const API_OPERATIONS_SOURCE_GROUP_ADD = datasourceId => `/api/${datasourceId}/sourceGroup`;
// 编辑
export const API_OPERATIONS_SOURCE_GROUP_EDIT = (datasourceId, code) => `/api/${datasourceId}/sourceGroup/${code}`;
// 删除
export const API_OPERATIONS_SOURCE_GROUP_DEL = (datasourceId, name) => `/api/${datasourceId}/sourceGroup/${name}`;
// 校验正则
export const API_OPERATIONS_SOURCE_GROUP_CHECK_REGEX = datasourceId => `/api/${datasourceId}/sourceGroup/checkRegex`;
// 校验名称
export const API_OPERATIONS_SOURCE_GROUP_CHECK_NAME = (datasourceId, name) => `/api/${datasourceId}/sourceGroup/checkName/${name}`;
// 校验标识
export const API_OPERATIONS_SOURCE_GROUP_CHECK_CODE = (datasourceId, code) => `/api/${datasourceId}/sourceGroup/checkCode/${code}`;

// 动态参数展示与修改
// 分页列表
export const API_OPERATIONS_DYNAMIC_PARAM_LIST = datasourceId => `/api/${datasourceId}/dynamicParam/list`;
// 编辑
export const API_OPERATIONS_DYNAMIC_PARAM_SET = datasourceId => `/api/${datasourceId}/dynamicParam/set`;
