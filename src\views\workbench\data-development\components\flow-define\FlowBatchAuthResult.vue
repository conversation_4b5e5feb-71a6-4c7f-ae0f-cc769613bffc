<script setup>
import { ref, defineExpose } from 'vue';
import BasicDialog from '@/components/BasicDialog.vue';
import BlockHeader from '@/components/BlockHeader.vue';

const visible = ref(false);

const tableData = ref([]);

const authMap = { EDIT: '编辑', RUN: '运行', READ: '查看', GRANT: '授予' };

const columns = ref([
  { colKey: 'user.staffName', title: '人员', width: 173 },
  {
    colKey: 'pre',
    title: '变更前',
    width: 365,
    cell: (h, { row }) => <div>{ row.processDefinitionList?.map(item => <p>{ `${item.processDefinitionName}(${item.preChange ? item.preChange.map(auth => authMap[auth]).join('、') : '无'})` }</p>) }</div>,
  },
  {
    colKey: 'post',
    title: '变更后',
    width: 365,
    cell: (h, { row }) => <div>{ row.processDefinitionList?.map(item => <p>{ `${item.processDefinitionName}(${item.postChange ? item.postChange.map(auth => authMap[auth]).join('、') : '无'})` }</p>) }</div>,
  },
]);

function handleClose() {
  close();
}

function open(data) {
  visible.value = true;
  tableData.value = data;
}
function close() {
  visible.value = false;
}
defineExpose({ open, close });
</script>

<template>
  <BasicDialog
    width="957"
    :visible.sync="visible"
    header="批量授权结果"
    :footer="null"
    :showFooter="null"
    @handleClose="handleClose"
  >
    <BlockHeader title="权限变更的人员" style="margin-bottom: 16px;"></BlockHeader>

    <t-table row-key="id" :data="tableData" :columns="columns" maxHeight="500" />
  </BasicDialog>
</template>

<style lang="less" scoped>
</style>
