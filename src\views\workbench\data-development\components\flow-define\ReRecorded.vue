<script setup>
import { ref, reactive, defineExpose, defineProps, computed } from 'vue';
import BasicDialog from '@/components/BasicDialog.vue';
import { useDataDevelopmentStore } from '@/stores/data-development';
import { isEmptyValue, uuid } from '@/utils/util';
import dayjs from 'dayjs';
import { MessagePlugin } from 'tdesign-vue';

const props = defineProps({
  spaceId: {
    type: String,
    default: '',
  },
});
const visible = ref(false);
const formRef = ref(null);
const dataDevelopmentStore = useDataDevelopmentStore();

const formData = reactive({
  name: '',
  code: '',
  startTime: '',
  endTime: '',
  params: [
  ],
});

const rules = {
  startTime: [{ required: true, message: '请选择开始时间' }, { validator: checkStartTime }],
  endTime: [{ required: true, message: '请选择结束时间' }, { validator: checkEndTime }],
};

function checkStartTime(startTime) {
  // 如果结束时间存在且早于开始时间，返回错误
  if (formData.endTime && dayjs(formData.endTime).isBefore(startTime)) {
    return {
      result: false,
      message: '开始时间不能晚于结束时间',
      type: 'error',
    };
  }

  return { result: true };
}

function checkEndTime(endTime) {
  // 如果开始时间存在且晚于结束时间，返回错误
  if (formData.startTime && dayjs(formData.startTime).isAfter(endTime)) {
    return {
      result: false,
      message: '结束时间不能早于开始时间',
      type: 'error',
    };
  }

  return { result: true };
}

function triggerCheckStartTime() {
  if (formData.startTime) {
    formRef.value.validate({ fields: ['startTime'] });
  }
}

function triggerCheckEndTime() {
  if (formData.endTime) {
    formRef.value.validate({ fields: ['endTime'] });
  }
}

function checkParam(item) {
  if (item.prop === '' || item.value === '') {
    return {
      result: false,
      message: '键值不能为空',
      type: 'error',
    };
  }
  const reg = /^[A-Za-z0-9_-]+$/;
  if (!reg.test(item.prop)) {
    return {
      result: false,
      message: '只允许包含英文字母、数字、下划线和中划线',
      type: 'error',
    };
  }

  return {
    result: true,
  };
}

function handleAddParam() {
  formData.params.push({ key: uuid(), prop: '', value: '' });
}
function handleDeleteParam(index) {
  formData.params.splice(index, 1);
}

function handleClose() {
  close();
}
async function handleConfirm() {
  const result = await formRef.value.validate();
  if (result === true) {
    await dataDevelopmentStore.fetchFlowDefineSupplement(props.spaceId, formData.code, {
      startTime: formData.startTime,
      endTime: formData.endTime,
      globalParams: formData.params.map(item => ({
        prop: item.prop,
        value: item.value,
      })),
    });
    close();
    MessagePlugin.success('操作成功');
  }
}

async function open(item) {
  visible.value = true;
  const { globalParams = [], name, code } = item;
  formData.name = name;
  formData.code = code;
  formData.startTime = dayjs().format('YYYY-MM-DD 00:00:00');
  formData.endTime = dayjs().format('YYYY-MM-DD 00:00:00');
  // const { data = {} } = await dataDevelopmentStore.fetchFlowDefineDetail(props.spaceId, formData.code);
  // const { globalParams = [] } = data;
  if (globalParams.length) {
    formData.params = globalParams.map(param => ({ key: uuid(), prop: param.prop, value: param.value }));
  }
}
function close() {
  visible.value = false;
  formData.name = '';
  formData.code = '';
  formData.startTime = '';
  formData.endTime = '';
  formData.params = [];
  formRef.value.reset();
}

const submitDisabled = computed(() => {
  let disabled = false;
  Object.keys(formData).forEach((key) => {
    const data = formData[key];
    if (key === 'params') {
      data.forEach((item) => {
        if (isEmptyValue(item.prop) || isEmptyValue(item.value)) {
          disabled = true;
        }
      });
    } else {
      isEmptyValue(data) && (disabled = true);
    }
  });
  return disabled;
});

defineExpose({ open, close });
</script>

<template>
  <BasicDialog
    width="560"
    :visible.sync="visible"
    header="补录"
    :submitDisabled="submitDisabled"
    @handleClose="handleClose"
    @handleConfirm="handleConfirm"
  >
    <t-form class="des-data-dev__flow-define__re-recorded" :data="formData" ref="formRef" labelWidth="106px" :rules="rules">
      <section>
        <t-form-item label="工作流名称">
          <div>{{ formData.name }}</div>
        </t-form-item>
        <t-form-item label="开始时间" name="startTime">
          <t-date-picker v-model="formData.startTime" style="width: 100%;" enable-time-picker allow-input clearable format="YYYY-MM-DD HH:mm:ss" @change="triggerCheckEndTime"></t-date-picker>
        </t-form-item>
        <t-form-item label="结束时间" name="endTime" class="role-form-item">
          <t-date-picker v-model="formData.endTime" style="width: 100%;" enable-time-picker allow-input clearable format="YYYY-MM-DD HH:mm:ss" @change="triggerCheckStartTime"></t-date-picker>
        </t-form-item>
      </section>
      <section class="global-params-setting">
        <h3>全局变量设置</h3>
        <t-form-item
          v-for="(param, index) in formData.params"
          :key="param.id"
          :label="`全局变量${index + 1}`"
          :name="`params[${index}]`"
          :rules="[{required: true }, { validator: () => checkParam(param) }]">
          <div class="params-row">
            <t-input v-model.trim="param.prop" placeholder="键"></t-input>
            <t-input v-model.trim="param.value" placeholder="值"></t-input>
            <div class="delete-btn" @click="handleDeleteParam(index)">
              <des-icon name="des-icon-shanshu" size="14"></des-icon>
            </div>
          </div>
        </t-form-item>
        <div>
          <t-link hover="color" theme="primary" @click="handleAddParam"><des-icon name="des-icon-jiaxiao" size="14px"></des-icon> 添加全局变量</t-link>
        </div>
      </section>
    </t-form>
  </BasicDialog>
</template>

<style lang="less" scoped>
.des-data-dev__flow-define__re-recorded {
  .params-row {
    display: flex;
    gap: 12px;
    align-items: center;
    .delete-btn {
      width: 24px;
      height: 24px;
      cursor: pointer;
      flex-shrink: 0;
      &:hover {
        color: var(--des-color-theme);
      }
    }
  }
  .global-params-setting {
    margin-top: 24px;
    & > h3 {
      color: #333333;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px;
      margin-bottom: 8px;
    }
    :deep(.t-form__item) {
      padding: 8px 0;
      border-radius: 4px;
      background: #F4F4F4;
      .t-is-error {
        .t-input__extra {
          bottom: -28px;
        }
      }
    }
  }
}
</style>
