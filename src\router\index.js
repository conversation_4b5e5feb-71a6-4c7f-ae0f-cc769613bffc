import Vue from 'vue';
import VueRouter from 'vue-router';
import MasterPage from '@/MasterPage.vue';
import { useCommonStore } from '@/stores/common';
import { useDataDevelopmentStore } from '@/stores/data-development';
import { showDialogConfirm } from '@/utils/dialog';
import { useDataCatalogStore } from '@/stores/data-catalog';
import { useSpacesStore } from '@/stores/spaces';
import emitter, { EVENT_NAMES } from '@/utils/emitter';

Vue.use(VueRouter);

const routes = [
  {
    path: '/',
    component: MasterPage,
    redirect,
    name: 'root',
    children: [
      {
        path: '/workbench',
        name: 'workbench',
        redirect,
        component: () => import(/* webpackChunkName: "workbench" */ '@/views/workbench/MainPage.vue'),
        meta: {
          auth: 'Menu_workbench',
        },
        children: [
          // 数据开发
          {
            path: '/workbench/data-development',
            name: 'dataDevelopment',
            component: () => import(/* webpackChunkName: "data-development" */ '@/views/workbench/data-development/MainPage.vue'),
            meta: {
              auth: 'Menu_dataDev',
            },
          },
          // 数据目录
          {
            path: '/workbench/data-catalog',
            name: 'dataCatalog',
            component: () => import(/* webpackChunkName: "data-catalog" */ '@/views/workbench/data-catalog/MainPage.vue'),
            meta: {
              auth: 'Menu_dataCatalog',
            },
          },
          // 数据接入
          // {
          //   path: '/workbench/data-access',
          //   name: 'dataAccess',
          //   component: () => import(/* webpackChunkName: "data-access" */ '@/views/workbench/data-access/MainPage.vue'),
          // },
          // 数据发布
          // {
          //   path: '/workbench/data-release',
          //   name: 'dataRelease',
          //   component: () => import(/* webpackChunkName: "data-release" */ '@/views/workbench/data-release/MainPage.vue'),
          // },
        ],
      },
      {
        path: '/spaces',
        name: 'spaces',
        component: () => import(/* webpackChunkName: "spaces" */ '@/views/spaces/MainPage.vue'),
        meta: {
          auth: 'Menu_spaceManage',
        },
      },
      {
        path: '/spaces/:type/:id',
        name: 'space',
        component: () => import(/* webpackChunkName: "space" */ '@/views/spaces/detail/MainPage.vue'),
        meta: {
          ignoreRedirect: true,
        },
      },
      // 系统运维
      {
        path: '/operations',
        name: 'operations',
        redirect,
        component: () => import(/* webpackChunkName: "operations" */ '@/views/operations/MainPage.vue'),
        meta: {
          auth: 'Menu_systemOperation',
        },
        children: [
          {
            path: '/operations/resource',
            name: 'resource',
            component: () => import(/* webpackChunkName: "operations-resource" */ '@/views/operations/resource/MainPage.vue'),
            meta: {
              auth: 'Menu_resourceManage',
            },
          },
          {
            path: '/operations/srdata',
            name: 'srdata',
            component: () => import(/* webpackChunkName: "operations-srda" */ '@/views/operations/srdata/MainPage.vue'),
            meta: {
              auth: 'Menu_srDataOperation',
            },
          },
          // SQL工作台
          {
            path: '/operations/sqlworkbench',
            name: 'sqlworkbench',
            component: () => import(/* webpackChunkName: "sql-workbench" */ '@/views/operations/sql-workbench/MainPage.vue'),
            meta: {
              auth: 'Menu_sqlWorkbench',
            },
          },
        ],
      },
      // 任务监控
      {
        path: '/monitor',
        name: 'monitor',
        component: () => import(/* webpackChunkName: "monitor" */ '@/views/monitor/MainPage.vue'),
        redirect,
        meta: {
          auth: 'Menu_taskMonitor',
        },
        children: [
          // 视图任务管理
          {
            path: '/monitor/view-task',
            name: 'viewTask',
            component: () => import(/* webpackChunkName: "view-task" */ '@/views/monitor/view-task/MainPage.vue'),
            meta: {
              auth: 'Menu_viewTask',
            },
          },
          // SQL执行记录
          {
            path: '/monitor/sql-execution',
            name: 'sqlExecution',
            component: () => import(/* webpackChunkName: "sql-execution" */ '@/views/monitor/sql-execution/MainPage.vue'),
            meta: {
              auth: 'Menu_sqlExecRecord',
            },
          },
          // 操作日志
          {
            path: '/monitor/operation-log',
            name: 'operationLog',
            component: () => import(/* webpackChunkName: "operation-log" */ '@/views/monitor/operation-log/MainPage.vue'),
            meta: {
              auth: 'Menu_logManage',
            },
          },
        ],
      },
      {
        path: '/operations/srdata/blacklist',
        name: 'srdataBlacklist',
        component: () => import(/* webpackChunkName: "blacklist" */ '@/views/operations/srdata/blacklist-sql/detail/MainPage.vue'),
        meta: {
          ignoreRedirect: true,
        },
      },
    ],
  },
  // 404页面
  {
    path: '/',
    component: MasterPage,
    children: [
      {
        path: '/401',
        name: 'notAuthorized',
        component: () => import(/* webpackChunkName: "no-auth" */ '@/views/NotAuthorized.vue'),
      },
      {
        path: '*',
        name: 'notFound',
        component: () => import(/* webpackChunkName: "not-found" */ '@/views/NotFound.vue'),
      },
    ],
  },
];
// 通过路由名称找到有权限的子路由
const findAuthRoutes = (routeName, operateCodeList) => {
  // 递归查找
  const list = [];
  const fn = (routes) => {
    for (const route of routes) {
      if (route.name === routeName) {
        if (route.children) {
          for (const child of route.children) {
            if (!child.meta?.auth || operateCodeList.includes(child.meta.auth)) {
              if (!child.meta?.ignoreRedirect) {
                list.push(child);
              }
            }
          }
        }
        return;
      }
      if (route.children) {
        fn(route.children);
      }
    }
  };
  fn(routes);
  return list;
};

function redirect(to) {
  const { name } = to;
  const commonStore = useCommonStore();
  const authRoutes = findAuthRoutes(name, commonStore.operateCodeList);
  if (authRoutes.length) {
    return authRoutes[0].path;
  }
  return '/401';
}

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes,
});

// 新增/编辑工作流直接切换路由时添加提醒
router.beforeEach(async (to, from, next) => {
  const dataDevelopmentStore = useDataDevelopmentStore();
  if (from.name !== 'dataDevelopment') {
    next();
    return;
  }
  const { closeReminder, needCompareNodeData } = dataDevelopmentStore.currentTabPanel;
  if (!closeReminder) {
    next();
    return;
  }

  if (needCompareNodeData) {
    emitter.emit(EVENT_NAMES.WORK_FLOW_PANEL.GRAPH_DATA_COMPARE, { panel: dataDevelopmentStore.currentTabValue });
    if (dataDevelopmentStore.flowDataIsEqual) {
      next();
      return;
    }
  }

  const { type } = await showDialogConfirm({
    title: '确定退出配置吗？',
    body: '退出后，您编辑的内容将不会保存',
    width: '440px',
  });

  if (type === 'success') {
    next();
  } else {
    const spaceStore = useSpacesStore();
    spaceStore.setCurrentSpace(from.query.spaceId);
  }
});
// 切换目标后重置tabPanelList
router.afterEach((to) => {
  if (['dataDevelopment', 'dataCatalog'].includes(to.name)) {
    const storeMap = {
      dataDevelopment: useDataDevelopmentStore(),
      dataCatalog: useDataCatalogStore(),
    };
    storeMap[to.name].resetTabPanelList();
  }
});

// 全局忽略 NavigationDuplicated 错误
const originalPush = router.push;
router.push = function push(location) {
  return originalPush.call(this, location).catch((error) => {
    if (error.name !== 'NavigationDuplicated') {
      throw error;
    }
  });
};

const originalReplace = router.replace;
router.replace = function replace(location) {
  return originalReplace.call(this, location).catch((error) => {
    if (error.name !== 'NavigationDuplicated') {
      throw error;
    }
  });
};

export default router;
