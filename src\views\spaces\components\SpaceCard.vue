<script setup>
import { MessagePlugin } from 'tdesign-vue';
import { ref, defineProps, computed, defineEmits } from 'vue';
import { storeToRefs } from 'pinia';
import { useRouter } from 'vue-router/composables';
import EllipsisWithTooltip from '@/components/EllipsisWithTooltip.vue';
import { AdjustmentIcon, DeleteIcon } from 'tdesign-icons-vue';
import { useCommonStore } from '@/stores/common';
import { useSpacesStore } from '@/stores/spaces';
import { showDialogAlert, showDialogConfirm } from '@/utils/dialog';
// import { useCommonStore } from '@/stores/common';

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
  showMore: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(['refresh']);
const router = useRouter();
const commonStore = useCommonStore();
const { operateCodeList } = storeToRefs(commonStore);
const spacesStore = useSpacesStore();
const { allOrgAreas } = storeToRefs(spacesStore);
// const commonStore = useCommonStore();
// const { staffId } = storeToRefs(commonStore);
const cardRef = ref(null);
const options = computed(() => [
  {
    content: '管理空间',
    value: 'manage',
    prefixIcon: () => <AdjustmentIcon />,
  },
  {
    content: '删除',
    value: 'delete',
    prefixIcon: () => <DeleteIcon />,
    auth: 'Menu_spaceDel',
  },
].filter(item => !item.auth || operateCodeList.value.includes(item.auth)));

function getAreaName(id) {
  return allOrgAreas.value.find(item => `${item.uniqueId}` === id)?.orgArea;
}
async function clickHandler(data, context) {
  // 阻止事件冒泡
  context.e.stopPropagation();
  if (data.value === 'manage') {
    goEdit();
  }
  if (data.value === 'delete') {
    // 先检查是否存在开发数据，如果有就不让删除
    const checkRes = await spacesStore.checkSpaceHasData(props.data.id);
    if (checkRes.data) {
      await showDialogAlert({
        title: `空间「${props.data.name}」已存在开发数据，无法删除`,
      });
    } else {
      const result = await showDialogConfirm({
        title: `确定删除空间「${props.data.name}」吗？`,
        body: () => (<span>删除后数据将<b class="des-common-warn-text">无法恢复</b>，请谨慎操作。</span>),
      });
      if (result.type === 'success') {
        await spacesStore.deleteSpace(props.data.id);
        MessagePlugin.success('删除成功');
        emit('refresh');
      }
    }
  }
};
// const cardState = computed(() => {
//   const { manageRoleList = [], techRoleList = [] } = props.data;
//   const isManage = manageRoleList.some(item => item.staffId === staffId.value);
//   if (isManage) {
//     return 'manage';
//   }
//   const isTech = techRoleList.some(item => item.staffId === staffId.value);
//   if (isTech) {
//     return 'tech';
//   }
//   return 'none';
// });
// const cardText = computed(() => {
//   let text = '';
//   switch (cardState.value) {
//     case 'manage':
//       text = '我的空间';
//       break;
//     case 'tech':
//       text = '已加入';
//       break;
//     case 'none':
//       text = '申请加入';
//       break;
//     default:
//       break;
//   }
//   return text;
// });

function goView() {
  router.push({
    name: 'space',
    params: {
      type: 'view',
      id: props.data.id,
    },
  });
}
function goEdit() {
  router.push({
    name: 'space',
    params: {
      type: 'edit',
      id: props.data.id,
    },
  });
}
</script>

<template>
  <div class="des-space-card" ref="cardRef" @click="goView">
    <div class="des-space-card__contaner">
      <div class="des-space-card__more" v-if="showMore && options.length" @click="(event) => event.stopPropagation()">
        <t-dropdown
          :options="options"
          @click="clickHandler"
          placement="bottom-right"
          :popupProps="{ attach:() => cardRef, overlayClassName: 'des-card-card__more-dropdown-item'}">
          <t-button theme="default" variant="outline" shape="square" size="small">
            <t-icon name="ellipsis" size="16" />
          </t-button>
        </t-dropdown>
      </div>
      <div class="des-space-card__header">
        <div class="des-space-card__header-left">
          <des-rect-icon name="des-icon-kongjianfuzerenbiaoshi" iconSize="32" size="48"></des-rect-icon>
        </div>
        <div class="des-space-card__header-right">
          <div class="des-space-card__title" @click="goView">{{ data.name }}</div>
          <div class="des-space-card__desc"><ellipsis-with-tooltip :text="data.description"></ellipsis-with-tooltip></div>
        </div>
      </div>
      <div class="des-space-card__body">
        <ul>
          <li><span class="label">所属领域</span><ellipsis-with-tooltip :text="getAreaName(data.orgArea?.uniqueId)"></ellipsis-with-tooltip></li>
          <li><span class="label">空间标识</span><ellipsis-with-tooltip :text="data.code"></ellipsis-with-tooltip></li>
          <li><span class="label">负责人</span><ellipsis-with-tooltip :text="(data.manageRoleList || []).map(item => item.staffName).join('、') || '-'"></ellipsis-with-tooltip></li>
        </ul>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.des-space-card {
  position: relative;
  border-radius: 4px;
  border: 1px solid #EEE;
  box-sizing: border-box;
  cursor: pointer;
  .des-space-card__contaner {
    padding: 16px 20px;
  }
  &:hover {
    border-color: #3464E0;
    .des-space-card__more {
      display: block;
    }
  }
  .des-space-card__more {
    display: none;
    position: absolute;
    right: 12px;
    top: 12px;
    width: 24px;
    height: 24px;
    // border-radius: 3px;
    // border: 1px solid #EEE;
  }
  // .des-space-card__more-icon {
  //   width: 100%;
  //   height: 100%;
  //   display: flex;
  //   align-items: center;
  //   justify-content: center;
  // }
  :deep(.des-card-card__more-dropdown-item) {
    .t-dropdown__item {
      color: #666666;
    }
  }
  .des-space-card__header {
    display: flex;
    margin-bottom: 12px;
  }
  .des-space-card__header-left {
    width: 48px;
    height: 48px;
    flex-shrink: 0;
    margin-right: 8px;
  }
  .des-space-card__header-right {
    flex-grow: 1;
    overflow: hidden;
  }
  .des-space-card__title {
    cursor: pointer;
    height: 24px;
    color: #333333;
    font-family: "PingFang SC";
    font-size: 15px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
    margin-bottom: 2px;
    &:hover {
      text-decoration: underline;
    }
  }
  .des-space-card__desc {
    color: #999999;
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
  }
  .des-space-card__body {
    color: #666666;
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    li {
      display: flex;
    }
    .label {
      color: #999999;
      width: 56px;
      flex-shrink: 0;
    }
  }
  .des-space-card__footer {
    display: flex;
    height: 28px;
    padding: 4px 12px;
    justify-content: center;
    align-items: center;
    border-top: 1px dashed #EEEEEE;
    color: #999999;
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    color: #3464E0;
    // &.manage {
    //   color: #999999;
    // }
    // &.tech {
    //   color: #C2D1F6;
    // }
    // &.none {
    //   color: #3464E0;
    // }
  }
}
</style>
