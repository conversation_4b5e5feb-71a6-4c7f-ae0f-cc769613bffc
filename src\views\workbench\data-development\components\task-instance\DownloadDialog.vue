<script setup>
import { downloadFile } from '@/utils/util';
import { useForm } from '@/hooks';
import { ref, defineExpose } from 'vue';
import { MessagePlugin } from 'tdesign-vue';
import BasicDialog from '@/components/BasicDialog.vue';
import { getScheduleLog, getExecuteLog } from '../../utils/task';
import { useRoute } from 'vue-router/composables';

const route = useRoute();
const visible = ref(false);
const initFormData = {
  checked: [],
};
const initFormRules = {
  checked: [{ required: true, message: '请选择日志类型', trigger: 'change' }],
};
const options = ref([
  { label: '执行日志', value: 'execute' },
  { label: '调度日志', value: 'schedule' },
]);
const { form, formRef, rules, validateForm, resetForm } = useForm(initFormData, initFormRules);

const currentRow = ref(null);

function handleClose() {
  close();
  resetForm();
}
function close() {
  visible.value = false;
}
function open(row) {
  currentRow.value = row;
  visible.value = true;
}

async function handleConfirm() {
  const valid = await validateForm();
  if (!valid) return;
  try {
    // 收集所有下载任务
    // eslint-disable-next-line array-callback-return
    const downloadPromises = form.checked.map((item) => {
      if (item === 'execute') {
        return getExecuteLog(currentRow.value, route.query.spaceId);
      }
      if (item === 'schedule') {
        return getScheduleLog(currentRow.value);
      }
    });
    // 等待所有下载完成
    const array = await Promise.all(downloadPromises);
    form.checked.forEach((logType, index) => {
      const data = array[index];
      if (!data) return;
      const blob = new Blob([data], { type: 'text/plain' });
      const url = window.URL.createObjectURL(blob);
      downloadFile(url, `${logType}Log.txt`);
    });
    // 下载完成后关闭弹窗
    close();
    MessagePlugin('success', '操作成功');
  } catch (error) {}
}

defineExpose({ open, close });
</script>

<template>
  <BasicDialog
    width="560"
    :visible.sync="visible"
    header="下载日志"
    confirmButtonText="立即下载"
    :onClosed="handleClose"
    @handleClose="handleClose"
    @handleConfirm="handleConfirm"
  >
    <t-form :data="form" ref="formRef" labelWidth="86px" :rules="rules">
      <t-form-item label="日志类型" name="checked">
        <t-checkbox-group v-model="form.checked" :options="options" name="city"></t-checkbox-group>
      </t-form-item>
    </t-form>
  </BasicDialog>
</template>

<style lang="less" scoped>
</style>
