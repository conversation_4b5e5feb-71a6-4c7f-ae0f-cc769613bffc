<script setup>
import { ref, defineExpose, reactive } from 'vue';
import BasicDialog from '@/components/BasicDialog.vue';
import BlockHeader from '@/components/BlockHeader.vue';
import EllipsisWithTooltip from '@/components/EllipsisWithTooltip.vue';

const visible = ref(false);

const data = reactive({
  name: '',
  types: [],
  perType: 1,
  wechatRobotChatid: '',
  wechatReceivers: [],
  phoneReceivers: [],
  emailReceivers: [],
});

function staffNames(list = []) {
  return list.map(item => item.staffName).join('、');
}

function handleClose() {
  close();
}

function open(item = {}) {
  data.name = item.name;
  data.types = item.types;
  data.perType = item.perType;
  data.wechatRobotChatid = item.wechatRobotChatid;
  data.wechatReceivers = item.wechatReceivers;
  data.phoneReceivers = item.phoneReceivers;
  data.emailReceivers = item.emailReceivers;
  visible.value = true;
}
function reset() {
  data.name = '';
  data.types = [];
  data.perType = 1;
  data.wechatRobotChatid = '';
  data.wechatReceivers = [];
  data.phoneReceivers = [];
  data.emailReceivers = [];
}
function close() {
  visible.value = false;
  reset();
}

defineExpose({ open, close });
</script>

<template>
  <BasicDialog
    width="604"
    :visible.sync="visible"
    :header="data.name"
    :showConfirmButton="false"
    @handleClose="handleClose"
  >
    <div class="des-space__view-alarm">
      <section v-if="data.types?.includes(1)">
        <block-header class="title" title="企微机器人告警配置" />
        <div class="item"><span class="label">人群类型</span><span>{{ data.perType === 1 ? '个人' : '群' }}</span></div>
        <div class="item" v-if="data.perType === 1"><span class="label">接收人</span><ellipsis-with-tooltip :text="staffNames(data.wechatReceivers)"></ellipsis-with-tooltip></div>
        <div class="item" v-if="data.perType === 2"><span class="label">chatid</span><ellipsis-with-tooltip :text="data.wechatRobotChatid"></ellipsis-with-tooltip></div>
      </section>
      <section v-if="data.types?.includes(2)">
        <block-header class="title"  title="电话告警配置" />
        <div class="item"><span class="label">接收人</span><ellipsis-with-tooltip :text="staffNames(data.phoneReceivers)"></ellipsis-with-tooltip></div>
      </section>
      <section v-if="data.types?.includes(3)">
        <block-header class="title"  title="邮件告警配置" />
        <div class="item"><span class="label">接收人</span><ellipsis-with-tooltip :text="staffNames(data.emailReceivers)"></ellipsis-with-tooltip></div>
      </section>
    </div>
  </BasicDialog>
</template>

<style lang="less" scoped>
.des-space__view-alarm {
  section:not(:last-child) {
    margin-bottom: 33px;
  }
  .title {
    margin-bottom: 17px;
  }
  .item {
    display: flex;
    color: #333333;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    &:not(:last-child) {
      margin-bottom: 10px;
    }
    .label {
      width: 72px;
      flex-shrink: 0;
      color: #666666;
      text-align: right;
      margin-right: 8px;
    }
  }
}
</style>
