<script setup>
import { defineAsyncComponent } from 'vue';
import { storeToRefs } from 'pinia';
import { useDataCatalogStore } from '@/stores/data-catalog';
// import { useRoute } from 'vue-router/composables';

const DataCatalog = defineAsyncComponent(() => import('./main-tabs/DataCatalog.vue'));
const DataSourcePanel = defineAsyncComponent(() => import('./main-tabs/DataSourcePanel.vue'));
const DataSourceDetail = defineAsyncComponent(() => import('./main-tabs/DataSourceDetail.vue'));

const dataCatalogStore = useDataCatalogStore();
const { currentTabValue, tabPanelList } = storeToRefs(dataCatalogStore);
// const route = useRoute();

// watch(() => route.query.spaceId, (val) => {
//   if (val) {
//     // 重置tabPanelList
//     dataCatalogStore.resetTabPanelList();
//   }
// });

function removeTab(item) {
  dataCatalogStore.removeTabPanelList(item.value);
}
function changeTab(val) {
  dataCatalogStore.setCurrentTabValue(val);
}
// 组件映射
function getComp(name) {
  let comp = null;
  switch (name) {
    case 'DataCatalog':
      comp = DataCatalog;
      break;
    case 'DataSourcePanel':
      comp = DataSourcePanel;
      break;
    case 'DataSourceDetail':
      comp = DataSourceDetail;
      break;
    default:
      break;
  }
  return comp;
}
// 属性保存对象，用于子组件触发tab切换
const compPorpsMap = {
  DataCatalog: {},
  DataSourcePanel: {},
  DataSourceDetail: {},
};
function switchTab(tabName = '', props = {}) {
  if (tabName) {
    compPorpsMap[tabName] = props;
    changeTab(tabName);
  }
}
</script>

<template>
  <div class="page-data-catalog__main-content">
    <t-tabs
      :value="currentTabValue"
      theme="card"
      @remove="removeTab"
      @change="changeTab"
      style="width: 100%;height:100%;"
    >
      <t-tab-panel
        v-for="data in tabPanelList"
        class="page-data-catalog__main-content__tab"
        :key="data.value"
        :value="data.value"
        :label="data.label"
        :removable="data.removable"
        :destroyOnHide="false"
      >
        <component :is="getComp(data.component)" @switch="switchTab" @remove="removeTab" v-bind="{ ...data.props, ...compPorpsMap[data.value] }" />
      </t-tab-panel>
    </t-tabs>
  </div>
</template>

<style lang="less" scoped>
.page-data-catalog__main-content {
  height: 100%;
  :deep(.t-tabs__nav--card) {
    border-radius: 4px 4px 0 0;
  }
  & > :deep(.t-tabs > .t-tabs__header ) {
    .t-tabs__nav-item {
      height: 38px;
      line-height: 38px;
    }
    .t-tabs__nav--card.t-tabs__nav-item.t-is-active {
      color: #2A2A2A;
    }
  }
  & > :deep(.t-tabs > .t-tabs__header > .t-tabs__nav) {
    .t-tabs__nav-scroll {
      background: #EBF0F7
    };
  }
  :deep(.t-tabs__content) {
    height: calc(100% - 38px);
  }
  .page-data-catalog__main-content__tab {
    height: 100%;
  }
}
</style>
