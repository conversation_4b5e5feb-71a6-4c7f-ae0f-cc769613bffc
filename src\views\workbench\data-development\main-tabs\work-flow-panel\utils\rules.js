import { isEmptyValue } from '@/utils/util';
import { STARROCKS_MATERIALIZED_VIEW, SUBPROCESS, STARROCKS_SQL, BIDSPLUS, JDBC_SQL, HTTP, MESSAGE } from './task-type';

// 公共字段验证规则
const BASE_FIELDS = ['name', 'failRetryTimes', 'failRetryInterval', 'delayTime', 'resourceGroupId', 'minCpuCores', 'minMemorySpace'];
const TIMEOUT_FIELDS = ['timeout', 'timeoutFailed'];

// 流程节点表单公共规则
export const commonRules = {
  name: [{ required: true, message: '请输入节点名称' }],
  code: [{ required: true, message: '请输入节点标识' }],
  failRetryTimes: [{ required: true, message: '请输入失败重试次数' }],
  failRetryInterval: [{ required: true, message: '请输入失败重试间隔' }],
  delayTime: [{ required: true, message: '请输入延迟执行' }],
  resourceGroupId: [{ required: true, message: '请选择资源标识' }],
  minCpuCores: [{ required: true, message: '请输入cpu' }],
  minMemorySpace: [{ required: true, message: '请输入内存' }],
  timeoutFlag: [{ required: true, message: '请选择超时告警开关' }],
  timeout: [{ required: true, message: '请输入超时时长' }],
  timeoutFailed: [{ required: true, message: '请选择超时是否失败' }],
};

// sr物化视图规则
export const srMaterializedViewRules = {
  ...commonRules,
  'taskProperties.srDataSourceId': [{ required: true, message: '请选择starrocks集群' }],
  'taskProperties.materializedViewName': [{ required: true, message: '请选择物化视图' }],
};
// srSql规则
export const srSqlRules = {
  ...commonRules,
  'taskProperties.srDataSourceId': [{ required: true, message: '请选择starrocks集群' }],
  'taskProperties.scriptType': [{ required: true, message: '请选择脚本来源' }],
  'taskProperties.scriptContent': [{ required: true, message: '请输入SQL语句' }],
  'taskProperties.gitProjectId': [{ required: true, message: '请选择git项目' }],
  'taskProperties.gitFile': [{ required: true, message: '请选择文件' }],
};
// subProcess规则
export const subProcessRules = {
  ...commonRules,
  'taskProperties.processDefinitionName': [{ required: true, message: '请选择子节点' }],
};
// bidsPlus规则
export const bidsPlusRules = {
  ...commonRules,
  'taskProperties.bidsSpaceId': [{ required: true, message: '请选择管道任务所属应用' }],
  'taskProperties.taskCode': [{ required: true, message: '请输入发布/订阅任务编码' }],
  'taskProperties.version': [{ required: true, message: '请选择管道任务版本' }],
};
// jdbcSql规则
export const jdbcSqlRules = {
  ...commonRules,
  'taskProperties.datasourceType': [{ required: true, message: '请选择业务数据源类型' }],
  'taskProperties.datasourceId': [{ required: true, message: '请选择业务数据源实例' }],
  'taskProperties.scriptType': [{ required: true, message: '请选择脚本来源' }],
  'taskProperties.scriptContent': [{ required: true, message: '请输入SQL语句' }],
  'taskProperties.gitProjectId': [{ required: true, message: '请选择git项目' }],
  'taskProperties.gitFile': [{ required: true, message: '请选择文件' }],
};
// http规则
export const httpRules = {
  ...commonRules,
  'taskProperties.url': [{ required: true, message: '请输入请求地址' }],
  'taskProperties.requestMethod': [{ required: true, message: '请选择请求类型' }],
  'taskProperties.resultCheckType': [{ required: true, message: '请选择返回值判断' }],
  'taskProperties.resultCheckValue': [{ required: true }],
  'taskProperties.connectTimeout': [{ required: true, message: '请输入连接超时值' }],
  'taskProperties.socketTimeout': [{ required: true, message: '请输入Socket超时值' }],
};
// 消息通知规则
export const messageRules = {
  ...commonRules,
};
// 验证基础字段
function validateBaseFields(data) {
  return BASE_FIELDS.every(field => !isEmptyValue(data[field]));
}

// 验证超时相关字段
function validateTimeoutFields(data) {
  return !data.timeoutFlag || TIMEOUT_FIELDS.every(field => !isEmptyValue(data[field]));
}

// 通用数据验证逻辑
export function validateTaskData(data = {}, requiredProps = []) {
  if (!validateBaseFields(data) || !validateTimeoutFields(data)) {
    return false;
  }
  const { taskProperties = {} } = data;
  return requiredProps.every(prop => !isEmptyValue(taskProperties[prop]));
}

// 物化视图数据校验逻辑
export function validateMaterializedViewData(data = {}) {
  return validateTaskData(data, ['srDataSourceId', 'materializedViewName']);
}
// 子任务校验逻辑
export function validateSubProcessData(data = {}) {
  return validateTaskData(data, ['processDefinitionName']);
}
// StarrocksSql校验逻辑
export function validateStarrocksSqlData(data = {}) {
  const { taskProperties = {} } = data;
  if (!taskProperties.scriptType) return false;

  const requiredFields = taskProperties.scriptType === 'CONTENT'
    ? ['srDataSourceId', 'scriptContent']
    : ['srDataSourceId', 'gitProjectId', 'gitFile'];

  return validateTaskData(data, requiredFields);
}
// BIDSPlus校验逻辑
export function validateBIDSPlusData(data = {}) {
  return validateTaskData(data, ['bidsSpaceId', 'taskCode', 'version']);
}
// JDBCSql校验逻辑
export function validateJDBCSqlData(data = {}) {
  const { taskProperties = {} } = data;
  if (!taskProperties.scriptType) return false;

  const requiredFields = taskProperties.scriptType === 'CONTENT'
    ? ['datasourceType', 'datasourceId', 'scriptContent']
    : ['datasourceType', 'datasourceId', 'gitProjectId', 'gitFile'];

  return validateTaskData(data, requiredFields);
}
// Http校验逻辑
export function validateHttpData(data = {}) {
  return validateTaskData(data, ['url', 'requestMethod', 'resultCheckType', 'resultCheckValue', 'connectTimeout', 'socketTimeout']);
}
// 消息通知校验逻辑
export function validateMessageData(data = {}) {
  // 待添加taskProperties字段的校验逻辑
  return validateTaskData(data);
}
// 无界面的节点数据校验
export function validateList(list = []) {
  const validatorMap = {
    [STARROCKS_MATERIALIZED_VIEW]: validateMaterializedViewData,
    [SUBPROCESS]: validateSubProcessData,
    [STARROCKS_SQL]: validateStarrocksSqlData,
    [BIDSPLUS]: validateBIDSPlusData,
    [JDBC_SQL]: validateJDBCSqlData,
    [HTTP]: validateHttpData,
    [MESSAGE]: validateMessageData,
  };
  for (let i = 0; i < list.length; i++) {
    const item = list[i];
    const { taskType } = item;
    if (validatorMap[taskType] && !validatorMap[taskType](item)) {
      return {
        result: false,
        // eslint-disable-next-line no-underscore-dangle
        nodeId: item._id,
        nodeName: item.name,
      };
    }
  }
  return {
    result: true,
  };
}
