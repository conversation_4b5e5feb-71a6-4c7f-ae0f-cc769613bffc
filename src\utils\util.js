import dayjs from 'dayjs';

/**
 * digger手动埋点
 * @param key 埋点事件名
 * @param params 埋点参数
 * @example 点击 diggerEvent('click:xxx页面', { event: 'xxx'});
 * @example 访问 diggerEvent('view:xxx页面', { source: 'xxx页面' });
 * @example diggerEvent('stay:xxx页面', { duration: number });
 */
export function diggerEvent(key, params) {
  // 埋点规范示例
  // 点击(event具体的点击事件)
  // diggerEvent('click:xxx页面', { event: 'xxx' });
  // // 访问，source表示来源，如果无指定来源可填default
  // diggerEvent('view:xxx页面', { source: 'xxx页面' });
  // // 停留时间
  // diggerEvent('stay:xxx页面', { duration: number });

  // 自动添加语言参数
  let data = {};
  if (params) {
    data = {
      ...data,
      ...params,
    };
  }
  console.log(key, data);
  if (window.digger) {
    window.digger.event(key, data, { beacon: false, mta: false });
  }
}

/**
 * 设置document的title
 * @param {string} title
 */
export function setTitle(title) {
  if (title) {
    document.title = title;
  }
}

// 判断非引用类型值是否是空
export function isEmptyValue(val) {
  return val === undefined || val === null || val === '';
}

/**
 * 字符串匹配功能高亮功能
 * @param keyword
 * @param text
 * @returns {string}
 */
export function highlightMatchedText(keyword, text) {
  if (typeof keyword !== 'string' || typeof text !== 'string') {
    throw new Error('Invalid arguments: keyword and text must be strings');
  }
  const lowercaseKeyword = keyword.toLowerCase();
  const lowercaseText = text.toLowerCase();
  const startIndex = lowercaseText.indexOf(lowercaseKeyword);
  const { length } = keyword;
  if (startIndex === -1) {
    return text;
  }
  const matchedWord = text.substring(startIndex, startIndex + length);
  const textRes = text.replaceAll(matchedWord, `<b>${matchedWord}</b>`);
  return textRes;
}

/**
 * 文件下载
 * @param {string} url 文件地址
 * @param {string}} name 文件名
 */
export const downloadFile = function (url, name) {
  if (!url) return;
  let aTag = document.createElement('a');
  aTag.href = url;
  aTag.download = name || new Date().getTime();
  aTag.click();
  aTag = null;
};

/**
 * 获取文字宽度
 * @param {string} text 文字
 * @param {object}} options 文字相关配置
 */
export const getTextWidth = function (text, options = {}) {
  if (!text) return 0;
  const { size = 14, family = 'PingFang SC' } = options;
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  ctx.font = `${size}px ${family}`;
  const metrics = ctx.measureText(text);
  return metrics.width;
};

export const to = promise => promise
  .then(res => [null, res.data])
  .catch(err => [err, null]);

/**
 * 生成uuid credit: https://github.com/antvis/X6/blob/master/packages/x6-common/src/string/uuid.ts
 * @returns {string} uuid
 */
export function uuid() {
  let res = '';
  const template = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx';

  for (let i = 0, len = template.length; i < len; i += 1) {
    const s = template[i];
    const r = (Math.random() * 16) | 0;
    // eslint-disable-next-line no-nested-ternary
    const v = s === 'x' ? r : s === 'y' ? (r & 0x3) | 0x8 : s;
    res += v.toString(16);
  }
  return res;
}

/**
 * 找出重复的value
 * @param {array} arr 任意数组
 */
export const findDuplicates = (arr) => {
  const duplicates = [];
  const uniqueValues = new Set();

  arr.forEach((value) => {
    if (uniqueValues.has(value)) {
      duplicates.push(value);
    } else {
      uniqueValues.add(value);
    }
  });

  return duplicates;
};

/**
 * 格式化时间
 * @param {Date} time 时间
 * @param {string} formatter 时间格式
 */
export const formatTime = (time, formatter = 'YYYY-MM-DD HH:mm:ss') => (time ? dayjs(time).format(formatter) : '-');

/**
 * 秒转换成时分秒
 * @param {number} seconds 秒
 * @returns {string} 时间
 */
export const secondConvertTime = (totalSeconds) => {
  const dur = dayjs.duration(totalSeconds, 'seconds');
  const days = dur.days();
  const hours = dur.hours();
  const hour = (days * 24 + hours).toString().padStart(2, '0');
  const minutes = dur.minutes().toString()
    .padStart(2, '0');
  const seconds = dur.seconds().toString()
    .padStart(2, '0');
  return `${hour}:${minutes}:${seconds}`;
};

/**
 * 判断是否是safari
 * @returns {boolean} 是否是safari
 */
export function isSafari() {
  const { userAgent } = window.navigator;
  return /^((?!chrome|android).)*safari/i.test(userAgent);
}
