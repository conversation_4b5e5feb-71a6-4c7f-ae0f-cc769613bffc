<template>
  <t-select
    :value="value"
    :options="options"
    v-bind="{ ...$attrs }"
    :style="style"
    :class="{ 'basic-datasource__select': <PERSON><PERSON><PERSON>(value) }"
    @change="handleChange"
  >
    <template #valueDisplay="{ value }" v-if="value">
      <div class="choose-wrap">
        <des-icon :name="icon" size="16"></des-icon>
        <span style="margin-left: 8px">{{ getOptionLabel(value) }}</span>
      </div>
    </template>
  </t-select>
</template>

<script setup>
import { isFunction } from 'lodash';
import { defineProps, defineEmits, useAttrs, computed } from 'vue';

const $attrs = useAttrs();

const props = defineProps({
  value: {
    type: String,
    default: '',
  },
  options: {
    type: Array,
    default: () => [],
  },
  width: {
    type: String,
    default: '200px',
  },
  icon: {
    type: String,
    default: 'des-micon-shujuyuan',
  },
  confirm: {
    type: Function,
    default: undefined,
  },
});

const emit = defineEmits(['input', 'change']);

const style = computed(() => {
  const obj = {};
  obj.width = props.width;
  return obj;
});

const handleChange = async (value) => {
  if (isFunction(props.confirm)) {
    const result = await props.confirm();
    if (!result) return;
    emit('input', value);
    emit('change', value);
    return;
  }
  emit('input', value);
  emit('change', value);
};

const getOptionLabel = value => props.options.find(item => item.value === value)?.label;
</script>

<style lang="less" scoped>
.basic-datasource__select {
  :deep(.t-input__wrap) {
    .t-input.t-is-readonly {
      color: var(--des-color-theme);
      background: #ebf0f7;
    }
  }
  .choose-wrap {
    display: flex;
    align-items: center;
  }
}
</style>
