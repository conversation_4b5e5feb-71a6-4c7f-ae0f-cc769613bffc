// 待补充统一的提示弹窗
export function useLeaveDiff(getData) {
  let saveData = '';
  function save() {
    const data = getData();
    saveData = JSON.stringify(data);
  }
  function same() {
    const data = getData();
    return JSON.stringify(data) === saveData;
  }
  function leave() {
    // return new Promise((resolve) => {
    //   if (same()) {
    //     resolve(true);
    //   } else {
    //     turnback().then(() => {
    //       resolve(true);
    //     });
    //   }
    // });
  }
  return {
    save,
    leave,
    same,
  };
};
