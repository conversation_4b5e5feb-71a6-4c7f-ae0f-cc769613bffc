/* eslint-disable no-param-reassign */
import { useCommonStore } from '@/stores/common';
// 权限指令，只能在样式控制上使用，不建议使用，通常情况下更建议使用BaseAuth组件进行控制

export default {
  bind(el, binding) {
    const store = useCommonStore();
    // 如果权限码不在列表中，则隐藏元素
    if (!store.operateCodeList.includes(binding.value)) {
      el.style.display = 'none';
    }
  },
  updated(el, binding) {
    const store = useCommonStore();
    // 根据权限码更新元素显示状态
    if (store.operateCodeList.includes(binding.value)) {
      el.style.display = '';
    } else {
      el.style.display = 'none';
    }
  },
};
