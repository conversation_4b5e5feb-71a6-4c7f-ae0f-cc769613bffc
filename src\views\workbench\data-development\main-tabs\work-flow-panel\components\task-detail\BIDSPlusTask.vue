<script setup>
import { BIDSPLUS } from '../../utils/task-type.js';
import { bidsPlusRules } from '../../utils/rules.js';
import { reactive, ref, defineExpose, onMounted, defineProps } from 'vue';
import { useDataDevelopmentStore } from '@/stores/data-development';
import BlockHeader from '@/components/BlockHeader.vue';
import BasicConfig from './common/BasicConfig.vue';
import ResourceConfig from './common/ResourceConfig.vue';
import AlarmConfig from './common/AlarmConfig.vue';
import PreTask from './common/PreTask.vue';
import { MessagePlugin } from 'tdesign-vue';

defineProps({
  currentFlowName: {
    type: String,
    default: '',
  },
  readonly: {
    type: Boolean,
    default: false,
  },
});

const { fetchFlowDefineBidsApps, fetchFlowDefineGetTaskByCode } = useDataDevelopmentStore();

const formRef = ref(null);
const formData = reactive({
  taskType: BIDSPLUS,
  id: '',
  // 节点名称
  name: '',
  // 节点标识
  code: '',
  // 节点描述
  description: '',
  // 失败重试次数
  failRetryTimes: 0,
  // 失败重试间隔
  failRetryInterval: 0,
  // 延迟执行
  delayTime: 0,
  // 自定义参数
  // params: [
  //   { key: '1', name: '参数1', relation: 'in', type: 'string', parameter: 'value' },
  // ],
  // 运行集群
  resourceGroupId: '',
  // cpu
  minCpuCores: 0.5,
  // 内存
  minMemorySpace: 512,
  // 超时告警开关
  timeoutFlag: false,
  // 超时时长
  timeout: 0,
  // 超时是否失败
  timeoutFailed: false,
  taskProperties: {
    // 管道任务所属应用
    bidsSpaceId: '',
    // 管道任务
    taskCode: '',
    // 管道任务版本
    version: '',
    // 选择管道任务版本时所得
    configId: '',
  },
});

const nodeId = ref('');

function getData() {
  return {
    taskType: formData.taskType,
    id: formData.id,
    name: formData.name,
    code: formData.code,
    description: formData.description,
    failRetryTimes: formData.failRetryTimes,
    failRetryInterval: formData.failRetryInterval,
    delayTime: formData.delayTime,
    resourceGroupId: formData.resourceGroupId,
    minCpuCores: formData.minCpuCores,
    minMemorySpace: formData.minMemorySpace,
    timeoutFlag: formData.timeoutFlag,
    timeout: formData.timeout,
    timeoutFailed: formData.timeoutFailed,
    taskProperties: {
      bidsSpaceId: formData.taskProperties.bidsSpaceId,
      taskCode: formData.taskProperties.taskCode,
      version: formData.taskProperties.version,
      configId: formData.taskProperties.configId,
    },
  };
}

async function save() {
  const result = await formRef.value.validate();
  if (result !== true) {
    return;
  }
  const data = getData();
  return {
    nodeId: nodeId.value,
    data,
  };
}
// function reset() {
//   formRef.value.reset();
// }
function setData(nodeid, data) {
  nodeId.value = nodeid;
  Object.keys(data).forEach((key) => {
    formData[key] = data[key];
  });
}
defineExpose({
  setData,
  // reset,
  save,
});

const bidsAppOptions = ref([]);
const getBidsApps = async () => {
  const { data } = await fetchFlowDefineBidsApps();
  bidsAppOptions.value = data.map(item => ({
    label: item.appName,
    value: item.appName,
  }));
};

const loading = ref(false);
const taskVersionOptions = ref([]);
const getTaskVersions = async (visible) => {
  if (!visible) return;
  const fields = ['taskProperties.bidsSpaceId', 'taskProperties.taskCode'];
  const result = await formRef.value.validate({ fields });
  if (result !== true) {
    return MessagePlugin('warning', '请选择所属应用和输入管道任务');
  }
  if (taskVersionOptions.value.length) {
    return;
  }
  loading.value = true;
  const { bidsSpaceId, taskCode } = formData.taskProperties;
  const { data } = await fetchFlowDefineGetTaskByCode({ appName: bidsSpaceId, taskCode });
  taskVersionOptions.value = data.map(item => ({
    label: item.version,
    value: item.version,
    configId: item.configId,
  }));
  loading.value = false;
};

const clearTaskVersion = () => {
  taskVersionOptions.value = [];
  Object.assign(formData.taskProperties, { version: '', configId: '' });
};

const versionChange = (version) => {
  const target = taskVersionOptions.value.find(item => item.value === version);
  Object.assign(formData.taskProperties, { configId: target.configId });
};

onMounted(() => {
  getBidsApps();
});
</script>

<template>
  <div class="des-workflow-job-form__bids-plus-task">
    <t-form :data="formData" ref="formRef" label-width="132px" :rules="bidsPlusRules" :disabled="readonly">
      <basic-config :formData="formData"></basic-config>
      <resource-config :formData="formData"></resource-config>
      <alarm-config :formData="formData"></alarm-config>
      <section>
        <block-header title="更多设置" style="margin-bottom: 12px;" />
        <t-form-item label="管道任务所属应用" name="taskProperties.bidsSpaceId">
          <t-select v-model="formData.taskProperties.bidsSpaceId" :options="bidsAppOptions" filterable placeholder="请选择" @change="clearTaskVersion" style="width: 374px;"></t-select>
        </t-form-item>
        <t-form-item label="管道任务编码" name="taskProperties.taskCode">
          <t-input v-model="formData.taskProperties.taskCode" placeholder="请输入发布/订阅任务编码" @change="clearTaskVersion" style="width: 374px;"></t-input>
        </t-form-item>
        <t-form-item label="管道任务版本" name="taskProperties.version">
          <t-select v-model="formData.taskProperties.version" :options="taskVersionOptions" @change="versionChange" filterable :loading="loading" empty="请选择所属应用和输入管道任务后获取" loading-text="数据加载中" placeholder="请选择" style="width: 374px;" @popup-visible-change="getTaskVersions"></t-select>
        </t-form-item>
      </section>
      <section>
        <block-header title="任务关联" style="margin-bottom: 12px;" />
        <!-- 前置任务 -->
        <pre-task :currentId="formData._id" :currentFlowName="currentFlowName"></pre-task>
      </section>
    </t-form>
  </div>
</template>
