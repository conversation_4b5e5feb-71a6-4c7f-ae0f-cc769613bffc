import { defineStore, acceptHMRUpdate } from 'pinia';
import axios from '@/server/client';
import {
  API_DATA_DEVELOPMENT_FLOW_DEFINE_LIST,
  API_DATA_DEVELOPMENT_FLOW_DEFINE_SAVE_DRAFT,
  API_DATA_DEVELOPMENT_FLOW_DEFINE_CREATE,
  API_DATA_DEVELOPMENT_FLOW_DEFINE_UPDATE,
  API_DATA_DEVELOPMENT_FLOW_DEFINE_DELETE,
  API_DATA_DEVELOPMENT_FLOW_DEFINE_RUN,
  API_DATA_DEVELOPMENT_FLOW_DEFINE_PUBLISH,
  API_DATA_DEVELOPMENT_FLOW_DEFINE_BATCH_PUBLISH,
  API_DATA_DEVELOPMENT_FLOW_DEFINE_OFFLINE,
  API_DATA_DEVELOPMENT_FLOW_DEFINE_BATCH_OFFLINE,
  API_DATA_DEVELOPMENT_FLOW_DEFINE_DETAIL,
  API_DATA_DEVELOPMENT_FLOW_DEFINE_DETAIL_BY_NAME,
  API_DATA_DEVELOPMENT_FLOW_DEFINE_VERSION_SWITCH,
  API_DATA_DEVELOPMENT_FLOW_DEFINE_VERSION_LIST,
  API_DATA_DEVELOPMENT_FLOW_DEFINE_VERSION,
  API_DATA_DEVELOPMENT_FLOW_DEFINE_AUTH_GRANTORS,
  API_DATA_DEVELOPMENT_FLOW_DEFINE_AUTH_GRANT,
  API_DATA_DEVELOPMENT_FLOW_DEFINE_AUTH_BATCH_GRANT,
  API_DATA_DEVELOPMENT_FLOW_DEFINE_AUTH_REVOKE,
  API_DATA_DEVELOPMENT_FLOW_DEFINE_SUPPLEMENT,
  API_DATA_DEVELOPMENT_FLOW_DEFINE_LOCK,
  API_DATA_DEVELOPMENT_FLOW_DEFINE_UNLOCK,
  API_DATA_DEVELOPMENT_FLOW_DEFINE_BIDS_APPS,
  API_DATA_DEVELOPMENT_FLOW_DEFINE_GET_TASK_BY_CODE,
  API_DATA_DEVELOPMENT_FLOW_DEFINE_IMPORT,
  API_DATA_DEVELOPMENT_FLOW_DEFINE_BATCH_PUBLISH_RESULT,
  API_DATA_DEVELOPMENT_FLOW_TREE,
  API_DATA_DEVELOPMENT_FLOW_TREE_PROCESS,
  API_DATA_DEVELOPMENT_FLOW_TREE_TASK,
  API_DATA_DEVELOPMENT_FLOW_TIMER_ONLINE,
  API_DATA_DEVELOPMENT_FLOW_TIMER_OFFLINE,
  API_DATA_DEVELOPMENT_FLOW_TIMER_GENERAL,
  API_DATA_DEVELOPMENT_FLOW_TIMER_DELETE,
  API_DATA_DEVELOPMENT_GIT_MANAGER_LIST,
  API_DATA_DEVELOPMENT_GIT_MANAGER_OPERATE,
  API_DATA_DEVELOPMENT_GIT_MANAGER_BRANCHES,
  API_DATA_DEVELOPMENT_GIT_MANAGER_BUILD,
  API_DATA_DEVELOPMENT_GIT_MANAGER_CHECK,
  API_DATA_DEVELOPMENT_GIT_MANAGER_GRANTORS,
  API_DATA_DEVELOPMENT_GIT_MANAGER_GRANT,
  API_DATA_DEVELOPMENT_GIT_MANAGER_REVOKE,
  API_DATA_DEVELOPMENT_GIT_MANAGER_CHECK_NAME,
  API_DATA_DEVELOPMENT_GIT_MANAGER_FILE_PATH,
  API_DATA_DEVELOPMENT_GIT_MANAGER_FILE_CONTENT,
  API_DATA_DEVELOPMENT_FLOW_INSTANCE_LIST,
  API_DATA_DEVELOPMENT_FLOW_INSTANCE_LOG,
  API_DATA_DEVELOPMENT_FLOW_INSTANCE_POD_LOG,
  API_DATA_DEVELOPMENT_FLOW_INSTANCE_OPERATE,
  API_DATA_DEVELOPMENT_FLOW_INSTANCE_DETAIL,
  API_DATA_DEVELOPMENT_FLOW_INSTANCE_SUB_INSTANCE_INFO,
  API_DATA_DEVELOPMENT_TASK_INSTANCE_LIST,
  API_DATA_DEVELOPMENT_TASK_INSTANCE_LOG,
  API_DATA_DEVELOPMENT_TASK_INSTANCE_POD_LOG,
  API_DATA_DEVELOPMENT_TASK_INSTANCE_OPERATE,
} from '@/server/api/data-development';
import emitter, { EVENT_NAMES } from '@/utils/emitter';

export const useDataDevelopmentStore = defineStore('data-development', {
  state: () => ({
    currentTabValue: 'DataDevelopment',
    tabPanelList: [
      {
        label: '数据开发',
        value: 'DataDevelopment',
        component: 'DataDevelopment',
        removable: false,
        props: {},
      },
    ],
    flowDataIsEqual: false,
  }),
  actions: {
    setCurrentTabValue(value) {
      this.currentTabValue = value;
    },
    addTabPanelList(tabPanel) {
      const valueList = this.tabPanelList.map(item => item.value);
      if (tabPanel.value && !valueList.includes(tabPanel.value)) {
        this.tabPanelList.push(tabPanel);
        return;
      }
      if (tabPanel.props.defaultSelectNodeId) {
        setTimeout(() => {
          emitter.emit(EVENT_NAMES.WORK_FLOW_PANEL.GRAPH_NODE_SELECT, { defaultSelectNodeId: tabPanel.props.defaultSelectNodeId, panel: tabPanel.value });
        }, 100);
      }
    },
    removeTabPanelList(val, childPanel) {
      const index = this.tabPanelList.findIndex(item => item.value === val);
      const deleteItem = this.tabPanelList.splice(index, 1);
      // 如果删除的是当前聚焦的内容，需要切换到数据开发页面
      if (this.currentTabValue === deleteItem[0].value) {
        // 回到数据开发页面
        this.currentTabValue = 'DataDevelopment';
        // 切换子面板
        emitter.emit(EVENT_NAMES.DATA_DEVELOPMENT.CHANGE_TAB, childPanel);
      } else {
        // 否则需要重新聚焦一个tab
        const lastIndex = this.tabPanelList.length - 1;
        this.currentTabValue = this.tabPanelList[lastIndex].value;
      }
    },
    resetTabPanelList() {
      this.tabPanelList = [
        {
          label: '数据开发',
          value: 'DataDevelopment',
          component: 'DataDevelopment',
          removable: false,
          props: {},
        },
      ];
      this.currentTabValue = 'DataDevelopment';
    },
    setFlowDataIsEqual(value) {
      this.flowDataIsEqual = value;
    },
    // 查看工作流
    goViewFlow(data = {}) {
      const newTab = {
        label: `查看工作流 ${data.name}`,
        value: `ViewFlow_${data.id}`,
        component: 'WorkFlowPanel',
        removable: true,
        childPanel: 'FlowDefine', // 子面板
        props: {
          id: data.id,
          name: data.name,
          code: data.code,
          readonly: true,
        },
      };
      this.addTabPanelList(newTab);
      this.setCurrentTabValue(newTab.value);
    },
    // 查看工作流实例
    goViewFlowInstance(data = {}) {
      const newTab = {
        label: `查看工作流实例 ${data.processInstanceId}`,
        value: `ViewInstance_${data.processInstanceId}`,
        component: 'WorkFlowPanel',
        removable: true,
        childPanel: 'FlowInstance', // 子面板
        props: {
          id: data.id,
          name: data.name,
          code: data.code,
          readonly: true,
          processInstanceId: data.processInstanceId,
          processInstanceState: data.processInstanceState,
        },
      };
      this.addTabPanelList(newTab);
      this.setCurrentTabValue(newTab.value);
    },
    // 编辑工作流
    goEditFlow(data = {}, props = {}) {
      const newTab = {
        label: `编辑工作流 ${data.name}`,
        value: `EditFlow_${data.id}`,
        component: 'WorkFlowPanel',
        removable: true,
        childPanel: 'FlowDefine', // 子面板
        closeReminder: true, // 关闭提醒
        needCompareNodeData: true, // 需要比较节点数据
        props: {
          id: data.id,
          name: data.name,
          code: data.code,
          isDraft: data.publishState === 'DRAFT',
          ...props,
        },
      };
      this.addTabPanelList(newTab);
      this.setCurrentTabValue(newTab.value);
    },

    // 查询工作流列表
    fetchFlowDefineList(spaceId, data, options = {}) {
      return axios({
        method: 'post',
        url: API_DATA_DEVELOPMENT_FLOW_DEFINE_LIST(spaceId),
        data,
        ...options,
      });
    },
    // 保存草稿
    saveFlowDefineDraft(spaceId, code, data = {}) {
      return axios({
        method: 'post',
        url: API_DATA_DEVELOPMENT_FLOW_DEFINE_SAVE_DRAFT(spaceId, code),
        data,
      });
    },
    // 创建工作流
    createFlowDefine(spaceId, data = {}) {
      return axios({
        method: 'post',
        url: API_DATA_DEVELOPMENT_FLOW_DEFINE_CREATE(spaceId),
        data,
      });
    },
    // 更新/保存工作流
    updateFlowDefine(spaceId, code, data = {}) {
      return axios({
        method: 'put',
        url: API_DATA_DEVELOPMENT_FLOW_DEFINE_UPDATE(spaceId, code),
        data,
      });
    },
    // 删除工作流
    deleteFlowDefine(spaceId, code) {
      return axios({
        method: 'delete',
        url: API_DATA_DEVELOPMENT_FLOW_DEFINE_DELETE(spaceId, code),
      });
    },
    // 根据code查询工作流详情
    fetchFlowDefineDetail(spaceId, code, options = {}) {
      return axios({
        method: 'get',
        url: API_DATA_DEVELOPMENT_FLOW_DEFINE_DETAIL(spaceId, code),
        ...options,
      });
    },
    // 根据name查询工作流详情
    fetchFlowDefineDetailByName(spaceId, params) {
      return axios({
        method: 'get',
        url: API_DATA_DEVELOPMENT_FLOW_DEFINE_DETAIL_BY_NAME(spaceId),
        params,
      });
    },
    // 运行工作流
    runFlowDefine(spaceId, code, data = {}) {
      return axios({
        method: 'post',
        url: API_DATA_DEVELOPMENT_FLOW_DEFINE_RUN(spaceId, code),
        data,
      });
    },
    // 发布工作流
    publishFlowDefine(spaceId, code, data = {}) {
      return axios({
        method: 'post',
        url: API_DATA_DEVELOPMENT_FLOW_DEFINE_PUBLISH(spaceId, code),
        data,
      });
    },
    // 批量发布工作流
    batchPublishFlowDefine(spaceId, data = {}) {
      return axios({
        method: 'post',
        url: API_DATA_DEVELOPMENT_FLOW_DEFINE_BATCH_PUBLISH(spaceId),
        data,
      });
    },
    // 下线工作流
    offlineFlowDefine(spaceId, code, data = {}) {
      return axios({
        method: 'post',
        url: API_DATA_DEVELOPMENT_FLOW_DEFINE_OFFLINE(spaceId, code),
        data,
      });
    },
    // 批量下线工作流
    batchOfflineFlowDefine(spaceId, data = {}) {
      return axios({
        method: 'post',
        url: API_DATA_DEVELOPMENT_FLOW_DEFINE_BATCH_OFFLINE(spaceId),
        data,
      });
    },
    // 工作流版本切换
    switchFlowDefine(params = {}) {
      return axios({
        method: 'post',
        url: API_DATA_DEVELOPMENT_FLOW_DEFINE_VERSION_SWITCH(),
        params,
      });
    },
    // 工作流版本查询
    fetchFlowDefineVersionList(data = {}) {
      return axios({
        method: 'post',
        url: API_DATA_DEVELOPMENT_FLOW_DEFINE_VERSION_LIST(),
        data,
        loading: false,
      });
    },
    // 工作流版本删除
    fetchFlowDefineVersionDel(id) {
      return axios({
        method: 'delete',
        url: API_DATA_DEVELOPMENT_FLOW_DEFINE_VERSION(id),
      });
    },
    // 工作流版本详情
    fetchFlowDefineVersionGet(id) {
      return axios({
        method: 'get',
        url: API_DATA_DEVELOPMENT_FLOW_DEFINE_VERSION(id),
      });
    },
    // 查询授权成员
    fetchFlowDefineAuthGrantors(spaceId, code) {
      return axios({
        method: 'get',
        url: API_DATA_DEVELOPMENT_FLOW_DEFINE_AUTH_GRANTORS(spaceId, code),
      });
    },
    // 授权
    fetchFlowDefineAuthGrant(spaceId, code, data = {}) {
      return axios({
        method: 'post',
        url: API_DATA_DEVELOPMENT_FLOW_DEFINE_AUTH_GRANT(spaceId, code),
        data,
      });
    },
    // 批量授权
    fetchFlowDefineAuthBatchGrant(spaceId, data = {}) {
      return axios({
        method: 'post',
        url: API_DATA_DEVELOPMENT_FLOW_DEFINE_AUTH_BATCH_GRANT(spaceId),
        data,
      });
    },
    // 授权移除
    fetchFlowDefineAuthRevoke(spaceId, code, params = {}) {
      return axios({
        method: 'delete',
        url: API_DATA_DEVELOPMENT_FLOW_DEFINE_AUTH_REVOKE(spaceId, code),
        params,
      });
    },
    // 工作流补录
    fetchFlowDefineSupplement(spaceId, code, data = {}) {
      return axios({
        method: 'post',
        url: API_DATA_DEVELOPMENT_FLOW_DEFINE_SUPPLEMENT(spaceId, code),
        data,
      });
    },
    // 工作流加锁
    fetchFlowDefineLock(spaceId, code) {
      return axios({
        method: 'post',
        url: API_DATA_DEVELOPMENT_FLOW_DEFINE_LOCK(spaceId, code),
        loading: false,
      });
    },
    // 工作流解锁
    fetchFlowDefineUnlock(spaceId, code) {
      return axios({
        method: 'post',
        url: API_DATA_DEVELOPMENT_FLOW_DEFINE_UNLOCK(spaceId, code),
        loading: false,
      });
    },
    // 获取所有BIDS的App
    fetchFlowDefineBidsApps() {
      return axios({
        method: 'get',
        url: API_DATA_DEVELOPMENT_FLOW_DEFINE_BIDS_APPS(),
      });
    },
    // 根据任务编码获取任务列表
    fetchFlowDefineGetTaskByCode(params) {
      return axios({
        method: 'get',
        url: API_DATA_DEVELOPMENT_FLOW_DEFINE_GET_TASK_BY_CODE(),
        params,
        loading: false,
      });
    },
    // 导入工作流
    fetchFlowDefineImport(spaceId, data = {}) {
      return axios({
        method: 'put',
        url: API_DATA_DEVELOPMENT_FLOW_DEFINE_IMPORT(spaceId),
        data,
        loading: false,
        showErrorMsg: false,
      });
    },
    // 获取批量发布结果
    fetchFlowDefineBatchPublishResult(spaceId, params = {}) {
      return axios({
        method: 'post',
        url: API_DATA_DEVELOPMENT_FLOW_DEFINE_BATCH_PUBLISH_RESULT(spaceId),
        params,
        loading: false,
      });
    },
    // 工作流导航树
    fetchFlowDefineNavTree(spaceId, params = {}) {
      return axios({
        method: 'get',
        url: API_DATA_DEVELOPMENT_FLOW_TREE(spaceId),
        loading: false,
        params,
      });
    },
    // 工作流导航树(第一层)
    fetchFlowDefineNavTreeProcess(spaceId) {
      return axios({
        method: 'get',
        url: API_DATA_DEVELOPMENT_FLOW_TREE_PROCESS(spaceId),
        loading: false,
      });
    },
    // 工作流导航树(第二层)
    fetchFlowDefineNavTreeTask({ spaceId, code }) {
      return axios({
        method: 'get',
        url: API_DATA_DEVELOPMENT_FLOW_TREE_TASK(spaceId, code),
        loading: false,
      });
    },

    // 定时调度上线
    fetchFlowTimerOnline(id) {
      return axios({
        method: 'post',
        url: API_DATA_DEVELOPMENT_FLOW_TIMER_ONLINE(id),
      });
    },
    // 定时调度下线
    fetchFlowTimerOffline(id) {
      return axios({
        method: 'post',
        url: API_DATA_DEVELOPMENT_FLOW_TIMER_OFFLINE(id),
      });
    },
    // 查询定时调度详情
    fetchFlowTimerDetail(params = {}) {
      return axios({
        method: 'get',
        url: API_DATA_DEVELOPMENT_FLOW_TIMER_GENERAL(),
        params,
      });
    },
    // 新增或者更新定时调度
    fetchFlowTimerPut(data) {
      return axios({
        method: 'post',
        url: API_DATA_DEVELOPMENT_FLOW_TIMER_GENERAL(),
        data,
      });
    },
    // 删除定时调度
    fetchFlowTimerDelete(id) {
      return axios({
        method: 'delete',
        url: API_DATA_DEVELOPMENT_FLOW_TIMER_DELETE(id),
      });
    },

    // Git项目管理列表
    fetchGitManagerList(data, options = {}) {
      return axios({
        method: 'post',
        url: API_DATA_DEVELOPMENT_GIT_MANAGER_LIST(),
        data,
        ...options,
      });
    },
    // 判断当前空间下的Git项目名称是否重复
    fetchGitManagerCheckName(params) {
      return axios({
        method: 'get',
        url: API_DATA_DEVELOPMENT_GIT_MANAGER_CHECK_NAME(),
        params,
      });
    },
    // 新增Git项目
    fetchGitManagerAdd(data) {
      return axios({
        method: 'post',
        url: API_DATA_DEVELOPMENT_GIT_MANAGER_OPERATE(),
        data,
      });
    },
    // 删除Git项目
    fetchGitManagerDel(id) {
      return axios({
        method: 'delete',
        url: API_DATA_DEVELOPMENT_GIT_MANAGER_OPERATE(id),
      });
    },
    // 查询Git项目详情
    fetchGitManagerGet(id) {
      return axios({
        method: 'get',
        url: API_DATA_DEVELOPMENT_GIT_MANAGER_OPERATE(id),
      });
    },
    // 查询Git项目详情
    fetchGitManagerUpdate(data) {
      return axios({
        method: 'put',
        url: API_DATA_DEVELOPMENT_GIT_MANAGER_OPERATE(data.id),
        data,
      });
    },
    // 获取Git项目分支
    fetchGitManagerBranches(data) {
      return axios({
        method: 'post',
        url: API_DATA_DEVELOPMENT_GIT_MANAGER_BRANCHES(),
        data,
        loading: false,
      });
    },
    // 获取Git项目分支
    fetchGitManagerBuild(data) {
      return axios({
        method: 'post',
        url: API_DATA_DEVELOPMENT_GIT_MANAGER_BUILD(),
        data,
      });
    },
    // 获取Git项目分支
    fetchGitManagerCheck(params) {
      return axios({
        method: 'get',
        url: API_DATA_DEVELOPMENT_GIT_MANAGER_CHECK(),
        params,
      });
    },
    // 查询已授权人员列表
    fetchGitManagerGrantors(spaceId, gitManagerId) {
      return axios({
        method: 'post',
        url: API_DATA_DEVELOPMENT_GIT_MANAGER_GRANTORS(spaceId, gitManagerId),
      });
    },
    // 授权
    fetchGitManagerGrant(spaceId, gitManagerId, data) {
      return axios({
        method: 'post',
        url: API_DATA_DEVELOPMENT_GIT_MANAGER_GRANT(spaceId, gitManagerId),
        data,
      });
    },
    // 授权移除
    fetchGitManagerRevoke(params) {
      return axios({
        method: 'delete',
        url: API_DATA_DEVELOPMENT_GIT_MANAGER_REVOKE(),
        params,
      });
    },
    // 查询git文件路径
    fetchGitManagerFilePath(data, options = {}) {
      return axios({
        method: 'post',
        url: API_DATA_DEVELOPMENT_GIT_MANAGER_FILE_PATH(),
        data,
        ...options,
      });
    },
    // 查询git文件内容
    fetchGitManagerFileContent(params, options = {}) {
      return axios({
        method: 'get',
        url: API_DATA_DEVELOPMENT_GIT_MANAGER_FILE_CONTENT(),
        params,
        ...options,
      });
    },
    // 工作流实例列表
    fetchFlowInstanceList(data) {
      return axios({
        method: 'post',
        url: API_DATA_DEVELOPMENT_FLOW_INSTANCE_LIST(),
        data,
      });
    },
    // 工作流实例调度日志
    fetchFlowInstanceLog(params) {
      return axios({
        method: 'get',
        url: API_DATA_DEVELOPMENT_FLOW_INSTANCE_LOG(),
        params,
      });
    },
    // 工作流实例执行日志
    fetchFlowInstancePodLog(params) {
      return axios({
        method: 'get',
        url: API_DATA_DEVELOPMENT_FLOW_INSTANCE_POD_LOG(),
        params,
      });
    },
    // 工作流实例操作
    fetchFlowInstanceOperate(data, cancel) {
      return axios({
        method: 'post',
        url: API_DATA_DEVELOPMENT_FLOW_INSTANCE_OPERATE(),
        data,
        cancel,
      });
    },
    // 工作流实例标识跳转工作流实例详情
    fetchFlowInstanceDetail({ spaceId, code, processInstanceId }) {
      return axios({
        method: 'get',
        url: API_DATA_DEVELOPMENT_FLOW_INSTANCE_DETAIL(spaceId, code, processInstanceId),
      });
    },
    // 查询工作流实例ID（用于工作流实例详情中 跳转到 子节点的实例）
    fetchFlowInstanceSubInstanceInfo(params) {
      return axios({
        method: 'get',
        url: API_DATA_DEVELOPMENT_FLOW_INSTANCE_SUB_INSTANCE_INFO(),
        params,
        loading: false,
      });
    },

    // 任务实例列表
    fetchTaskInstanceList(data) {
      return axios({
        method: 'post',
        url: API_DATA_DEVELOPMENT_TASK_INSTANCE_LIST(),
        data,
      });
    },
    // 任务实例调度日志
    fetchTaskInstanceLog(params) {
      return axios({
        method: 'get',
        url: API_DATA_DEVELOPMENT_TASK_INSTANCE_LOG(),
        params,
      });
    },
    // 任务实例执行日志
    fetchTaskInstancePodLog(params) {
      return axios({
        method: 'get',
        url: API_DATA_DEVELOPMENT_TASK_INSTANCE_POD_LOG(),
        params,
      });
    },
    // 任务实例操作
    fetchTaskInstanceOperate(data) {
      return axios({
        method: 'post',
        url: API_DATA_DEVELOPMENT_TASK_INSTANCE_OPERATE(),
        data,
      });
    },
  },
  getters: {
    currentTabPanel(state) {
      return state.tabPanelList.find(tab => tab.value === state.currentTabValue);
    },
  },
});

if (import.meta.webpackHot) {
  import.meta.webpackHot.accept(acceptHMRUpdate(useDataDevelopmentStore, import.meta.webpackHot));
}
