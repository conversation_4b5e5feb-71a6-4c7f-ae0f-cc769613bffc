import { cloneDeep, isBoolean } from 'lodash';
import { reactive, ref } from 'vue';

export function useForm(initFormData = {}, initFormRules = {}) {
  const rowData = cloneDeep(initFormData);

  const form = reactive({ ...initFormData });
  const rules = reactive({ ...initFormRules });
  const formRef = ref(null);

  const validateForm = async (fields) => {
    const valid = await formRef.value.validate({ fields });
    return isBoolean(valid) ? valid : false;
  };

  const resetForm = () => {
    resetData();
    formRef.value.reset();
  };

  const resetData = () => {
    const rowKeys = Object.keys(rowData);
    Object.keys(form).forEach((key) => {
      if (rowKeys.includes(key)) {
        form[key] = cloneDeep(rowData[key]);
      } else {
        delete form[key];
      }
    });
  };

  const clearValidate = () => {
    formRef.value.clearValidate();
  };
  return { form, rules, formRef, validateForm, resetForm, clearValidate, resetData };
}
