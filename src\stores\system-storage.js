import { API_COMMON_CURRENT_USER_INFO } from '@/server/api/common';
import axios from '@/server/client';

export const systemObj = {
  staffId: '',
  staffName: '',
  operateCodeList: [],
};
export async function getUserInfo() {
  const res = await axios({
    url: API_COMMON_CURRENT_USER_INFO,
    method: 'get',
  });
  const { data } = res;
  systemObj.staffId = data.staffId;
  systemObj.staffName = data.staffName;
  systemObj.staffDisplayName = data.staffDisplayName;
  systemObj.operateCodeList = data.operateCodeList || [];
  return res;
}
