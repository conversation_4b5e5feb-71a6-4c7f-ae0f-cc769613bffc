<!-- 业务数据源详情 -->
<template>
  <div class="page-des-data-source-detail">
    <section style="margin-bottom: 32px;">
      <BlockHeader title="基本信息" style="margin-bottom: 12px"></BlockHeader>
      <t-row :gutter="[0, 14]">
        <t-col class="des-flex-align-center" :span="4"><span class="label">数据源名称</span><span class="content">{{ form.name }}</span></t-col>
        <t-col class="des-flex-align-center" :span="4"><span class="label">数据源标识</span><span class="content">{{ form.businessDatasourceFlag }}</span></t-col>
        <t-col class="des-flex-align-center" :span="4"><span class="label">数据源类型</span><span class="content">{{ form.type }}</span></t-col>

        <t-col class="des-flex-align-center" :span="4"><span class="label">数据源类别</span><span class="content">业务数据源</span></t-col>
        <t-col class="des-flex-align-center" :span="4"><span class="label">IP主机名</span><span class="content">{{ form.ip }}</span></t-col>
        <t-col class="des-flex-align-center" :span="4"><span class="label">端口</span><span class="content">{{ form.port }}</span></t-col>

        <t-col class="des-flex-align-center" :span="4"><span class="label">数据库名</span><span class="content">{{ form.databaseName }}</span></t-col>
        <t-col class="des-flex-align-center" :span="4"><span class="label">创建人</span><span class="content"><user-name :user="form.createByName"></user-name></span></t-col>
        <t-col class="des-flex-align-center" :span="4"><span class="label">负责人</span><span class="content"><ellipsis-with-tooltip class="content" :text="form.inchargeList.map(item => item.staffDisplayName).join('；')"></ellipsis-with-tooltip></span></t-col>

        <t-col class="des-flex-align-center" :span="4"><span class="label">所属空间</span><span class="content">{{ form.spaceName }}</span></t-col>
        <t-col class="des-flex-align-center" :span="4"><span class="label">创建时间</span><span class="content">{{ form.createTime }}</span></t-col>
        <t-col class="des-flex-align-center" :span="4"><span class="label">更新时间</span><span class="content">{{ form.updateTime }}</span></t-col>

        <t-col class="des-flex-align-center special-wrap" :span="12"><span class="label">数据源描述</span><span class="content">{{ form.description }}</span></t-col>
      </t-row>
    </section>
    <section style="margin-bottom: 32px;">
      <BlockHeader title="安全认证" style="margin-bottom: 12px"></BlockHeader>
      <t-row :gutter="[0, 14]">
        <t-col class="des-flex-align-center" :span="4"><span class="label">用户名</span><span class="content">{{ form.userName }}</span></t-col>
        <t-col class="des-flex-align-center" :span="4"><span class="label">密码</span><span class="content">******</span></t-col>
      </t-row>
    </section>
    <section>
      <BlockHeader title="高级信息" style="margin-bottom: 12px"></BlockHeader>
      <t-row :gutter="[0, 14]">
        <t-col class="des-flex-align-center" :span="4"><span class="label">数据库名</span><span class="content">{{ form.databaseName }}</span></t-col>
        <t-col class="des-flex-align-center" :span="4"><span class="label">jdbc连接参数</span><span class="content">{{ form.connectionParams }}</span></t-col>
      </t-row>
    </section>
  </div>
</template>

<script setup>
import { to } from '@/utils/util';
import { defineProps, onMounted, reactive } from 'vue';
import { useDataCatalogStore } from '@/stores/data-catalog';
import UserName from '@/components/UserName.vue';
import BlockHeader from '@/components/BlockHeader.vue';
import EllipsisWithTooltip from '@/components/EllipsisWithTooltip.vue';

const { fetchBusiDatasourceGet } = useDataCatalogStore();

const props = defineProps({
  id: String,
});

const form = reactive({
  inchargeList: [],
});

const fetchData = async () => {
  const [err, data] = await to(fetchBusiDatasourceGet({ datasourceId: props.id }));
  if (err) {
    return;
  }
  Object.assign(form, data);
};

onMounted(() => {
  fetchData();
});
</script>

<style lang="less" scoped>
.page-des-data-source-detail {
  padding: 20px;
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  .label {
    display: inline-block;
    width: 104px;
    margin-right: 8px;
    color: #999999;
    text-align: right;
  }
  .content {
    display: inline-block;
    color: #333333;
    width: calc(~"100% - 112px");
  }
  .special-wrap {
    align-items: start;
  }
}
</style>
