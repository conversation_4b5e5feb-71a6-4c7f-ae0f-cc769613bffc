<template>
  <div style="height: 100%">
    <PageLayout>
      <template #search>
        <BasicSearch v-model="searchKey" placeholder="黑名单SQL/创建人/修改人..." @reset="handleReset" @change="searchChange">
          <template #prefix>
            <t-select
              v-model="datasourceId"
              :options="datasourceOptions"
              placeholder="数据源"
              filterable
              @change="searchChange"
            ></t-select>
          </template>
        </BasicSearch>
      </template>

      <template #button>
        <t-button theme="primary" @click="handleAdd"><add-icon slot="icon" />添加黑名单</t-button>
      </template>

      <template #table>
        <t-table rowKey="id" :data="tableData" :columns="columns" :pagination="pagination" max-height="68vh">
          <template #empty v-if="!datasourceId">
            <no-data text="请先选择数据源"></no-data>
          </template>
        </t-table>
      </template>
    </PageLayout>
  </div>
</template>

<script setup lang="jsx" name="blacklist-sql">
import { to, formatTime } from '@/utils/util';
import { ref, defineProps, onMounted } from 'vue';
import { useSearch, usePagination } from '@/hooks';
import { AddIcon } from 'tdesign-icons-vue';
import { MessagePlugin } from 'tdesign-vue';
import { showDialogConfirm, showDialogAlert } from '@/utils/dialog';
import { useRouter, useRoute } from 'vue-router/composables';
import { PageLayout } from '@/views/operations/components';
import BasicSearch from '@/components/BasicSearch.vue';
import { useOperationsStore } from '@/stores/operations';
import UserName from '@/components/UserName.vue';
import NoData from '@/components/NoData.vue';

defineProps({
  datasourceOptions: {
    type: Array,
    default: () => [],
  },
});

const operationsStore = useOperationsStore();

const route = useRoute();
const router = useRouter();

const datasourceId = ref('');

// 表格列
const columns = ref([
  { colKey: 'serial-number', width: 61, title: '序号', fixed: 'left' },
  {
    colKey: 'id',
    title: '黑名单ID',
    width: 123,
  },
  {
    colKey: 'sqlRegex',
    title: '黑名单SQL',
    width: 180,
    ellipsis: true,
  },
  {
    colKey: 'createUser',
    title: '创建人',
    width: 164,
    cell: (h, { row }) => <UserName fullName={row.createUser}></UserName>,
  },
  {
    colKey: 'createTime',
    title: '创建时间',
    width: 182,
    cell: (h, { row }) => formatTime(row.createTime),
  },
  {
    colKey: 'updateUser',
    title: '修改人',
    width: 164,
    cell: (h, { row }) => <UserName fullName={row.updateUser}></UserName>,
  },
  {
    colKey: 'updateTime',
    title: '修改时间',
    width: 182,
    cell: (h, { row }) => formatTime(row.updateTime),
  },
  {
    title: '操作',
    colKey: 'operate',
    width: 150,
    cell: (h, { row }) => (
      <t-space>
        <t-link theme="primary" hover="color" onClick={() => handleEdit(row)}>编辑</t-link>
        <t-link theme="primary" hover="color" onClick={() => handleDel(row)}>删除</t-link>
      </t-space>
    ),
  },
]);

const tableData = ref([]);

const fetchData = async () => {
  if (!datasourceId.value) return;
  const { current: pageNum, pageSize } = pagination;
  const params = {
    pageNum,
    pageSize,
    queryData: {
      queryKey: searchKey.value,
      datasourceId: datasourceId.value,
    },
  };
  const [err, data] = await to(operationsStore.fetchBlacklistList(params));
  if (err) {
    return;
  }
  const { total, list } = data;
  setPaginationTotal(total);
  tableData.value = list;
};

const { searchKey, setSearchKey, onSearch } = useSearch(fetchData);
const { pagination, setPaginationCurrent, setPaginationTotal } = usePagination(fetchData);

const searchChange = () => {
  setPaginationCurrent(1);
  onSearch();
};

const handleReset = () => {
  setSearchKey('');
  setPaginationCurrent(1);
  fetchData();
};

const handleAdd = () => {
  router.push({ name: 'srdataBlacklist' });
};

const handleEdit = (row) => {
  router.push({
    name: 'srdataBlacklist',
    query: { id: row.id },
  });
};

const handleDel = async (row) => {
  const result = await showDialogConfirm({
    title: `确定删除黑名单「${row.id}」吗？`,
    body: '删除后不可恢复',
    width: '440px',
  });
  if (result.type === 'cancel') return;
  const [err] = await to(operationsStore.fetchBlacklistDel(row.id));
  if (err) {
    showDialogAlert({
      title: `不可删除黑名单「${row.id}」`,
      body: err.msg,
      width: '440px',
    });
    return;
  }
  fetchData();
  MessagePlugin('success', '操作成功');
};

onMounted(() => {
  if (route.params.datasourceId) {
    datasourceId.value = route.params.datasourceId;
    fetchData();
  }
});
</script>

<style lang="less" scoped></style>
