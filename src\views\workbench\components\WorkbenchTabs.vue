<template>
  <ul class="tabs-wrap" @click="handleClick">
    <li class="tabs-item" :class="{ actived: actived === 'flow' }" data-value="flow">工作流</li>
    <li class="tabs-item" :class="{ actived: actived === 'menu' }" data-value="menu">数据目录</li>
  </ul>
</template>

<script setup>
import { ref } from 'vue';
// const emit = defineEmits(['change']);

const actived = ref('flow');
const handleClick = () => {
  // 暂时关闭此功能
  // const { value } = e.target.dataset;
  // actived.value = value;
  // emit('change', value);
};

</script>

<style lang="less" scoped>
.tabs-wrap {
  display: flex;
  text-align: center;
  cursor: pointer;
  font-size: 12px;
  .tabs-item {
    width: 108px;
    height: 28px;
    line-height: 28px;
    border: 1px solid #dcdcdc;
    border-radius: 0 4px 4px 0;
    &:first-child {
      border-radius: 4px 0 0 4px;
    }
  }
  .actived {
    color: hsla(220.5, 100%, 53.5%, 1);
    border: 1px solid #125fff;
    background: hsla(220.5, 100%, 53.5%, 0.1);
  }
}
</style>
