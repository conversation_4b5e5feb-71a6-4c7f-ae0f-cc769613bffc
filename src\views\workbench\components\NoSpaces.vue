<script setup>
import { defineProps } from 'vue';

defineProps({
  text: {
    type: String,
    default: '暂无管理或参与的数据空间~',
  },
});

</script>

<template>
  <div class="des-space-empty">
    <img src="@/assets/emptySpace.png" alt="无空间">
    <p>{{ text }}</p>
  </div>
</template>

<style lang="less" scoped>
.des-space-empty {
  text-align: center;
  height: 100vh;
  width: 1200px;
  background: #fff;
  padding-top: 80px;
  margin: 12px auto;
  border-radius: 5px;
  img {
    display: block;
    width: 200px;
    height: 200px;
    margin: 0 auto;
  }
  p {
    color: #333333;
    font-family: "PingFang SC";
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 28px;
    text-align: center;
  }
}
</style>
