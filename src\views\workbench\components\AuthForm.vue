<template>
  <t-form ref="formRef" :data="form" :rules="rules" labelAlign="top">
    <slot></slot>
    <t-form-item label="授权成员" name="staff">
      <sdc-staff-selector ref="staffRef" v-model="form.staff" multiple style="width:100%;" placeholder="请选择人员" modalClass="des-sdc-modal--fix" size="small" @change="staffChange"></sdc-staff-selector>
    </t-form-item>
    <t-form-item label="授权类型" name="type">
      <div>
        <div>
          <t-checkbox :checked="checkAll" :indeterminate="indeterminate" :onChange="handleSelectAll">全选</t-checkbox>
        </div>
        <t-checkbox-group v-model="form.type" :options="options" />
      </div>
    </t-form-item>
    <t-form-item>
      <t-button variant="outline" theme="primary" @click="handleAdd"><add-icon slot="icon" />添加</t-button>
    </t-form-item>
  </t-form>
</template>

<script setup>
import { useForm } from '@/hooks';
import { AddIcon } from 'tdesign-icons-vue';
import { useSpacesStore } from '@/stores/spaces';
import { computed, ref, defineEmits, defineExpose, defineProps } from 'vue';
import { storeToRefs } from 'pinia';

const spacesStore = useSpacesStore();
const { isSpaceOwner } = storeToRefs(spacesStore);

const emit = defineEmits(['handleAdd']);
const props = defineProps({
  // 当前用户的权限
  powerList: {
    type: Array,
  },
});

const initFormData = {
  staff: [],
  type: [],
};
const initFormRules = {
  staff: [
    {
      required: true,
      message: '必选',
      type: 'error',
      trigger: 'change',
    },
  ],
  type: [
    {
      required: true,
      message: '必选',
      type: 'error',
      trigger: 'change',
    },
  ],
};

const { form, formRef, rules, validateForm, resetForm } = useForm(initFormData, initFormRules);

const hasAuth = (operate) => {
  if (isSpaceOwner.value) {
    return true;
  }
  return props.powerList.includes(operate);
};

const options = computed(() => {
  const array = [
    {
      value: 'edit',
      label: '编辑',
      disabled: !hasAuth('edit'),
    },
    {
      value: 'use',
      label: '使用',
      disabled: !hasAuth('use'),
    },
  ];
  if (isSpaceOwner.value) {
    array.push({ value: 'grant', label: '授予', disabled: !hasAuth('grant') });
  }
  return array;
});

const checkAll = computed(() => options.value.length === form.type.length);
const indeterminate = computed(() => !!(options.value.length > form.type.length && form.type.length));
const handleSelectAll = (checked) => {
  const powerList = isSpaceOwner.value ? ['edit', 'use', 'grant'] : props.powerList.filter(item => item !== 'grant');
  form.type = checked ? powerList : [];
};

const selectStaffList = ref([]);
const staffChange = (val) => {
  selectStaffList.value = val;
};

const staffRef = ref(null);
const clearSelected = () => {
  selectStaffList.value = [];
  staffRef.value.clearSelected();
};

const handleAdd = async () => {
  const valid = await validateForm();
  if (!valid) return;
  const data = { form, selectStaffList };

  const callback = () => {
    clearSelected();
    resetForm();
  };
  emit('handleAdd', data, callback);
};

defineExpose({ resetForm });
</script>
