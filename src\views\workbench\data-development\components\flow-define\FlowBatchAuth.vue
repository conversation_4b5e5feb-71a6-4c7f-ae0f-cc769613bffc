<script setup>
import { ref, reactive, computed, defineExpose, defineProps, defineEmits } from 'vue';
import { useDataDevelopmentStore } from '@/stores/data-development';
import { useSpacesStore } from '@/stores/spaces';
import { MessagePlugin } from 'tdesign-vue';
import FlowBatchAuthResult from './FlowBatchAuthResult.vue';

const emit = defineEmits(['refresh', 'reset']);

const props = defineProps({
  spaceId: {
    type: String,
    default: '',
  },
});

const dataDevelopmentStore = useDataDevelopmentStore();
const spacesStore = useSpacesStore();

const visible = ref(false);
const formRef = ref(null);
const staffSelectorRef = ref(null);
const formData = reactive({
  nameList: [],
  staff: [],
  authType: [],
});
const choosedStaff = ref([]);

const rules = {
  staff: [{ required: true, message: '请选择授权人员' }],
  authType: [{ required: true, message: '请选择授权类型' }],
};
const authTypeOption = computed(() => {
  const options = [
    { label: '编辑', value: 'EDIT' },
    { label: '运行', value: 'RUN' },
    { label: '查看', value: 'READ' },
    { label: '授予', value: 'GRANT' },
  ];
  return options;
});
const userList = ref([]);

// 提交前检查权限的方法
async function checkPermissionsBeforeSubmit(spaceId, staffList) {
  for (const staff of staffList) {
    const { data } = await spacesStore.checkSpaceIsOwnerByStaff(spaceId, staff.StaffID);
    if (data) {
      MessagePlugin.error(`${staff.StaffName}是空间负责人，默认已有全部权限，不需要添加`);
      return false;
    }
  }
  return true;
}

function handleConfirm() {
  formRef.value.validate().then(async (result) => {
    if (result === true) {
      const canSubmit = await checkPermissionsBeforeSubmit(props.spaceId, choosedStaff.value);
      if (!canSubmit) return;

      choosedStaff.value.forEach(async (staff) => {
        // 有编辑权限默认+READ
        let permissionList = formData.authType;

        if (formData.authType.includes('EDIT')) {
          const setArray = new Set(formData.authType);
          setArray.add('READ');
          permissionList = [...setArray];
        }
        userList.value.push({
          user: {
            staffId: staff.StaffID,
            engName: staff.EngName,
            staffName: staff.StaffName,
          },
          permissionList,
        });
      });

      handleGrantAuth();
    }
  });
}

function getSaveData() {
  return {
    userList: userList.value,
    codeList: formData.codeList,
  };
}

function handleChangeStaff(staffs) {
  console.log('staffs', staffs);
  choosedStaff.value = staffs;
}

function handleClose() {
  close();
}

async function handleGrantAuth() {
  const subData = getSaveData();
  console.log('subData', subData);
  const { data } = await dataDevelopmentStore.fetchFlowDefineAuthBatchGrant(props.spaceId, subData);
  handleClose();
  emit('reset');
  flowBatchAuthResultRef.value.open(data);
}

function open({ codeList, nameList }) {
  visible.value = true;
  formData.nameList = nameList;
  formData.codeList = codeList;
}
function close() {
  visible.value = false;
  staffSelectorRef.value.clearSelected();
  formData.codeList = [];
  formData.nameList = [];
  formData.staff = [];
  formData.authType = [];
  choosedStaff.value = [];
  userList.value = [];
  formRef.value.reset();
  emit('refresh');
}

const flowBatchAuthResultRef = ref(null);

defineExpose({ open, close });
</script>

<template>
  <t-drawer
    size="640px"
    :visible.sync="visible"
    header="批量授权"
    :closeBtn="true"
    @close="handleClose"
    class="des-data-development__flow-define__flow-auth-drawer"
  >
    <t-form :data="formData" ref="formRef" labelWidth="106px" :rules="rules" labelAlign="top">
      <t-form-item label="工作流名称">
        <div style="color: #333;">{{ formData.nameList.join('，') }}</div>
      </t-form-item>
      <t-form-item label="授权成员" name="staff">
        <sdc-staff-selector ref="staffSelectorRef" v-model="formData.staff" @change="handleChangeStaff" multiple style="width:100%;" placeholder="请选择人员" modalClass="des-sdc-modal--fix" size="small"></sdc-staff-selector>
      </t-form-item>
      <t-form-item label="授权类型" name="authType">
        <t-checkbox-group v-model="formData.authType" :options="authTypeOption" />
      </t-form-item>
    </t-form>

    <template #footer>
      <div style="text-align: right;">
        <t-space :size="8">
          <t-button variant="outline" @click="handleClose">取消</t-button>
          <t-button @click="handleConfirm">确定</t-button>
        </t-space>
      </div>
    </template>

    <flow-batch-auth-result ref="flowBatchAuthResultRef"/>
  </t-drawer>
</template>

<style lang="less">
.des-data-development__flow-define__flow-auth-drawer {
  .t-drawer__body {
    padding: 24px;
  }
}
</style>
