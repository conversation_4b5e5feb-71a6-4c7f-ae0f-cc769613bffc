<template>
  <div style="height: 100%">
    <PageLayout>
      <template #search>
        <BasicSearch
          v-model="searchKey"
          placeholder="名称/创建人…"
          @reset="handleReset"
          @change="searchChange"
        ></BasicSearch>
      </template>

      <template #button>
        <t-button theme="primary" @click="handleAdd"><add-icon slot="icon" />新增数据源</t-button>
      </template>

      <template #table>
        <t-table
          rowKey="id"
          :data="tableData"
          :columns="columns"
          :pagination="pagination"
          max-height="68vh"
        ></t-table>
      </template>
    </PageLayout>
    <SRDataDialog ref="dialogRef" @fetchData="fetchData"></SRDataDialog>
    <RelatedDialog ref="relatedRef"></RelatedDialog>
  </div>
</template>

<script setup lang="jsx" name="srdata-manage">
import { to, formatTime } from '@/utils/util';
import { useSearch, usePagination } from '@/hooks';
import { ref, onMounted } from 'vue';
import { AddIcon } from 'tdesign-icons-vue';
import { MessagePlugin } from 'tdesign-vue';
import { showDialogConfirm, showDialogAlert } from '@/utils/dialog';
import { useOperationsStore } from '@/stores/operations';
import { PageLayout } from '@/views/operations/components';
import SRDataDialog from './SRDataDialog.vue';
import RelatedDialog from './RelatedDialog.vue';
import BasicSearch from '@/components/BasicSearch.vue';
import UserName from '@/components/UserName.vue';

// 表格列
const columns = ref([
  { colKey: 'serial-number', width: 61, title: '序号', fixed: 'left' },
  {
    colKey: 'name',
    title: '数据源名称',
    width: 136,
    ellipsis: true,
  },
  {
    colKey: 'ip',
    title: 'IP主机名',
    width: 93,
    ellipsis: true,
  },
  {
    colKey: 'databaseName',
    title: '数据库名',
    width: 159,
    ellipsis: true,
  },
  {
    colKey: 'userName',
    title: '用户名',
    width: 95,
    ellipsis: true,
  },
  {
    colKey: 'createUser',
    title: '创建人',
    width: 188,
    cell: (h, { row }) => <UserName fullName={row.createUser}></UserName>,
  },
  {
    colKey: 'updateTime',
    title: '更新时间',
    width: 188,
    cell: (h, { row }) => formatTime(row.updateTime),
  },
  {
    title: '操作',
    colKey: 'operate',
    width: 300,
    cell: (h, { row }) => (
      <t-space>
        <t-link theme="primary" hover="color" onClick={() => handleEdit(row)}>
          编辑
        </t-link>
        <t-link theme="primary" hover="color" disabled={ row.srDatasourceSpaceRelationDTOList.length === 0 } onClick={() => handleZone(row)}>
          关联的空间
        </t-link>
        <t-link theme="primary" hover="color" disabled={ row.srDatasourceProcessRelationDTOList.length === 0 } onClick={() => handleFlow(row)}>
          关联的工作流
        </t-link>
        <t-link theme="primary" hover="color" onClick={() => handleCancel(row)}>
          注销
        </t-link>
      </t-space>
    ),
  },
]);

const { fetchDatasourcesList, fetchDatasourcesCancel } = useOperationsStore();

// 表格数据
const tableData = ref([]);

const fetchData = async () => {
  const { current: pageNum, pageSize } = pagination;
  const data = { pageNum, pageSize, queryData: { queryKey: searchKey.value } };
  const [err, result] = await to(fetchDatasourcesList(data));
  if (err) {
    return;
  }
  const { total, list } = result;
  setPaginationTotal(total);
  tableData.value = list;
};

const { pagination, setPaginationCurrent, setPaginationTotal } = usePagination(fetchData);
const { searchKey, setSearchKey, onSearch } = useSearch(fetchData);

const searchChange = () => {
  setPaginationCurrent(1);
  onSearch();
};

// 重置按钮
const handleReset = () => {
  setSearchKey('');
  setPaginationCurrent(1);
  fetchData();
};

const handleCancel = async (row) => {
  const result = await showDialogConfirm({
    title: `确定注销SR数据源「${row.name}」吗？`,
    body: '注销后不可恢复',
    width: '440px',
  });
  if (result.type === 'cancel') return;
  const [err] = await to(fetchDatasourcesCancel({ datasourceId: row.id }, { showErrorMsg: false }));
  if (err) {
    showDialogAlert({
      title: `不可注销SR数据源「${row.name}」`,
      body: err.msg,
      width: '440px',
    });
    return;
  }
  fetchData();
  MessagePlugin('success', '操作成功');
};

const handleEdit = (row) => {
  dialogRef.value.openEditDialog(row);
};

const dialogRef = ref(null);
const handleAdd = () => {
  dialogRef.value.openAddDialog();
};


const relatedRef = ref(null);
const handleFlow = (row) => {
  relatedRef.value.show(row.id, 'flow');
};
const handleZone = (row) => {
  relatedRef.value.show(row.id, 'zone');
};

onMounted(() => fetchData());
</script>

<style lang="less" scoped></style>
