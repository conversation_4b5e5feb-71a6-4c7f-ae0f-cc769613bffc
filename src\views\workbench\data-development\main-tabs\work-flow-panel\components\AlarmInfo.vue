<script setup>
import { defineProps, ref, watch } from 'vue';
import EllipsisWithTooltip from '@/components/EllipsisWithTooltip.vue';
import { useSpacesStore } from '@/stores/spaces';

const props = defineProps({
  alarmId: {
    type: String,
  },
});

const alarmInfo = ref({});

const spaceStore = useSpacesStore();

async function fetchAlarmInfo(id) {
  if (id) {
    const { data } = await spaceStore.getAlarmDetail(id);
    alarmInfo.value = data;
  }
}
watch(() => props.alarmId, fetchAlarmInfo, { immediate: true });

function staffNames(list = []) {
  return list.map(item => item.staffName).join('、');
}

</script>

<template>
  <div>
    <section class="alarm-template-info" style="margin-left: 106px;margin-bottom: 16px;">
      <section v-if="alarmInfo.types?.includes(1)">
        <h3>企微机器人告警配置</h3>
        <ul>
          <li>
            <span class="label">人群类型</span>
            <div class="content">
              <ellipsis-with-tooltip :text="alarmInfo.perType === 1 ? '个人' : '群'"></ellipsis-with-tooltip>
            </div>
          </li>
          <li v-if="alarmInfo.perType === 1">
            <span class="label">接收人</span>
            <div class="content">
              <ellipsis-with-tooltip :text="staffNames(alarmInfo.wechatReceivers)"></ellipsis-with-tooltip>
            </div>
          </li>
          <li v-if="alarmInfo.perType === 2">
            <span class="label">chatid</span>
            <div class="content">
              <ellipsis-with-tooltip :text="alarmInfo.wechatRobotChatid"></ellipsis-with-tooltip>
            </div>
          </li>
        </ul>
      </section>
      <section v-if="alarmInfo.types?.includes(2)">
        <h3>电话告警配置</h3>
        <ul>
          <li>
            <span class="label">接收人</span>
            <div class="content">
              <ellipsis-with-tooltip :text="staffNames(alarmInfo.phoneReceivers)"></ellipsis-with-tooltip>
            </div>
          </li>
        </ul>
      </section>
      <section v-if="alarmInfo.types?.includes(3)">
        <h3>邮件告警配置</h3>
        <ul>
          <li>
            <span class="label">接收人</span>
            <div class="content">
              <ellipsis-with-tooltip :text="staffNames(alarmInfo.emailReceivers)"></ellipsis-with-tooltip>
            </div>
          </li>
        </ul>
      </section>
    </section>
  </div>
</template>

<style lang="less" scoped>
.alarm-template-info {
  display: flex;
  padding: 16px;
  flex-direction: column;
  align-items: flex-start;
  gap: 12px;
  align-self: stretch;
  border-radius: 4px;
  background: #EBF0F7;
  & > section {
    border-radius: 4px;
    width: 100%;
    & > h3 {
      padding: 8px 12px;
      color: #333333;
      font-family: "PingFang SC";
      font-size: 12px;
      font-style: normal;
      font-weight: 600;
      line-height: 20px;
      border-bottom: 1px solid #EEE;
      background: #FFF;
    }
    & > ul {
      display: flex;
      padding: 16px 12px;
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
      align-self: stretch;
      background: #FFF;
    }
    li {
      display: flex;
      width: 100%;
      color: #333333;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
    }
    .label {
      width: 70px;
      flex-shrink: 0;
      color: #999999;
      text-align: right;
      margin-right: 8px;
    }
    .content {
      flex-grow: 1;
      overflow: hidden;
    }
  }
}
</style>
