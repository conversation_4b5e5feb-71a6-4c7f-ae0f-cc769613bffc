<script setup>
import { defineProps } from 'vue';

defineProps({
  text: {
    type: String,
    default: '',
  },
});

</script>

<template>
  <div class="des-data-empty">
    <img src="@/assets/emptyRow.png">
    <p>{{ text }}</p>
  </div>
</template>

<style lang="less" scoped>
.des-data-empty {
  text-align: center;
  margin-top: 20px;
  img {
    display: block;
    width: 120px;
    height: 120px;
    margin: 0 auto;
  }
  p {
    color: #999999;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    text-align: center;
    margin-top: 8px;
  }
}
</style>
