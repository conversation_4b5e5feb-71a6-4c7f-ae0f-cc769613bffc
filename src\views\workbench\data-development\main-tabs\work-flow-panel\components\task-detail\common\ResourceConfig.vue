<script setup>
import { defineProps, ref, watch } from 'vue';
import { useRoute } from 'vue-router/composables';
import BlockHeader from '@/components/BlockHeader.vue';
import { useOperationsStore } from '@/stores/operations';

const props = defineProps({
  formData: {
    type: Object,
    required: true,
  },
});
const route = useRoute();
const operationsStore = useOperationsStore();
const resourceGroupOptions = ref([]);
async function initOptions() {
  const res = await operationsStore.fetchResourceGroupsGetBySpaceId({ spaceId: route.query.spaceId });
  resourceGroupOptions.value = res.data.map(item => ({
    label: item.code,
    value: item.id,
  }));
  if (resourceGroupOptions.value.length) {
    // eslint-disable-next-line vue/no-mutating-props
    props.formData.resourceGroupId = props.formData.resourceGroupId || resourceGroupOptions.value[0].value;
  }
}
watch(() => route.query.spaceId, () => {
  initOptions();
}, { immediate: true });
</script>

<template>
  <!-- eslint-disable vue/no-mutating-props -->
  <section>
    <block-header title="资源配置" style="margin-bottom: 12px;" />
    <t-form-item label="资源标识" name="resourceGroupId">
      <t-select v-model="formData.resourceGroupId" placeholder="请选择" style="width: 374px;" :options="resourceGroupOptions"></t-select>
    </t-form-item>
    <!-- <t-form-item label="cpu" name="minCpuCores">
      <t-input-number v-model="formData.minCpuCores" :min="0.5" :max="5" :step="0.5" :decimalPlaces="1" :allowInputOverLimit="false" theme="column" placeholder="请选择" style="width: 374px;">
        <template #suffix><span>核</span></template>
      </t-input-number>
    </t-form-item>
    <t-form-item label="memory" name="minMemorySpace">
      <t-input-number v-model="formData.minMemorySpace" :min="512" :max="10240" :step="512" :decimalPlaces="0" :allowInputOverLimit="false" theme="column" placeholder="请选择" style="width: 374px;">
        <template #suffix><span>MB</span></template>
      </t-input-number>
    </t-form-item> -->
  </section>
</template>
