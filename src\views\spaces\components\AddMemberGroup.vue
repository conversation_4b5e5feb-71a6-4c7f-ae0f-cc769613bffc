<script setup>
import { ref, defineExpose, defineProps, defineEmits } from 'vue';
import BasicDialog from '@/components/BasicDialog.vue';
import { useSpacesStore } from '@/stores/spaces';
import { MessagePlugin } from 'tdesign-vue';
import { useForm } from '@/hooks';

const props = defineProps({
  spaceId: {
    type: String,
    default: '',
  },
});

const emit = defineEmits('update');
const spaceStore = useSpacesStore();
const visible = ref(false);

const initFormData = {
  name: '',
  code: '',
};

const initFormRules = {
  name: [{ required: true, message: '请输入用户组名称' }, { validator: checkName, trigger: 'change' }],
  code: [{ required: true, message: '请输入用户组标识' }],
};

const { form: formData, rules, formRef, validateForm, resetForm } = useForm(initFormData, initFormRules);

async function checkName(val) {
  if (!val) {
    return {
      result: false,
      message: '请输入用户组名称',
      type: 'error',
    };
  }
  const { data } = await spaceStore.checkSpacesUserGroupName(props.spaceId, val);
  if (data) {
    return {
      result: false,
      message: '用户组名称已存在',
      type: 'error',
    };
  }
  return true;
}

async function handleConfirm() {
  const success = await validateForm();
  if (success) {
    const params = { ...formData, spaceId: props.spaceId, isSystemDefault: 0 };
    await spaceStore.addSpacesUserGroup(params);
    close();
    emit('update');
    MessagePlugin.success('添加成功');
  }
}

function open() {
  visible.value = true;
}
function close() {
  visible.value = false;
  resetForm();
}

defineExpose({ open, close });
</script>

<template>
  <BasicDialog
    width="560"
    :visible.sync="visible"
    header="添加用户组"
    :onClosed="close"
    @handleClose="close"
    @handleConfirm="handleConfirm"
  >
    <t-form class="des-space__add-menber-group" :data="formData" style="padding-right:12px;" ref="formRef" labelWidth="98px" :rules="rules">
      <t-form-item label="用户组名称" name="name">
        <t-input v-model="formData.name"></t-input>
      </t-form-item>
      <t-form-item label="用户组标识" name="code">
        <t-input v-model="formData.code"></t-input>
      </t-form-item>
    </t-form>
  </BasicDialog>
</template>

<style lang="less" scoped>
.des-space__add-menber-group {
  :deep(.t-form__label--right) {
    padding-right: 12px;
  }
}
</style>
