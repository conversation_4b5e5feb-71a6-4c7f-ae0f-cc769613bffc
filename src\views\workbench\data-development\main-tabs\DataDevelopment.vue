<script setup>
import emitter, { EVENT_NAMES } from '@/utils/emitter';
import { ref, defineAsyncComponent, computed, watchEffect, defineExpose, onMounted, onBeforeUnmount } from 'vue';
import { storeToRefs } from 'pinia';
import { useCommonStore } from '@/stores/common';

const FlowDefine = defineAsyncComponent(() => import('../components/FlowDefine.vue'));
const FlowInstance = defineAsyncComponent(() => import('../components/FlowInstance.vue'));
const GitManage = defineAsyncComponent(() => import('../components/GitManage.vue'));
const TaskInstance = defineAsyncComponent(() => import('../components/TaskInstance.vue'));

const commonStore = useCommonStore();
const { operateCodeList } = storeToRefs(commonStore);
const current = ref('');
const tabPanelList = computed(() => [
  {
    label: () => (<div class="des-flex-align-center"><des-icon name="des-icon-gongzuoliufenzhi" size="16" /><span>工作流定义</span></div>),
    value: 'FlowDefine',
    auth: 'Menu_processDefinition',
  },
  {
    label: () => (<div class="des-flex-align-center"><des-icon name="des-icon-gongzuoliufenzhishili" size="16" /><span>工作流实例</span></div>),
    value: 'FlowInstance',
    auth: 'Menu_processInstance',
  },
  {
    label: () => (<div class="des-flex-align-center"><des-icon name="des-icon-linwushili" size="16" /><span>任务实例</span></div>),
    value: 'TaskInstance',
    auth: 'Menu_taskInstance',
  },
  {
    label: () => (<div class="des-flex-align-center"><des-icon name="des-icon-git" size="16" /><span>Git项目管理</span></div>),
    value: 'GitManage',
    auth: 'Menu_gitProject',
  },
].filter(item => operateCodeList.value.includes(item.auth)));

watchEffect(() => {
  const tabs = tabPanelList.value.map(item => item.value);
  if (!tabs.includes(current.value)) {
    changeTab(tabs[0]);
  }
});
function changeTab(val) {
  current.value = val;
}
// 属性保存对象，用于子组件触发tab切换
const compPorpsMap = {
  FlowDefine: {},
  FlowInstance: {},
  GitManage: {},
  TaskInstance: {},
};
// 组件映射
function getComp(name) {
  let comp = null;
  switch (name) {
    case 'FlowDefine':
      comp = FlowDefine;
      break;
    case 'FlowInstance':
      comp = FlowInstance;
      break;
    case 'GitManage':
      comp = GitManage;
      break;
    case 'TaskInstance':
      comp = TaskInstance;
      break;
    default:
      break;
  }
  return comp;
}
function switchTab(tabName = '', props = {}) {
  if (tabName) {
    compPorpsMap[tabName] = props;
    changeTab(tabName);
  }
}

function fetchPanelData() {
  if (current.value === 'FlowDefine') {
    emitter.emit(EVENT_NAMES.DATA_DEVELOPMENT.REFRESH_FLOW_DEFINE);
  }
}

onMounted(() => {
  emitter.on(EVENT_NAMES.DATA_DEVELOPMENT.CHANGE_TAB, changeTab);
});

onBeforeUnmount(() => {
  emitter.off(EVENT_NAMES.DATA_DEVELOPMENT.CHANGE_TAB, changeTab);
});

defineExpose({ fetchPanelData });
</script>

<template>
  <div class="page-des-development">
    <t-tabs
      :value="current"
      @change="changeTab"
      style="width: 100%;height:100%;"
    >
      <t-tab-panel
        v-for="data in tabPanelList"
        :key="data.value"
        :value="data.value"
        :label="data.label"
        :removable="data.removable"
        :destroyOnHide="false"
      >
        <component :is="getComp(data.value)" @switch="switchTab" v-bind="compPorpsMap[data.value]" :current="current" />
      </t-tab-panel>
    </t-tabs>
  </div>
</template>

<style lang="less" scoped>
.page-des-development {
  padding: 2px 20px 0;
  height: 100%;
  :deep(.t-tabs__content) {
    height: calc(100% - 48px);
    overflow: auto;
  }
  :deep(.t-tab-panel) {
    height: 100%;
  }
}
</style>
