import { defineStore, acceptHMRUpdate } from 'pinia';
import axios from '@/server/client';
import {
  API_DATA_CATALOG_BUSINESS_DATASOURCE_LIST,
  API_DATA_CATALOG_BUSINESS_DATASOURCE_ADD,
  API_DATA_CATALOG_BUSINESS_DATASOURCE_DEL,
  API_DATA_CATALOG_BUSINESS_DATASOURCE_GET,
  API_DATA_CATALOG_BUSINESS_DATASOURCE_TEST,
  API_DATA_CATALOG_BUSINESS_DATASOURCE_GRANT,
  API_DATA_CATALOG_BUSINESS_DATASOURCE_UPDATE,
  API_DATA_CATALOG_BUSINESS_DATASOURCE_LIST_BY_AUTH,
} from '@/server/api/data-catalog';

export const useDataCatalogStore = defineStore('data-catalog', {
  state: () => ({
    currentTabValue: 'DataCatalog',
    tabPanelList: [
      {
        label: '数据目录',
        value: 'DataCatalog',
        component: 'DataCatalog',
        removable: false,
        props: {},
      },
    ],
  }),
  actions: {
    setCurrentTabValue(value) {
      this.currentTabValue = value;
    },
    addTabPanelList(tabPanel) {
      const valueList = this.tabPanelList.map(item => item.value);
      if (tabPanel.value && !valueList.includes(tabPanel.value)) {
        this.tabPanelList.push(tabPanel);
      }
    },
    removeTabPanelList(val) {
      const index = this.tabPanelList.findIndex(item => item.value === val);
      const deleteItem = this.tabPanelList.splice(index, 1);
      // 如果删除的是当前聚焦的内容，那么需要重新聚焦一个tab
      if (this.currentTabValue === deleteItem[0].value) {
        const lastIndex = this.tabPanelList.length - 1;
        this.currentTabValue = this.tabPanelList[lastIndex].value;
      }
    },
    resetTabPanelList() {
      this.tabPanelList = [
        {
          label: '数据目录',
          value: 'DataCatalog',
          component: 'DataCatalog',
          removable: false,
          props: {},
        },
      ];
      this.currentTabValue = 'DataCatalog';
    },

    // 查询业务数据源列表
    fetchBusiDatasourceList(data, options = {}) {
      return axios({
        url: API_DATA_CATALOG_BUSINESS_DATASOURCE_LIST,
        method: 'post',
        data,
        ...options,
      });
    },
    // 新增业务数据源
    fetchBusiDatasourceAdd(data) {
      return axios({
        url: API_DATA_CATALOG_BUSINESS_DATASOURCE_ADD,
        method: 'post',
        data,
      });
    },
    // 删除业务数据源
    fetchBusiDatasourceDel(params, options = {}) {
      return axios({
        url: API_DATA_CATALOG_BUSINESS_DATASOURCE_DEL,
        method: 'get',
        params,
        ...options,
      });
    },
    // 编辑业务数据源
    fetchBusiDatasourceUpdate(data) {
      return axios({
        url: API_DATA_CATALOG_BUSINESS_DATASOURCE_UPDATE,
        method: 'post',
        data,
      });
    },
    // 查询业务数据源
    fetchBusiDatasourceGet(params) {
      return axios({
        url: API_DATA_CATALOG_BUSINESS_DATASOURCE_GET,
        method: 'get',
        params,
      });
    },
    // 业务数据源测试连通性
    fetchBusiDatasourceTest(data) {
      return axios({
        url: API_DATA_CATALOG_BUSINESS_DATASOURCE_TEST,
        API_DATA_CATALOG_BUSINESS_DATASOURCE_GRANT,
        method: 'post',
        data,
      });
    },
    // 业务数据源授权
    fetchBusiDatasourceGrant(data) {
      return axios({
        url: API_DATA_CATALOG_BUSINESS_DATASOURCE_GRANT,
        method: 'post',
        data,
      });
    },
    // 业务数据源授权
    fetchBusiDatasourceListByAuth(params) {
      return axios({
        url: API_DATA_CATALOG_BUSINESS_DATASOURCE_LIST_BY_AUTH,
        method: 'get',
        params,
      });
    },
  },
});

if (import.meta.webpackHot) {
  import.meta.webpackHot.accept(acceptHMRUpdate(useDataCatalogStore, import.meta.webpackHot));
}
