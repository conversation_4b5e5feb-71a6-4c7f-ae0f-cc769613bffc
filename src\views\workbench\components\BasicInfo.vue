<template>
  <div class="basic-info__wrap">
    <span class="label">表名称</span><span class="content">retail_e_commer1</span>
    <span class="label">SR数据源</span><span class="content">retail_e_commer1</span>
    <span class="label">所属空间</span><span class="content">retail_e_commer1</span>
    <span class="label">创建人</span><span class="content">retail_e_commer1</span>
    <span class="label">负责人</span><span class="content">retail_e_commer1</span>
    <span class="label">创建时间</span><span class="content">retail_e_commer1</span>
    <span class="label">更新时间</span><span class="content">retail_e_commer1</span>
    <div class="summary">
      <span class="label">描述</span>
      <span class="content">retail_edjashdjkashiudqwhkjehgawhjd_commer1</span>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from 'vue';

defineProps({
  data: Object,
});
</script>

<style lang="less" scoped>
.basic-info__wrap {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;

  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  span {
    margin-bottom: 8px;
  }
  .label {
    display: inline-block;
    width: 52px;
    margin-right: 8px;
    color: #999999;
    text-align: left;
  }
  .content {
    display: inline-block;
    color: #333333;
    width: calc(~"100% - 60px");
  }
  .summary {
    display: flex;
    align-items: start;
    .content {
      word-break: break-word;
      white-space: normal;
      overflow-wrap: break-word;
    }
  }
}
</style>
