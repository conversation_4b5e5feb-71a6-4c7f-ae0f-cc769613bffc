<template>
  <t-menu theme="light" width="210px" v-model="actived" @change="handleMenuChange">
    <t-menu-item value="viewTask" v-auth="'Menu_viewTask'">
      <template #icon>
        <des-icon name="des-icon-shitulinwuguanli"></des-icon>
      </template>
      <span class="text">视图任务管理</span>
    </t-menu-item>
    <t-menu-item value="sqlExecution" v-auth="'Menu_sqlExecRecord'">
      <template #icon>
        <DesIcon name="des-icon-SQLzhixingjilu"></DesIcon>
      </template>
      <span class="text">SQL执行记录</span>
    </t-menu-item>
    <!-- <t-menu-item value="operationLog" v-auth="'Menu_logManage'">
      <template #icon>
        <DesIcon name="des-icon-rizhiguanli"></DesIcon>
      </template>
      <span class="text">操作日志</span>
    </t-menu-item> -->
  </t-menu>
</template>

<script setup>
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';

const route = useRoute();
const router = useRouter();

const actived = ref(route.name);

const handleMenuChange = (active) => {
  router.push({ name: active });
};
</script>

<style lang="less" scoped>
.t-menu {
  :deep(.t-menu__item) {
    height: 38px;
    &:nth-child(n + 2) {
      margin-top: 8px;
    }
  }
  .text {
    margin-left: 12px;
  }
}
</style>
