import Vue from 'vue';
import { create<PERSON><PERSON>, PiniaVuePlugin } from 'pinia';
import TDesign from 'tdesign-vue';
import 'tdesign-vue/es/style/index.css';
import SDCWebUIVue from '@tencent/sdc-webui-vue';
import '@tencent/sdc-webui-vue/lib/theme-grace/index.css';
import App from './App.vue';
import router from './router';
import { useCommonStore } from '@/stores/common';
import '@/styles/index.less';
import DesIcon from '@/components/DesIcon.vue';
import DesRectIcon from '@/components/DesRectIcon.vue';
import '@/utils/sql-language';
import crontab from 'vue-crontab-ui';
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate';
import '@/directives';
import { getUserInfo } from '@/stores/system-storage';
import dayjs from 'dayjs';
import isBetween from 'dayjs/plugin/isBetween';
import duration from 'dayjs/plugin/duration';
import minMax from 'dayjs/plugin/minMax';

dayjs.extend(isBetween);
dayjs.extend(duration);
dayjs.extend(minMax);

Vue.use(PiniaVuePlugin);
const pinia = createPinia();
pinia.use(piniaPluginPersistedstate);
Vue.use(pinia);
Vue.use(TDesign);
Vue.use(SDCWebUIVue);
Vue.use(crontab);
Vue.component('DesIcon', DesIcon);
Vue.component('DesRectIcon', DesRectIcon);

Vue.config.productionTip = false;

router.beforeEach(async (to, from, next) => {
  const commonStore = useCommonStore();
  if (to.meta?.auth && !commonStore.operateCodeList.includes(to.meta.auth)) {
    next({ name: 'notAuthorized' });
    return;
  }
  // 去掉登录后在url上的ticket和sessionKey
  if (to.query.ticket || to.query.sessionKey) {
    const { ticket, sessionKey, ...query } = to.query;
    next({
      ...to,
      query,
    });
  } else {
    next();
  }
});

getUserInfo().finally(() => {
  new Vue({
    router,
    pinia,
    render: h => h(App),
  }).$mount('#app');
});
