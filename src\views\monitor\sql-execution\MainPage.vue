<template>
  <div class="sql-execution">
    <section class="filter-wrap">
      <BasicSearch v-model="searchKey" placeholder="查询ID/用户/查询SQL/资源组/数据库.." @reset="handleReset" @change="searchChange">
        <template #prefix>
          <t-select
            v-model="datasourceId"
            :options="datasourceOptions"
            placeholder="数据源"
            filterable
            @change="searchChange"
          ></t-select>
        </template>
        <template #suffix>
          <t-date-range-picker v-model="time" :presets="presets" allow-input enable-time-picker :placeholder="['开始时间', '结束时间']" separator="至" @change="searchChange" style="width: 388px;">
            <template #suffixIcon>
              <time-icon />
            </template>
          </t-date-range-picker>
        </template>
      </BasicSearch>
    </section>

    <t-table style="width: 100%" rowKey="id" :data="tableData" :columns="columns" :pagination="pagination" max-height="100%">
      <template #empty v-if="!datasourceId">
        <no-data text="请先选择数据源"></no-data>
      </template>
    </t-table>

    <ViewRecordDialog ref="dialogRef" :getDatasourceLabel="getDatasourceLabel"></ViewRecordDialog>
  </div>
</template>

<script setup lang="jsx" name="sql-execution">
import { ref, onMounted, reactive } from 'vue';
import { useSearch, usePagination } from '@/hooks';
import BasicSearch from '@/components/BasicSearch.vue';
import ViewRecordDialog from './ViewRecordDialog.vue';
import { useMonitorStore } from '@/stores/monitor';
import { useOperationsStore } from '@/stores/operations';
import { to, formatTime } from '@/utils/util';
import { TimeIcon } from 'tdesign-icons-vue';
import dayjs from 'dayjs';
import NoData from '@/components/NoData.vue';

const monitorStore = useMonitorStore();
const operationsStore = useOperationsStore();

const dialogRef = ref(null);

const datasourceOptions = ref([]);

const getRecentlyTime = (n) => {
  // 获取当前时间
  const now = dayjs();

  // 计算n天前的0点0分0秒
  const startDay = now.subtract(n, 'day').startOf('day');

  // 获取今天的23点59分59秒999毫秒
  const endDay = now.endOf('day');

  return [startDay.toDate(), endDay.toDate()];
};

const time = ref(getRecentlyTime(1).map(item => formatTime(item)));

const getDatasourceLabel = (id) => {
  const datasource = datasourceOptions.value.find(item => item.value === id);
  return datasource ? datasource.label : '';
};

// 表格列
const columns = ref([
  { colKey: 'serial-number', width: 61, title: '序号', fixed: 'left' },
  {
    colKey: 'queryId',
    title: 'QueryID',
    width: 157,
    ellipsis: true,
  },
  {
    colKey: 'timestamp',
    title: 'Timestamp',
    width: 188,
    ellipsis: true,
  },
  {
    colKey: 'user',
    title: 'User',
    width: 82,
    ellipsis: true,
  },
  {
    colKey: 'resourceGroup',
    title: 'ResourceGroup',
    width: 146,
    ellipsis: true,
  },
  {
    colKey: 'db',
    title: 'Db',
    width: 179,
    ellipsis: true,
  },
  {
    colKey: 'queryTime',
    title: 'QueryTime',
    width: 182,
    ellipsis: true,
  },
  {
    colKey: 'stmt',
    title: 'Stmt',
    width: 179,
    ellipsis: true,
  },
  {
    title: '操作',
    colKey: 'operate',
    width: 61,
    fixed: 'right',
    cell: (h, { row }) => (
      <t-space>
        <t-link theme="primary" hover="color" onClick={ () => handleView(row) }>查看</t-link>
      </t-space>
    ),
  },
]);

// 表格数据
const tableData = ref([]);

const fetchData = async () => {
  if (!datasourceId.value) return;
  const { current: pageNum, pageSize } = pagination;
  const params = {
    pageNum,
    pageSize,
    queryData: {
      queryKey: searchKey.value,
      datasourceId: datasourceId.value,
      startTime: time.value[0],
      endTime: time.value[1],
    },
  };
  const [err, data] = await to(monitorStore.fetchSqlExecutionList(datasourceId.value, params));
  if (err) {
    return;
  }
  const { total, list } = data;
  setPaginationTotal(total);
  tableData.value = list;
};

const datasourceId = ref('');
const { searchKey, setSearchKey, onSearch } = useSearch(fetchData);
const { pagination, setPaginationCurrent, setPaginationTotal } = usePagination(fetchData);

const presets = reactive({
  最近7天: getRecentlyTime(6),
  最近3天: getRecentlyTime(2),
  今天: getRecentlyTime(0),
});

const searchChange = () => {
  setPaginationCurrent(1);
  onSearch();
};

const handleReset = () => {
  time.value = getRecentlyTime(1).map(item => formatTime(item));
  setSearchKey('');
  setPaginationCurrent(1);
  fetchData();
};

const getDataSourceOptions = async () => {
  const data = { pageNum: 1, pageSize: 100, queryData: { queryKey: '' } };
  const [err, result] = await to(operationsStore.fetchDatasourcesList(data));
  if (err) {
    return;
  }
  datasourceOptions.value = result.list.map(item => ({
    ...item,
    label: item.name,
    value: item.id,
  }));
};

const handleView = (row) => {
  dialogRef.value.openEditDialog(row);
};

// 生命周期钩子
onMounted(() => {
  getDataSourceOptions();
});
</script>

<style lang="less" scoped>
.sql-execution {
  height: 100%;
  padding: 20px 20px 0 20px;
  background: #fff;
  .filter-wrap {
    margin-bottom: 16px;
  }
  :deep(.t-table) {
    height: calc(100% - 48px);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
}
:deep(.basic-search-space__wrap) {
  .basic-search-input {
    width: 458px;
  }
}
</style>
