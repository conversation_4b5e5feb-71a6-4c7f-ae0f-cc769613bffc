<script setup>
import { ref, reactive, computed, defineProps, defineExpose, watch, defineEmits } from 'vue';
import { MessagePlugin } from 'tdesign-vue';
import { CronosExpression, validate } from 'cronosjs';
import CrontabSelect from '@/components/CrontabSelect.vue';
import { useDataDevelopmentStore } from '@/stores/data-development';
import { showDialogConfirm } from '@/utils/dialog';
import dayjs from 'dayjs';
import { formatTime, isEmptyValue } from '@/utils/util';

const emit = defineEmits(['refresh']);

const props = defineProps({
  spaceId: {
    type: String,
    default: '',
  },
});
const visible = ref(false);
const formRef = ref(null);
const dataDevelopmentStore = useDataDevelopmentStore();

const formData = reactive({
  id: '',
  name: '',
  processDefinitionCode: '',
  createTime: '',
  updateTime: '',
  startTime: dayjs().startOf('day')
    .format('YYYY-MM-DD HH:mm:ss'),
  endTime: '9999-12-31 23:59:59',
  crontabFrom: 'CUSTOM',
  // cron表达式
  crontab: '',
  // 失败策略
  failureStrategy: '',
  // 流程优先级
  jobPriority: '',
  state: '',
});

const rules = {
  startTime: [{ required: true, message: '请选择开始时间' }],
  endTime: [{ required: true, message: '请选择结束时间' }],
  crontabFrom: [{ required: true, message: '请选择定时设置' }],
  crontab: [{ required: true }, { validator: checkCron }],
  failureStrategy: [{ required: true, message: '请选择失败策略' }],
  jobPriority: [{ required: true, message: '请选择流程优先级' }],
};

function checkCron(val) {
  if (!val) {
    return {
      result: false,
      message: 'cron表达式不能为空',
      type: 'error',
    };
  }
  // 校验cron表达式
  if (!validate(val)) {
    return {
      result: false,
      message: 'cron表达式错误',
      type: 'error',
    };
  }

  if (formData.startTime) {
    if (dayjs().isBefore(formData.startTime)) {
      return {
        result: true,
      };
    }
  }
  const nextDate = CronosExpression.parse(val).nextDate();
  if (formData.startTime && formData.endTime && !dayjs(nextDate).isBetween(formData.startTime, formData.endTime)) {
    return {
      result: false,
      message: `下次执行时间是${dayjs(nextDate).format('YYYY-MM-DD: HH:mm:ss')}，不在起止时间内`,
      type: 'error',
    };
  }
  return {
    result: true,
  };
}

function handleTimeChange() {
  triggerCheckCron();
  showTime.value = false;
}

async function triggerCheckCron() {
  if (formData.crontab) {
    const valid = await formRef.value.validate({ fields: ['crontab'] });
    return valid;
  }
}

function handleCrontabFromChange() {
  formData.crontab = '0 0 0 * * ?';
  showTime.value = false;
}

function getSubData() {
  return {
    id: formData.id,
    spaceId: props.spaceId,
    processDefinitionCode: formData.processDefinitionCode,
    startTime: formData.startTime,
    endTime: formData.endTime || null,
    crontabFrom: formData.crontabFrom,
    crontab: formData.crontab,
    failureStrategy: formData.failureStrategy,
    jobPriority: formData.jobPriority,
  };
}
const allowSubmit = computed(() => {
  const flag = formData.startTime && formData.crontabFrom && formData.crontab && formData.failureStrategy && formData.jobPriority;
  // 编辑态
  if (formData.id) {
    // 定时管理上线状态禁止提交
    return formData.state !== 'ONLINE' && flag;
  }
  // 新增态只需检查必填字段
  return flag;
});

function handleClose() {
  close();
}
async function handleConfirm() {
  const result = await formRef.value.validate();
  if (result === true) {
    const subData = getSubData();
    await dataDevelopmentStore.fetchFlowTimerPut(subData);
    emit('refresh');
    MessagePlugin.success('提交成功');
    close();
  }
}
async function handleDelete() {
  const result = await showDialogConfirm({
    title: `确定删除工作流「${formData.name}」的定时管理吗？`,
    body: () => (<span>删除后数据<b class="des-common-warn-text">无法恢复</b>，请谨慎操作。</span>),
    width: '440px',
  });
  if (result.type === 'success') {
    await dataDevelopmentStore.fetchFlowTimerDelete(formData.id);
    emit('refresh');
    close();
    MessagePlugin.success('删除成功');
  }
}

async function handleOnline() {
  await dataDevelopmentStore.fetchFlowTimerOnline(formData.id);
  emit('refresh');
  close();
  MessagePlugin.success('上线成功');
}
async function handleOffline() {
  await dataDevelopmentStore.fetchFlowTimerOffline(formData.id);
  emit('refresh');
  close();
  MessagePlugin.success('下线成功');
}

async function open(row) {
  visible.value = true;
  formData.name = row.name;
  formData.processDefinitionCode = row.code;
  const { data } = await dataDevelopmentStore.fetchFlowTimerDetail({ processCode: row.code, spaceId: props.spaceId });
  if (data) {
    formData.id = data.id;
    formData.startTime = data.startTime;
    formData.endTime = data.endTime;
    formData.crontabFrom = data.crontabFrom;
    formData.crontab = data.crontab;
    formData.failureStrategy = data.failureStrategy;
    formData.jobPriority = data.jobPriority;
    formData.state = data.state;
    formData.createTime = data.createTime;
    formData.updateTime = data.updateTime;
  }
  if (isEmptyValue(formData.crontab)) {
    formData.crontab = '0 0 0 * * ?';
  }
}
function close() {
  visible.value = false;
  formRef.value.reset();
  formData.id = '';
  formData.name = '';
  formData.processDefinitionCode = '';
  formData.startTime = dayjs().startOf('day')
    .format('YYYY-MM-DD HH:mm:ss');
  formData.endTime = '9999-12-31 23:59:59';
  formData.crontabFrom = 'CUSTOM';
  formData.crontab = '';
  formData.failureStrategy = '';
  formData.jobPriority = '';
  formData.state = '';
  formData.createTime = '';
  formData.updateTime = '';
}

function statusText(status) {
  switch (status) {
    case 'OFFLINE':
      return '下线';
    case 'ONLINE':
      return '在线';
    default:
      return '未上线';
  }
}

function getChineseByNumber(num) {
  return {
    1: '一',
    2: '二',
    3: '三',
    4: '四',
    5: '五',
  }[num];
}
const showTime = ref(false);
const nextDates = ref([]);
async function handleExcuteTime() {
  if (formData.startTime) {
    const valid = await triggerCheckCron();
    if (valid === true) {
      const time = dayjs.max(dayjs(), dayjs(formData.startTime)).toDate();

      // 获取符合条件的最近5次执行时间
      const result = CronosExpression.parse(formData.crontab).nextNDates(time, 5);

      // 根据是否有结束时间进行不同过滤
      nextDates.value = result.filter((time) => {
        const timeDayjs = dayjs(time);
        return formData.endTime
          ? timeDayjs.isBetween(formData.startTime, formData.endTime, null, '[]') // []表示包含起止时间
          : timeDayjs.isAfter(formData.startTime);
      }).map(time => formatTime(time));

      showTime.value = true;
    } else {
      showTime.value = false;
    }
  } else {
    showTime.value = false;
    MessagePlugin.error('请选择起止时间');
  }
}

watch(() => formData.crontab, () => {
  showTime.value = false;
});
defineExpose({ open, close });
</script>

<template>
  <t-dialog
    class="des-common-dialog"
    width="560"
    :visible.sync="visible"
    :closeOnOverlayClick="false"
    :onClosed="handleClose"
    header="定时管理"
  >
    <t-form class="des-data-dev__flow-define__flow-timed" :data="formData" ref="formRef" labelWidth="106px" :rules="rules">
      <t-form-item label="工作流名称">
        <div>{{ formData.name }}</div>
      </t-form-item>
      <t-form-item label="起止时间" name="startTime">
        <div class="time-range">
          <t-date-picker style="width: 188px;" v-model="formData.startTime" enable-time-picker allow-input clearable format="YYYY-MM-DD HH:mm:ss" placeholder="请选择开始时间" @change="handleTimeChange"></t-date-picker>
          <span>至</span>
          <t-date-picker style="width: 188px;" v-model="formData.endTime" enable-time-picker allow-input clearable format="YYYY-MM-DD HH:mm:ss" placeholder="请选择结束时间" @change="handleTimeChange"></t-date-picker>
        </div>
      </t-form-item>
      <t-form-item label="定时设置" name="crontabFrom">
        <t-radio-group v-model="formData.crontabFrom" @change="handleCrontabFromChange">
          <t-radio value="CUSTOM">输入</t-radio>
          <t-radio value="SELECT">选择</t-radio>
        </t-radio-group>
      </t-form-item>
      <t-form-item label="定时" name="crontab">
        <div class="crontab-wrap">
          <t-input v-model="formData.crontab" v-if="formData.crontabFrom === 'CUSTOM'"></t-input>
          <crontab-select v-model="formData.crontab" v-else></crontab-select>
          <t-button theme="primary" variant="text" @click="handleExcuteTime">执行时间</t-button>
        </div>
      </t-form-item>
      <section class="strategy-section" v-show="showTime">
        <div class="strategy-section-title">接下来{{ getChineseByNumber(nextDates.length) }}次执行时间</div>
        <p v-for="(time, index) in nextDates" :key="index">{{ time }}</p>
      </section>
      <t-form-item label="失败策略" name="failureStrategy">
        <t-radio-group v-model="formData.failureStrategy">
          <t-radio value="CONTINUE">继续</t-radio>
          <t-radio value="END">结束</t-radio>
        </t-radio-group>
      </t-form-item>
      <t-form-item label="流程优先级" name="jobPriority">
        <t-select v-model="formData.jobPriority">
          <t-option label="最高" value="HIGHEST"></t-option>
          <t-option label="高" value="HIGH"></t-option>
          <t-option label="中" value="MEDIUM"></t-option>
          <t-option label="低" value="LOW"></t-option>
          <t-option label="最低" value="LOWEST"></t-option>
        </t-select>
      </t-form-item>
      <t-form-item label="状态">
        <span class="status" :style="{ color: formData.state === '' ? '#999' : 'inherit' }">{{ statusText(formData.state) }}</span>
      </t-form-item>
      <template v-if="formData.id">
        <t-form-item label="创建时间">
          <div>{{ formData.createTime }}</div>
        </t-form-item>
        <t-form-item label="更新时间">
          <div>{{ formData.updateTime }}</div>
        </t-form-item>
      </template>
    </t-form>
    <template #footer>
      <t-row justify="space-between">
        <t-col>
          <t-space size="10px" v-if="formData.id">
            <t-button theme="default" variant="outline" :disabled="formData.state !== 'OFFLINE'" @click="handleOnline">上线</t-button>
            <t-button theme="default" variant="outline" :disabled="formData.state !== 'ONLINE'" @click="handleOffline">下线</t-button>
          </t-space>
        </t-col>
        <t-col>
          <t-space size="8px">
            <t-button theme="default" variant="outline" v-if="formData.id" :disabled="formData.state === 'ONLINE'" @click="handleDelete">删除</t-button>
            <t-button theme="default" variant="outline" @click="handleClose">取消</t-button>
            <t-button theme="primary" @click="handleConfirm" :disabled="!allowSubmit">提交</t-button>
          </t-space>
        </t-col>
      </t-row>
    </template>
  </t-dialog>
</template>

<style lang="less" scoped>
.des-data-dev__flow-define__flow-timed {
  .time-range {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  .crontab-wrap {
    width: 100%;
    display: flex;
  }
  .strategy-section {
    margin-top: 24px;
    padding: 12px 20px;
    background-color: #F4F4F4;
    margin-bottom: 16px;
    p {
      margin-bottom: 10px;
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  .strategy-section-title {
    color: #333333;
    text-align: left;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
    margin-bottom: 10px;
  }
}
</style>
