<script setup>
import { ref, reactive, defineExpose, computed } from 'vue';
import BasicDialog from '@/components/BasicDialog.vue';

const visible = ref(false);
const formRef = ref(null);

const formData = reactive({
  name: '物化视图刷新',
  // 申请类型
  typeName: '工作流申请',
  // 权限类型
  authType: [],
  // 原因
  reason: '',
});

const rules = {
  authType: [{ required: true, message: '请选择权限类型' }],
};

const authTypeOption = ref([
  { label: '编辑', value: 'edit' },
  { label: '运行', value: 'execute' },
  { label: '查看', value: 'view' },
]);
function handleSelectAll(checked) {
  formData.authType = checked ? authTypeOption.value.map(item => item.value) : [];
}
const checkAll = computed(() => authTypeOption.value.length === formData.authType.length);
const indeterminate = computed(() => !!(authTypeOption.value.length > formData.authType.length && formData.authType.length));

function handleClose() {
  close();
}
function handleConfirm() {
  formRef.value.validate().then((result) => {
    if (result === true) {
      console.log('表单校验通过');
    }
  });
}

function open() {
  visible.value = true;
}
function close() {
  visible.value = false;
  formRef.value.reset();
}

defineExpose({ open, close });
</script>

<template>
  <BasicDialog
    width="560"
    :visible.sync="visible"
    header="申请权限"
    @handleClose="handleClose"
    @handleConfirm="handleConfirm"
  >
    <t-form :data="formData" ref="formRef" labelWidth="106px" :rules="rules">
      <t-form-item label="申请名称">
        <span>{{ formData.name }}</span>
      </t-form-item>
      <t-form-item label="申请类型">
        <span>{{ formData.typeName }}</span>
      </t-form-item>
      <t-form-item label="权限类型" name="authType">
        <t-space direction="vertical" :size="12">
          <div style="padding-top: 6px;">
            <t-checkbox :checked="checkAll" :indeterminate="indeterminate" :onChange="handleSelectAll">全选</t-checkbox>
          </div>
          <t-checkbox-group v-model="formData.authType" :options="authTypeOption" />
        </t-space>
      </t-form-item>
      <t-form-item label="申请原因" name="reason">
        <t-textarea v-model="formData.reason" :autosize="{ minRows: 3, maxRows: 5 }" :maxlength="200" placeholder="请输入申请原因..."></t-textarea>
      </t-form-item>
    </t-form>
  </BasicDialog>
</template>

<style lang="less" scoped>
</style>
