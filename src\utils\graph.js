import { StringExt } from '@antv/x6';
import { register } from '@antv/x6-vue-shape';
import { DagreLayout } from '@antv/layout';

const NODE_WIDTH = 240;
const NODE_HEIGHT = 68;
/**
 * 注册自定义节点
 * @param {string} shapName 节点名称
 * @param {object} component 节点Vue组件
 * @param {object} options 节点相关配置
 * @param {object} options.width 节点宽度
 * @param {object} options.height 节点高度
 */
export function registerCustomNode(shapName, component, options = {}) {
  const { width = NODE_WIDTH, height = NODE_HEIGHT, direction = 'horizontal' } = options;
  register({
    shape: shapName,
    width,
    height,
    component,
    ports: {
      // 定义透明桩，鼠标移入再显示
      groups: {
        in: {
          position: direction === 'horizontal' ? 'left' : 'top',
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: 'transparent',
              strokeWidth: 1,
              fill: 'transparent',
            },
          },
        },
        out: {
          position: direction === 'horizontal' ? 'right' : 'bottom',
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: 'transparent',
              strokeWidth: 1,
              fill: 'transparent',
            },
          },
        },
      },
    },
  });
}

/**
 * 创建节点
 * @param {string} shape 节点类型
 * @param {object} data 节点携带数据
 * @param {object} position 节点位置
 * @param {string} position.x 节点属性
 * @param {string} position.y 节点属性
 * @param {Graph} graph 图形实例
 * @param {string} customId 自定义节点id
 */
export function createNode(shape, data = {}, position = {}, graph, customId = '') {
  if (!graph) {
    return {};
  }
  let newNode = {};
  const id =  customId || StringExt.uuid();
  const node = {
    id,
    shape,
    x: position?.x,
    y: position?.y,
    ports: [
      { id: `${id}-in`, group: 'in' },
      { id: `${id}-out`, group: 'out' },
    ],
    data,
  };
  newNode = graph.addNode(node);
  return newNode;
}
/**
 * 创建边
 * @param {string} source 来源ID
 * @param {string} target 目标ID
 * @param {Graph} graph 图形实例
 */
export function createEdge(source, target, graph) {
  const edge = {
    id: StringExt.uuid(),
    source: {
      cell: source,
      port: `${source}-out`,
    },
    target: {
      cell: target,
      port: `${target}-in`,
    },
    zIndex: -1,
    attrs: {
      line: {
        stroke: '#3464E0',
        strokeWidth: 1,
        targetMarker: {
          name: 'block',
          width: 12,
          height: 8,
        },
      },
    },
  };
  if (graph) {
    graph.addEdge(edge);
  }
}

/**
 * 根据起点初始上下游节点的位置信息
 * @param {Node} node 起始节点
 * @param {Graph} graph
 * @param {number} dx x轴偏移量
 * @param {number} dy y轴偏移量
 * @returns
 */
export function getStreamNodePosition(type = 'next', node, graph, dx = 400, dy = 100) {
  // 找出画布中以该起始节点为起点的相关边的终点id集合
  const downstreamNodeIdList = [];
  graph.getEdges().forEach((edge) => {
    if (type === 'next') {
      if (edge.source.cell === node.id) {
        downstreamNodeIdList.push(edge.target.cell);
      }
    } else {
      if (edge.target.cell === node.id) {
        downstreamNodeIdList.push(edge.source.cell);
      }
    }
  });
  // 获取起点的位置信息
  const position = node.getPosition();
  let minX = Infinity;
  let maxY = -Infinity;
  graph.getNodes().forEach((graphNode) => {
    if (downstreamNodeIdList.indexOf(graphNode.id) > -1) {
      const nodePosition = graphNode.getPosition();
      // 找到所有节点中最左侧的节点的x坐标
      if (nodePosition.x < minX) {
        minX = nodePosition.x;
      }
      // 找到所有节点中最下方的节点的y坐标
      if (nodePosition.y > maxY) {
        maxY = nodePosition.y;
      }
    }
  });
  if (type === 'next') {
    return {
      x: minX !== Infinity ? minX : position.x + dx,
      y: maxY !== -Infinity ? maxY + dy : position.y,
    };
  }
  return {
    x: minX !== Infinity ? minX : position.x - dx,
    y: maxY !== -Infinity ? maxY + dy : position.y,
  };
};
// 垂直布局的情况计算上下游位置
export function getStreamNodePosition2(type = 'next', node, graph, dx = 400, dy = 120) {
  const downstreamNodeIdList = [];
  graph.getEdges().forEach((edge) => {
    if (type === 'next') {
      if (edge.source.cell === node.id) {
        downstreamNodeIdList.push(edge.target.cell);
      }
    } else {
      if (edge.target.cell === node.id) {
        downstreamNodeIdList.push(edge.source.cell);
      }
    }
  });
  // 获取起点的位置信息
  const position = node.getPosition();
  let maxX = -Infinity;
  let minY = Infinity;
  graph.getNodes().forEach((graphNode) => {
    if (downstreamNodeIdList.indexOf(graphNode.id) > -1) {
      const nodePosition = graphNode.getPosition();
      if (nodePosition.x > maxX) {
        maxX = nodePosition.x;
      }
      // 找到所有节点中最下方的节点的y坐标
      if (nodePosition.y < minY) {
        minY = nodePosition.y;
      }
    }
  });
  if (type === 'next') {
    return {
      x: maxX !== -Infinity ? maxX + dx : position.x,
      y: minY !== Infinity ? minY : position.y + dy,
    };
  }
  return {
    x: maxX !== -Infinity ? maxX + dx : position.x,
    y: minY !== Infinity ? minY : position.y,
  };
}

/**
 * 创建上下游节点
 * @param {string} shape 节点类型
 * @param {object} data 节点数据
 * @param {Node} node 上游节点
 * @param {Graph} graph 画布实例
 */
export function createStreamNode(type = 'next', shape, data, node, graph, direction = 'horizontal') {
  if (graph) {
    // 获取下游节点的初始位置信息
    const position = direction === 'horizontal' ? getStreamNodePosition(type, node, graph) : getStreamNodePosition2(type, node, graph);
    // 创建下游节点
    const newNode = createNode(shape, data, position, graph);
    const source = node.id;
    const target = newNode.id;
    if (type === 'next') {
      // 创建该节点出发到下游节点的边
      createEdge(source, target, graph);
    } else {
      // 创建上游节点，线要反着画
      createEdge(target, source, graph);
    }
    return newNode;
  }
}

/**
 * 重排节点
 * @param {Graph} graph 画布实例
 * @param {object} options 配置
 * @param {string} options.direction 排列方向，水平horizontal或者垂直vertical
 * @param {number} options.ranksep 垂直间距，默认130
 * @param {number} options.nodesep 水平间距，默认130
 * @returns
 */
export function reLayout(graph, options = {}) {
  const { direction = 'horizontal', nodeSize = [NODE_WIDTH, NODE_HEIGHT], ranksep = 130, nodesep = 130 } = options;
  // 获取所有节点和边
  const nodes = graph.getNodes();
  const edges = graph.getEdges();

  if (nodes.length === 0) return;

  // 使用Dagre布局算法自动排列节点
  const dagreLayout = new DagreLayout({
    type: 'dagre',
    rankdir: direction === 'horizontal' ? 'LR' : 'TB',  // 从左到右布局
    align: 'UL',    // 对齐方式
    nodeSize, // 节点尺寸
    ranksep: direction === 'horizontal' ? ranksep : 30,    // 层级间距
    nodesep: direction === 'vertical' ? nodesep : 30,    // 节点间距
  });

  // 准备布局数据
  const layoutData = {
    nodes,
    edges,
  };

  const model = dagreLayout.layout(layoutData);

  model.nodes.forEach(({ id, x, y }) => {
    const node = graph.getCellById(id);
    node.setPosition({ x, y });
  });

  // 居中显示所有内容
  // graph.centerContent();

  // 调整画布视图，添加边距
  graph.zoomToFit({
    padding: 100,
    maxScale: 1,
  });
}
