// 我的空间
export const API_SPACES_MY = () => '/api/spaces/my';
// 全部空间
export const API_SPACES_LIST = () => '/api/spaces/list';
// 操作空间（新增/修改/删除/详情，除新增外需要增加id作为参数）
export const API_SPACES_OPERATE = (id = '') => (id ? `/api/spaces/${id}` : '/api/spaces');
// 查询空间是否存在开发数据
export const API_SPACES_HASDATA = (id = '') => `/api/spaces/${id}/hasData`;
// 判断Code标识是否已使用
export const API_SPACES_CHECK_CODE = () => '/api/spaces/checkCode';
// 判断是否是空间owner
export const API_SPACES_IS_OWNER = (id = '') => `/api/spaces/${id}/isOwner`;
// 判断某个用户是否某个空间负责人
export const API_SPACES_IS_OWNER_BY_STAFF = (spaceId, staffId) => `/api/spaces/${spaceId}/${staffId}/isOwner`;
// 判断该空间下的工作流名称是否已使用
export const API_SPACES_PROCESSES_CHECKNAME = (spaceId, name) => `/api/spaces/${spaceId}/processes/${name}/checkName`;
// 获取当前空间的负责人组下的成员
export const API_SPACES_OWNER_GROUP_MEMBERS = spaceId => `/api/spaces/${spaceId}/userGroup/getOwnersBySpaceId`;
// 判断用户组名称是否已使用
export const API_SPACES_USER_GROUP_CHECK_NAME = (spaceId, name) => `/api/spaces/${spaceId}/userGroup/${name}/checkName`;

// 操作告警配置（新增/修改/删除/详情）
export const API_ALARM_OPERATE = (id = '') => (id ? `/api/alertConfig/${id}` : '/api/alertConfig');
// 测试告警
export const API_ALARM_TEST = () => '/api/alertConfig/testAlert';
// 告警列表（分页）
export const API_ALARM_LIST = () => '/api/alertConfig/list';
// 查询领域列表
export const API_SPACES_DOMAIN_LIST = () => '/api/spaces/orgAreaList';
// 查询空间成员列表
export const API_SPACES_MEMBERS = () => '/api/spaces/list_members';
// 新增空间成员
export const API_SPACES_MEMBERS_ADD = () => '/api/spaces/addMember';
// 修改空间成员
export const API_SPACES_MEMBERS_EDIT = () => '/api/spaces/editMember';
// 删除空间成员
export const API_SPACES_MEMBERS_DELETE = () => '/api/spaces/deleteMember';
// 转让空间负责人
export const API_SPACES_MEMBERS_TRANSFER = () => '/api/spaces/transfer';
// 判断空间告警名称是否重复
export const API_SPACES_ALARM_CHECK_NAME = () => '/api/alertConfig/checkAlertName';

// 用户组操作（添加/查询/编辑/删除，除新增外需要增加id作为参数）
export const API_SPACES_USER_GROUP_OPERATE = (spaceId, id = '') => (id ? `/api/spaces/${spaceId}/userGroup/${id}` : `/api/spaces/${spaceId}/userGroup`);
// 查询用户组列表（分页）
export const API_SPACES_USER_GROUP_LIST = spaceId => `/api/spaces/${spaceId}/userGroup/list`;

// 工作流授权详情 permissionKey传参用户id或 者 用户组id
export const API_SPACES_FLOW_AUTH_DETAIL = (spaceId, permissionKey) => `/api/spaces/${spaceId}/userGroup/processPermissionDetail/${permissionKey}`;
// Git授权详情 permissionKey传参用户id 或者 用户组id
export const API_SPACES_GIT_AUTH_DETAIL = (spaceId, permissionKey) => `/api/spaces/${spaceId}/userGroup/gitPermissionDetail/${permissionKey}`;
// 工作流授权
export const API_SPACES_FLOW_AUTH = spaceId => `/api/spaces/${spaceId}/userGroup/grantForProcess`;
// Git授权
export const API_SPACES_GIT_AUTH = spaceId => `/api/spaces/${spaceId}/userGroup/grantForGit`;

// 权限迁移
export const API_SPACES_PRIVILEGE_MIGRATION  = () => '/api/spaces/privilegeMigration';
