<template>
  <section class="workbench-tree__wrap">
    <slot></slot>
    <t-input clearable placeholder="关键词搜索..." class="filter" v-model="filterText" @change="onInputChange">
      <template #suffixIcon>
        <search-icon :style="{ cursor: 'pointer' }" />
      </template>
    </t-input>
    <t-tree line hover expand-all activable :data="data" :label="label" :icon="icon" v-bind="{ ...$attrs }" @active="handleActive" />
  </section>
</template>

<script setup lang="jsx">
import { debounce } from 'lodash';
import { defineProps, ref, onMounted, useAttrs, defineEmits, watch, defineExpose } from 'vue';
import { SearchIcon } from 'tdesign-icons-vue';
import DesIcon from '@/components/DesIcon.vue';
import EllipsisWithTooltip from '@/components/EllipsisWithTooltip.vue';
import { taskTypeColorMapping } from '../data-development/main-tabs/work-flow-panel/utils/task-type';

const $attrs = useAttrs();
const props = defineProps({
  data: {
    type: Array,
    required: true,
  },
});
const emit = defineEmits(['click', 'search']);

// 移除最后级数据的icon
const removeIcon = () => {
  const icons = document.querySelectorAll('.t-tree__icon');
  icons.forEach((dom) => {
    if (dom.children.length === 0) {
      dom.remove();
    }
  });
};
// 移除span标签的提示
const removeTip = () => {
  const labels = document.querySelectorAll('.t-tree__label');
  labels.forEach((dom) => {
    dom.removeAttribute('title');
  });
};

const resetTreeStyle = () => {
  setTimeout(() => {
    removeIcon();
    removeTip();
  }, 0);
};

watch(() => props.data, () => {
  // 数据变更重新清除样式
  resetTreeStyle();
}, { deep: true });

const filterText = ref('');

const onInputChange = (value) => {
  if (value) {
    // 获取全部数据
    handleSearch(value);
  } else {
    // 获取第一层数据
    handleRefresh();
  }
};

const handleSearch = debounce((value) => {
  emit('search', value);
}, 300);

const handleRefresh = debounce(() => {
  emit('refresh');
}, 300);

const label = (h, node) => {
  const iconMap = {
    FLOW: 'des-micon-gongzuoliu',
    TASK: 'des-icon-linwujiedian',
    catalog: 'des-micon-catalog',
    shujuku: 'des-micon-shujuku',
    biao: 'des-micon-biao',
    shitu: 'des-micon-shitu',
    wuhuashitu: 'des-micon-wuhuashitu',
    hanshu: 'des-micon-hanshu',
    ceng: 'des-micon-ceng',
    yu: 'des-micon-yu',
    icon1: 'des-icon-biao',
    hanshu1: 'des-icon-hanshu',
  };
  const style = {
    color: taskTypeColorMapping[node.data.taskType],
  };
  const name = iconMap[node.data.type];
  return (
    <div style="width: 100%; height: 100%; display: flex; align-items: center;">
      <DesIcon name={name} size="16" style={ style }></DesIcon>
      <EllipsisWithTooltip style="flex: 1;margin-left: 5px;" text={node.label}></EllipsisWithTooltip>
      {!node.data.hasRight && (
        <t-tooltip content="无权限" style="margin-left: 5px;">
          <DesIcon name="des-icon-suomoquanwen" size="16" style="color: #ccc;"></DesIcon>
        </t-tooltip>
      )}
    </div>
  );
};

const icon = (h, node) => {
  let name = '';
  if (node.getChildren()) {
    name = node.expanded ? 'des-icon-jianxiao' : 'des-icon-jiaxiao';
  }
  return name ? <DesIcon name={name} size="14" style="color: #2A2A2A;"></DesIcon> : null;
};

const handleActive = (value, context) => {
  emit('click', { value, context });
};

const getSearchValue = () => filterText.value;

onMounted(() => {
  removeIcon();
  removeTip();
});

defineExpose({
  resetTreeStyle,
  getSearchValue,
});
</script>

<style lang="less" scoped>
.workbench-tree__wrap {
  height: calc(100% - 96px);
  padding: 12px;
  display: flex;
  flex-direction: column;

  .filter {
    margin-bottom: 8px;
  }

  .t-tree {
    flex: 1;
    overflow-y: auto;
  }
  :deep(.t-tree__icon) {
    width: 16px;
    height: 16px;
    border: 1px solid #dcdcdc;
    border-radius: 2px;
  }
  :deep(.t-tree__label) {
    width: 100%;
    line-height: 29px;
    > span {
      display: inline-block;
      width: 100%;
    }
  }
}
</style>
