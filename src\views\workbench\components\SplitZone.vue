<template>
  <Splitpanes horizontal class="default-theme">
    <Pane :size="100">
      <slot></slot>
    </Pane>
    <Pane :size="0">
      <slot name="bottom"></slot>
    </Pane>
  </Splitpanes>
</template>

<script setup>
import { Splitpanes, Pane } from 'splitpanes';
import 'splitpanes/dist/splitpanes.css';
</script>

<style lang="less" scoped>
.splitpanes {
  height: calc(100% - 96px);
  :deep(.splitpanes__pane) {
    background: #fff;
  }
}
</style>
