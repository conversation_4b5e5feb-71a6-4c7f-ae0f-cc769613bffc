<template>
  <t-dialog
    class="des-common-dialog"
    :closeOnOverlayClick="false"
    v-bind="{ ...$attrs }"
    @update:visible="emit('update:visible', false)"
  >
    <slot></slot>

    <template #footer v-if="showFooter">
      <div class="footer-wrap">
        <t-space :size="8">
          <t-button theme="default" variant="outline" @click="emit('handleClose')">取消</t-button>
          <t-button :disabled="submitDisabled" @click="emit('handleConfirm')" v-if="showConfirmButton">{{ confirmButtonText }}</t-button>
        </t-space>
        <slot name="footer-left"></slot>
      </div>
    </template>
  </t-dialog>
</template>

<script setup>
import { defineProps, defineEmits, useAttrs } from 'vue';

const $attrs = useAttrs();
const emit = defineEmits(['update:visible', 'handleClose', 'handleConfirm']);

defineProps({
  submitDisabled: {
    type: Boolean,
    default: false,
  },
  confirmButtonText: {
    type: String,
    default: '提交',
  },
  showFooter: {
    type: Boolean,
    default: true,
  },
  showConfirmButton: {
    type: Boolean,
    default: true,
  },
});
</script>

<style lang="less" scoped>
.des-common-dialog {
  .footer-wrap {
    flex-direction: row-reverse;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}
</style>
