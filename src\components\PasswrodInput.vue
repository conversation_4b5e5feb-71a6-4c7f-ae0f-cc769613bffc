<template>
  <section style="width: 100%;">
    <t-input :value="value" :placeholder="placeholder" type="password" @change="handleChange" v-if="mode === 'ADD' || inputing === true" />
    <t-link theme="primary" hover="color" :underline="false" v-else @click="handleClick">输入(新)密码</t-link>
  </section>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue';

defineProps({
  value: {
    default: '',
    required: true,
  },
  placeholder: {
    type: String,
    default: '请输入',
  },
  mode: {
    type: String, // ADD、EDIT
    required: true,
  },
  inputing: {
    type: Boolean, // 是否正在输入
  },
});

const emit = defineEmits(['input', 'change']);

const handleChange = (value) => {
  emit('input', value);
  emit('change');
};

const handleClick = () => {
  emit('updateInputing', true);
};
</script>
