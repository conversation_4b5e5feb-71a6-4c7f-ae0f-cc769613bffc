<script setup>
import { reactive, ref, defineExpose, watch, defineProps, onMounted, computed } from 'vue';
import BlockHeader from '@/components/BlockHeader.vue';
import BasicConfig from './common/BasicConfig.vue';
import ResourceConfig from './common/ResourceConfig.vue';
import AlarmConfig from './common/AlarmConfig.vue';
import PreTask from './common/PreTask.vue';
import GitFile from './common/GitFile.vue';
import { JDBC_SQL } from '../../utils/task-type.js';
import { jdbcSqlRules } from '../../utils/rules.js';
import BasicSqlEditor from '@/components/BasicSqlEditor.vue';
import { useDataCatalogStore } from '@/stores/data-catalog';
import { useRoute } from 'vue-router/composables';

defineProps({
  currentFlowName: {
    type: String,
    default: '',
  },
  readonly: {
    type: Boolean,
    default: false,
  },
});
const route = useRoute();
const formRef = ref(null);
const formData = reactive({
  taskType: JDBC_SQL,
  id: '',
  // 节点名称
  name: '',
  // 节点标识
  code: '',
  // 节点描述
  description: '',
  // 失败重试次数
  failRetryTimes: 0,
  // 失败重试间隔
  failRetryInterval: 0,
  // 延迟执行
  delayTime: 0,
  // 自定义参数
  // params: [
  //   { key: '1', name: '参数1', relation: 'in', type: 'string', parameter: 'value' },
  // ],
  // 运行集群
  resourceGroupId: '',
  // cpu
  minCpuCores: 0.5,
  // 内存
  minMemorySpace: 512,
  // 超时告警开关
  timeoutFlag: false,
  // 超时时长
  timeout: 0,
  // 超时是否失败
  timeoutFailed: false,
  taskProperties: {
    // 数据源类型
    datasourceType: '',
    // 数据源实例
    datasourceId: '',
    // 脚本来源
    scriptType: '',
    // SQL语句
    scriptContent: '',
    // 项目
    gitProjectId: '',
    // 文件
    gitFile: '',
  },
});
const nodeId = ref('');

function getData() {
  return {
    taskType: formData.taskType,
    id: formData.id,
    name: formData.name,
    code: formData.code,
    description: formData.description,
    failRetryTimes: formData.failRetryTimes,
    failRetryInterval: formData.failRetryInterval,
    delayTime: formData.delayTime,
    resourceGroupId: formData.resourceGroupId,
    minCpuCores: formData.minCpuCores,
    minMemorySpace: formData.minMemorySpace,
    timeoutFlag: formData.timeoutFlag,
    timeout: formData.timeout,
    timeoutFailed: formData.timeoutFailed,
    taskProperties: {
      datasourceType: formData.taskProperties.datasourceType,
      datasourceId: formData.taskProperties.datasourceId,
      scriptType: formData.taskProperties.scriptType,
      scriptContent: formData.taskProperties.scriptContent,
      gitProjectId: formData.taskProperties.gitProjectId,
      gitFile: formData.taskProperties.gitFile,
    },
  };
}
const scriptTypeOptions = ref([
  { label: '手工编写', value: 'CONTENT' },
  { label: 'GIT项目', value: 'GIT_FILE' },
]);

// 数据源类型
const dataSourceTypes = ref([
  { label: 'starrocks', value: 'starrocks' },
  { label: 'mysql', value: 'mysql' },
  { label: 'postgresql', value: 'postgresql' },
  { label: 'oracle', value: 'oracle' },
  // { label: 'http', value: 'http' },
]);
// function handleDebug() {
//   console.log('调试SQL 跳转到SQL工作台');
// }
async function save() {
  const result = await formRef.value.validate();
  if (result !== true) {
    return;
  }
  const data = getData();
  return {
    nodeId: nodeId.value,
    data,
  };
}
// function reset() {
//   formRef.value.reset();
// }
function setData(nodeid, data) {
  nodeId.value = nodeid;
  Object.keys(data).forEach((key) => {
    formData[key] = data[key];
  });
}

const dataCatalogStore  = useDataCatalogStore();
const datasourceOptions = ref([]);
function initDatasourceOptions() {
  dataCatalogStore.fetchBusiDatasourceListByAuth({ spaceId: route.query.spaceId }, { loading: false }).then((res) => {
    datasourceOptions.value = res.data.map(item => ({
      label: item.name,
      value: item.id,
      type: item.type,
    }));
  });
}
const filterDatasourceOptions = computed(() => datasourceOptions.value.filter((item) => {
  const { datasourceType } = formData.taskProperties;
  if (datasourceType) {
    return item.type === datasourceType;
  }
  return true;
}));

function datasourceTypeChange() {
  Object.assign(formData.taskProperties, { datasourceId: '' });
}

watch(() => route.query.spaceId, () => {
  initDatasourceOptions();
});

onMounted(() => {
  initDatasourceOptions();
});

defineExpose({
  setData,
  // reset,
  save,
});
</script>

<template>
  <div class="des-workflow-job-form__starrocks-materialized-view">
    <t-form :data="formData" ref="formRef" label-width="122px" :rules="jdbcSqlRules" :disabled="readonly">
      <basic-config :formData="formData"></basic-config>
      <resource-config :formData="formData"></resource-config>
      <alarm-config :formData="formData"></alarm-config>
      <section>
        <block-header title="更多设置" style="margin-bottom: 12px;" />
        <t-form-item label="业务数据源类型" name="taskProperties.datasourceType">
          <t-select v-model="formData.taskProperties.datasourceType" placeholder="请选择" style="width: 374px;" :options="dataSourceTypes" @change="datasourceTypeChange"></t-select>
        </t-form-item>
        <t-form-item label="业务数据源实例" name="taskProperties.datasourceId">
          <t-select v-model="formData.taskProperties.datasourceId" placeholder="请选择" style="width: 374px;" :options="filterDatasourceOptions"></t-select>
        </t-form-item>
        <t-form-item label="脚本来源" name="taskProperties.scriptType">
          <t-select v-model="formData.taskProperties.scriptType" placeholder="请选择" style="width: 374px;" :options="scriptTypeOptions"></t-select>
        </t-form-item>
        <div style="padding-left:117px;" v-if="formData.taskProperties.scriptType === 'CONTENT'">
          <basic-sql-editor v-model="formData.taskProperties.scriptContent" title="SQL语句" style="height:280px;" :readOnly="readonly">
            <!-- <t-link hover="color" theme="primary" @click="handleDebug"><des-icon name="des-icon-daimacod2" size="16px" style="margin-right: 2px;"></des-icon>SQL调试</t-link> -->
          </basic-sql-editor>
        </div>
        <template v-if="formData.taskProperties.scriptType === 'GIT_FILE'">
          <git-file :formData="formData"></git-file>
        </template>
      </section>
      <section>
        <block-header title="任务关联" style="margin-bottom: 12px;" />
        <!-- 前置任务 -->
        <pre-task :currentId="formData._id" :currentFlowName="currentFlowName"></pre-task>
      </section>
    </t-form>
  </div>
</template>

<style lang="less" scoped>
// .file-content {
//   max-height: 200px;
//   overflow: auto;
// }
</style>
