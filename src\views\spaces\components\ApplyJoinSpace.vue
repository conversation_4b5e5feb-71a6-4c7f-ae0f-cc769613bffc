<script setup>
import { ref, reactive, defineExpose } from 'vue';
import BasicDialog from '@/components/BasicDialog.vue';

const visible = ref(false);
const formRef = ref(null);

const formData = reactive({
  name: '',
  role: '',
  reason: '',
});

const rules = {
  role: [{ required: true, message: '请选择成员角色' }],
  reason: [{ required: true, message: '请填写申请原因' }],
};

function reset() {
  formData.name = '';
  formData.role = '';
  formData.reason = '';
}

function handleClose() {
  close();
}
function handleConfirm() {
  formRef.value.validate().then((result) => {
    if (result === true) {
      console.log('表单校验通过');
    }
  });
}

function open(data) {
  formData.name = data.name;
  visible.value = true;
}
function close() {
  visible.value = false;
  reset();
  formRef.value.reset();
}

defineExpose({ open, close });
</script>

<template>
  <BasicDialog
    width="560"
    :visible.sync="visible"
    header="申请加入空间"
    @handleClose="handleClose"
    @handleConfirm="handleConfirm"
  >
    <t-form class="des-space__apply-join-space" :data="formData" style="padding-right:12px;" ref="formRef" labelWidth="78px" :rules="rules">
      <t-form-item label="空间名称" name="name">
        <span>{{ formData.name }}</span>
      </t-form-item>
      <t-form-item label="成员角色" name="role" class="role-form-item">
        <t-radio-group class="vertical-radio-group" v-model="formData.role">
          <div class="radio-item">
            <t-radio value="1">空间负责人</t-radio>
            <p>拥有整个空间的管理权限</p>
          </div>
          <div class="radio-item">
            <t-radio value="2">数据开发人员</t-radio>
            <p>一句该角色对应的权限范围说明，方便用户选择</p>
          </div>
        </t-radio-group>
      </t-form-item>
      <t-form-item label="申请原因" name="reason">
        <t-textarea v-model="formData.reason" :autosize="{ minRows: 3, maxRows: 5 }" :maxlength="200" placeholder="请输入"></t-textarea>
      </t-form-item>
    </t-form>
  </BasicDialog>
</template>

<style lang="less" scoped>
.des-space__apply-join-space {
  :deep(.t-form__label--right) {
    padding-right: 12px;
  }
  .vertical-radio-group {
    display: block;
    .radio-item {
      display: flex;
      align-items: center;
      line-height: 22px;
      height: 22px;
      &:not(:last-child) {
        margin-bottom: 12px;
      }
      p {
        color: #999999;
      }
      :deep(.t-radio) {
        margin-right: 8px;
      }
    }
  }
  .role-form-item {
    :deep(.t-form__label) {
      line-height: 22px;
    }
  }
}
</style>
