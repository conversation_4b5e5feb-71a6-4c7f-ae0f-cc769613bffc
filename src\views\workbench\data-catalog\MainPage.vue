<template>
  <t-layout style="position: relative">
    <t-aside width="240px" class="aside" v-show="show">
      <!-- 数据目录操作区 -->
      <WorkbenchTitle title="数据目录">
        <template #operate>
          <DropdownMenu @change="dropdownChange"></DropdownMenu>
        </template>
        <SelectWithIcon clearable v-model="value" :options="options" @change="selectChange" placeholder="全部数据源" style="width: 100%;"></SelectWithIcon>
      </WorkbenchTitle>

      <!-- 数据目录操作树 -->
      <SplitZone>
        <!-- 操作树 -->
        <WorkbenchTree style="height: 100%" :data="items2" @click="clickHandler"></WorkbenchTree>
        <!-- 信息区 -->
        <template #bottom>
          <WorkbenchInfo :data="{}"></WorkbenchInfo>
        </template>
      </SplitZone>
    </t-aside>
    <!-- 隐藏工具按钮 -->
    <!-- <CollapseTool :visible.sync="show"></CollapseTool> -->
     <!-- 未开发，暂时禁用 -->
    <CollapseTool :visible="show"></CollapseTool>
    <t-layout class="layout-right">
      <t-content>
        <main-content></main-content>
      </t-content>
    </t-layout>
  </t-layout>
</template>

<script setup lang="jsx">
import { onMounted, ref } from 'vue';
import { SplitZone, CollapseTool, DropdownMenu, WorkbenchTitle, WorkbenchTree, WorkbenchInfo } from '../components';
import MainContent from './MainContent.vue';
import SelectWithIcon from '@/components/SelectWithIcon.vue';

const value = ref('');
const options = ref([
  {
    label: 'StarRock1',
    value: '1',
  },
  {
    label: 'StarRock2',
    value: '2',
  },
]);

const show = ref(false);

const selectChange = (val) => {
  console.log('selectChange', val);
};

const dropdownChange = (val) => {
  console.log('dropdown', val);
};

const items2 = ref([
  {
    label: 'retail_e_com',
    type: 'shujuku',
    hasRight: true,
    children: [
      {
        label: '表',
        type: 'biao',
      },
      {
        label: '视图',
        type: 'shitu',
      },
    ],
  },
]);

const clickHandler = ({ context }) => {
  console.log(context);
};

onMounted(() => {});
</script>

<style lang="less" scoped>
.t-layout {
  height: 100%;
  .aside {
    flex: 0 0 240px;
  }
  .layout-right {
    padding: 12px 16px;
    width: 100%;
    overflow: hidden;
    :deep(.t-layout__content) {
      width: 100%;
      overflow: auto;
      border-radius: 4px;
    }
  }
}
</style>
