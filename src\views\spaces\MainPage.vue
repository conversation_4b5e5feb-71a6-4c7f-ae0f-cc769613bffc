<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { storeToRefs } from 'pinia';
import { AddIcon, SearchIcon } from 'tdesign-icons-vue';
import SpaceCard from './components/SpaceCard.vue';
import NoSpaces from './components/NoSpaces.vue';
import CreateSpace from './components/CreateSpace.vue';
// import ApplyJoinSpace from './components/ApplyJoinSpace.vue';
import { useSpacesStore } from '@/stores/spaces';
import { useCommonStore } from '@/stores/common';
import { useSearch } from '@/hooks';
import BaseAuth from '@/components/BaseAuth.vue';

const spaceStore = useSpacesStore();
const commonStore = useCommonStore();
const { operateCodeList, staffId } = storeToRefs(commonStore);

const actived = ref('my');

const menus = computed(() => [
  { label: '我的空间', value: 'my', auth: 'Menu_mySpace' },
  { label: '全部空间', value: 'all', auth: 'Menu_allSpace' },
].filter(item => operateCodeList.value.includes(item.auth)));

spaceStore.getAllOrgAreas();
const { allOrgAreas } = storeToRefs(spaceStore);

function handleActived(val) {
  if (actived.value !== val) {
    actived.value = val;
    pagination.page = 1;
    fetchData();
  }
}
const searchForm = reactive({
  areaId: '',
  queryKey: '',
});
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0,
});
const list = ref([]);
const noAnyData = ref(true);
const { onSearch } = useSearch(fetchData);

function handleSearch() {
  pagination.page = 1;
  onSearch();
}
async function fetchData() {
  let res;
  const reqData = {
    queryData: {
      areaId: searchForm.areaId,
      queryKey: searchForm.queryKey,
    },
    pageNum: pagination.page,
    pageSize: pagination.pageSize,
  };
  if (actived.value === 'my') {
    res = await spaceStore.getMySpaces(reqData);
  } else {
    res = await spaceStore.getSpacesList(reqData);
  }
  const { data } = res;
  list.value = data?.list || [];
  pagination.total = data?.total || 0;
  noAnyData.value = list.value.length === 0 && !searchForm.queryKey && !searchForm.areaId;
}

const createSpaceRef = ref(null);

function handleNewSpace() {
  createSpaceRef.value.open();
}

// function handleJoin() {
//   actived.value = 'all';
// }
function onChange(pageInfo) {
  pagination.page = pageInfo.current;
  if (pagination.pageSize !== pageInfo.pageSize) {
    pagination.page = 1;
  }
  pagination.pageSize = pageInfo.pageSize;
  fetchData();
}
// const applyJoinSpaceRef = ref(null);
// function handleApply(item) {
//   console.log('申请加入', item);
//   applyJoinSpaceRef.value.open(item);
// }
function getShowMore(item) {
  // 系统管理员显示[...]
  if (operateCodeList.value.includes('Menu_spaceCreate') && operateCodeList.value.includes('Menu_spaceEdit') && operateCodeList.value.includes('Menu_spaceDel')) {
    return true;
  }
  // 空间负责人显示[...]
  return item.manageRoleList?.some(item => item.staffId === staffId.value);
}

onMounted(() => {
  fetchData();
});
</script>

<template>
  <div class="page-spaces">
    <section class="page-spaces__header">
      <div class="page-spaces__tab">
        <span v-for="item in menus" :class="{actived: actived === item.value}" @click="handleActived(item.value)" :key="item.value">{{ item.label }}</span>
      </div>
      <t-button v-auth="'Menu_spaceCreate'" @click="handleNewSpace"><add-icon slot="icon" />新建空间</t-button>
    </section>
    <template v-if="!noAnyData">
      <section class="page-spaces__content">
        <div class="page-spaces__search">
          <t-space :size="14">
            <t-select v-model="searchForm.areaId" style="width: 134px;" @change="handleSearch">
              <t-option label="全部领域" value="" />
              <t-option v-for="item in allOrgAreas" :key="item.uniqueId" :label="item.orgArea" :value="item.uniqueId"></t-option>
            </t-select>
            <t-input v-model="searchForm.queryKey" placeholder="请搜索空间名称/描述/空间标识" style="width: 290px;" @change="handleSearch">
              <template #suffixIcon>
                <search-icon :style="{ cursor: 'pointer' }" />
              </template>
            </t-input>
          </t-space>
        </div>
        <template v-if="list.length">
          <div class="page-spaces__cards--scroll">
            <div class="page-sapces__cards">
              <space-card
                v-for="item in list"
                @refresh="fetchData"
                :key="item.id"
                :data="item"
                class="page-spaces__card-item"
                :showMore="getShowMore(item)">
              </space-card>
            </div>
          </div>
        </template>
        <template v-else>
          <no-spaces text="暂无数据~"></no-spaces>
        </template>
      </section>
      <section class="pages-spaces__footer">
        <t-pagination
          v-model="pagination.page"
          :total="pagination.total"
          :page-size="pagination.pageSize"
          @change="onChange"
        >
        <template #totalContent>
          <div class="t-pagination__total">共 <b class="des-common-primary-text">{{ pagination.total }}</b> 条记录</div>
        </template>
      </t-pagination>
      </section>
    </template>
    <template v-if="noAnyData">
      <section class="page-spaces-empty">
        <no-spaces :text="operateCodeList.includes('Menu_spaceCreate') ? '您还未创建或加入任何空间 ~' : '您还未加入任何空间 ~'">
          <template #btn-group>
            <base-auth code="Menu_spaceCreate">
              <t-space :size="8">
                <t-button theme="primary" @click="handleNewSpace"><add-icon slot="icon" />新建空间</t-button>
                <!-- <t-button variant="outline" @click="handleJoin">加入空间</t-button> -->
              </t-space>
            </base-auth>
          </template>
        </no-spaces>
      </section>
    </template>
    <create-space ref="createSpaceRef" @success="fetchData"></create-space>
    <!-- <apply-join-space ref="applyJoinSpaceRef" @success="fetchData"></apply-join-space> -->
  </div>
</template>

<style lang="less" scoped>
.page-spaces {
  width: 1200px;
  height: calc(~'100% - 24px');
  margin: 12px auto;
  background-color: #fff;
  border-radius: 4px;
}
.page-spaces__header {
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #EEEEEE;
  align-items: center;
  .page-spaces__tab {
    display: flex;
    height: 46px;
    & > span {
      position: relative;
      margin-right: 24px;
      text-align: center;
      font-family: "PingFang SC";
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 46px;
      color: #666666;
      cursor: pointer;
      &.actived {
        color: #3464e0;
        font-weight: 600;
        &::after {
          position: absolute;
          left: 0;
          right: 0;
          bottom: 0;
          height: 2px;
          background-color: #3464E0;
          content: ' ';
        }
      }
      &[disabled] {
        cursor: not-allowed;
      }
    }
  }
}
.page-spaces__content {
  height: calc(~'100% - 47px - 52px');
  .page-spaces__search {
    display: flex;
    padding: 16px 20px;
  }
  .page-spaces__cards--scroll {
    padding: 0 20px;
    overflow: auto;
    height: calc(~'100% - 64px');
  }
  .page-sapces__cards {
    display: grid;
    gap: 20px;
    grid-template-columns: repeat(4, 1fr);
  }
  .page-spaces__card-item {
    width: 275px;
  }
}
.pages-spaces__footer {
  display: flex;
  align-items: center;;
  height: 52px;
  padding: 0 20px;
}
.page-spaces-empty {
  margin-top: 80px;
}

</style>
