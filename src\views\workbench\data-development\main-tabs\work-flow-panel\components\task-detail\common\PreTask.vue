<script setup>
import emitter, { EVENT_NAMES } from '@/utils/emitter';
import { usePreTaskHooks } from '../../../hooks/pretask';
import { defineProps, watch, onMounted, onBeforeUnmount } from 'vue';

const { preTaskIds, nodeList, linkPreTask, syncPreTaskId } = usePreTaskHooks();

const props = defineProps({
  currentId: {
    type: String,
    default: '',
  },
  currentFlowName: {
    type: String,
    default: '',
  },
});

function syncPreTasks() {
  syncPreTaskId(props.currentId);
}
watch(() => props.currentId, (value) => {
  if (value) {
    syncPreTasks();
  };
});

function handleLinkPreTask({ currentFlowName }) {
  // 限制当前看板重新连线
  if (currentFlowName === props.currentFlowName) {
    linkPreTask(props.currentId);
  }
}

onMounted(() => {
  // 画布前置任务连线
  emitter.on(EVENT_NAMES.WORK_FLOW_PANEL.LINK_PRE_TASK, handleLinkPreTask);
});

onBeforeUnmount(() => {
  emitter.off(EVENT_NAMES.WORK_FLOW_PANEL.LINK_PRE_TASK, handleLinkPreTask);
});
</script>

<template>
  <t-form-item label="前置任务">
    <t-select v-model="preTaskIds" multiple placeholder="请选择" style="width: 374px;" clearable :minCollapsedNum="1">
      <t-option v-for="item in nodeList" :key="item.value" :value="item.value" :label="item.label" :disabled="item.value === currentId"></t-option>
    </t-select>
  </t-form-item>
</template>
