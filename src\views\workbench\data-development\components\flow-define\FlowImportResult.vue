<script setup>
import { ref, defineExpose, computed } from 'vue';
import BasicDialog from '@/components/BasicDialog.vue';
import BlockHeader from '@/components/BlockHeader.vue';

const visible = ref(false);
const importResult = ref([]);

// 新增工作流数据
const newWorkflowData = computed(() => importResult.value.filter(item => item.type === 'create'));

// 变更工作流数据
const changedWorkflowData = computed(() => importResult.value.filter(item => item.type === 'update'));

// 无需变更工作流数据
const noChangeWorkflowData = computed(() => importResult.value.filter(item => item.type === 'noNeedUpdate'));

function getStatusCell(status) {
  const stateMap = {
    success: '成功',
    failed: '失败',
  };
  const text = stateMap[status];
  const className = status === 'success' ? 'des-green-dot' : 'des-red-dot';
  if (text) {
    return <div class={className}>{text}</div>;
  }
  return '';
}

function publishStateCell(state) {
  const stateMap = {
    UNPUBLISHED: '待发布',
    PUBLISHED: '已发布',
    DRAFT: '草稿中',
  };
  const text = stateMap[state];
  const className = state === 'PUBLISHED' ? 'des-green-dot' : 'des-blue-dot';
  if (text) {
    return <div class={className}>{text}</div>;
  }
  return <div>-</div>;
}

// 工作流表格列配置
const workflowColumns = [
  {
    colKey: 'serial-number',
    title: '序号',
    width: 60,
    align: 'center',
  },
  {
    colKey: 'processName',
    title: '工作流名称',
    width: 209,
  },
  {
    colKey: 'publishState',
    title: '发布状态',
    width: 110,
    cell: (h, { row }) => publishStateCell(row.publishState),
  },
  {
    colKey: 'version',
    title: '当前版本',
    width: 120,
    cell: (h, { row }) => (row.version ? `V${row.version}` : ''),
  },
  {
    colKey: 'status',
    title: '导入状态',
    width: 129,
    cell: (h, { row }) => getStatusCell(row.status),
  },
  {
    colKey: 'errMsg',
    title: '错误信息',
    width: 119,
    ellipsis: true,
  },
];

// 无需变更工作流表格列配置
const noChangeColumns = [
  {
    colKey: 'serial-number',
    title: '序号',
    width: 60,
    align: 'center',
  },
  {
    colKey: 'processName',
    title: '工作流名称',
    width: 208,
  },
  {
    colKey: 'publishState',
    title: '发布状态',
    width: 194,
    cell: (h, { row }) => publishStateCell(row.publishState),
  },
  {
    colKey: 'version',
    title: '当前版本',
    width: 166,
    cell: (h, { row }) => `V${row.version}`,
  },
];

function handleClose() {
  close();
}

function open(data) {
  visible.value = true;
  importResult.value = data;
}
function close() {
  visible.value = false;
}

defineExpose({ open, close });
</script>

<template>
  <BasicDialog
    width="798"
    :visible.sync="visible"
    header="导入结果"
    :footer="false"
    :showFooter="false"
    @handleClose="handleClose"
  >
    <t-space :size="5" class="success-tip">
      <des-icon name="des-icon-fill-wanchengdagouzhengque" style="color: #0ad0b6"></des-icon>
      <span>操作完成，导入结果如下：</span>
    </t-space>

    <section>
      <BlockHeader title="新增工作流" style="margin-bottom: 16px" />
      <t-table rowKey="id" :data="newWorkflowData" :columns="workflowColumns" />
    </section>

    <section>
      <BlockHeader title="变更工作流" style="margin-bottom: 16px" />
      <t-table rowKey="id" :data="changedWorkflowData" :columns="workflowColumns" />
    </section>

    <section>
      <BlockHeader title="无需变更工作流" style="margin-bottom: 16px" />
      <div class="no-change-tip">
        以下工作流因导入版本与当前版本配置字段完全一致，不会进行版本更新
      </div>
      <t-table rowKey="id" :data="noChangeWorkflowData" :columns="noChangeColumns" />
    </section>
  </BasicDialog>
</template>

<style lang="less" scoped>
.success-tip {
  color: #000000;
  margin-bottom: 16px;
}
section {
  margin-bottom: 16px;
  &:last-child {
    margin-bottom: 0;
  }
  .no-change-tip {
    color: #999999;
    font-size: 12px;
    margin-bottom: 16px;
  }
}
</style>
