<template>
  <t-layout>
    <t-aside class="aside">
      <AsideMenu></AsideMenu>
    </t-aside>
    <t-layout class="layout-right">
      <t-content>
        <router-view></router-view>
      </t-content>
    </t-layout>
  </t-layout>
</template>

<script setup>
import { AsideMenu } from './components';
</script>

<style lang="less" scoped>
.t-layout {
  height: 100%;
  .aside {
    width: 210px;
    flex: 0 0 210px;
  }
  .layout-right {
    padding: 12px 16px;
    width: 100%; /* 确保宽度占满剩余空间 */
    overflow: hidden; /* 防止内容溢出 */
    background-color: #ebf0f7;
    :deep(.t-layout__content) {
      width: 100%;
      overflow: auto; /* 允许滚动以适应内容 */
      border-radius: 4px;
    }
  }
}
</style>
