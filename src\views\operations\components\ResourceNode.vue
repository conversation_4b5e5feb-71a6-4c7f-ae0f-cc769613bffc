<template>
  <div class="node" :class="{ actived: choosed, 'last-node': lastNode }">
    <span>{{ data.label }}</span>
  </div>
</template>

<script setup>
import { computed, defineProps } from 'vue';
const props = defineProps({
  data: {
    type: Object,
  },
  actived: {
    required: true,
  },
  lastNode: {
    required: true,
    default: false,
  },
});

const choosed = computed(() => props.actived === props.data.id);
</script>

<style lang="less" scoped>
.node {
  height: 34px;
  line-height: 34px;
  position: relative;
  padding: 0 20px;
  background: #d6e0f9;
  border-radius: 20px;
  color: var(--des-color-theme);
  font-size: 14px;
  text-align: center;
  cursor: pointer;
  margin-right: 78px;
  &.actived::before {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 6px solid var(--des-color-theme);
  }
  &:not(.last-node):after {
    content: '';
    position: absolute;
    top: 50%;
    left: 100%;
    width: 78px;
    height: 1px;
    background: var(--des-color-theme);
    cursor: none;
  }
}
.last-node {
  margin-right: 0;
}
.actived {
  background: var(--des-color-theme);
  color: #fff;
}
</style>
