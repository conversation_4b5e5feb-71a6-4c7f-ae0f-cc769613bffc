<template>
  <section class="workbench__title">
    <div class="title-wrap">
      <div class="title">{{ title }}</div>
      <slot name="operate"></slot>
    </div>
    <slot></slot>
  </section>
</template>

<script setup>
import { defineProps } from 'vue';

defineProps({
  title: {
    type: String,
    default: '数据目录',
  },
});
</script>

<style lang="less" scoped>
.workbench__title {
  width: 100%;
  height: 96px;
  padding: 20px 12px 16px 12px;
  border-bottom: 1px solid #ebf0f7;
  .title-wrap {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    .title {
      color: #2a2a2a;
      font-family: "PingFang SC";
      font-size: 16px;
      font-style: normal;
      font-weight: var(--des-font-bold);
      line-height: 24px;
    }
  }
}
</style>
