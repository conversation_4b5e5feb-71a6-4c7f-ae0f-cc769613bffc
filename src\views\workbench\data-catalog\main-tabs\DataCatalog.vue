<script setup>
import { ref, defineAsyncComponent, computed, watchEffect } from 'vue';
import { storeToRefs } from 'pinia';
import { useCommonStore } from '@/stores/common';

const DatabaseManagement = defineAsyncComponent(() => import('../components/DatabaseManagement.vue'));
const DataSourceManagement = defineAsyncComponent(() => import('../components/DataSourceManagement.vue'));

const commonStore = useCommonStore();
const { operateCodeList } = storeToRefs(commonStore);

const current = ref('');
const tabPanelList = computed(() => [
  // {
  //   label: () => (<div class="des-flex-align-center"><des-icon name="des-icon-shujuku" size="16" /><span>数据库管理</span></div>),
  //   value: 'DatabaseManagement',
  //   auth: 'Menu_databaseManage',
  // },
  {
    label: () => (<div class="des-flex-align-center"><des-icon name="des-icon-shujuyuan" size="16" /><span>业务数据源管理</span></div>),
    value: 'DataSourceManagement',
    auth: 'Menu_businessDatasource',
  },
  // { label: '视图管理', value: '', auth: 'Menu_viewMamager' },
  // { label: '物化视图管理', value: '', auth: 'Menu_materializedView' },
  // { label: '函数管理', value: '', auth: 'Menu_functionManage' },
  // { label: '业务数据源管理', value: '', auth: 'Menu_businessDatasource' },
  // { label: 'Catlog管理', value: '', auth: 'Menu_catalogManage' },
].filter(item => operateCodeList.value.includes(item.auth)));

watchEffect(() => {
  const tabs = tabPanelList.value.map(item => item.value);
  if (!tabs.includes(current.value)) {
    changeTab(tabs[0]);
  }
});

function changeTab(val) {
  current.value = val;
}
// 组件映射
function getComp(name) {
  let comp = null;
  switch (name) {
    case 'DatabaseManagement':
      comp = DatabaseManagement;
      break;
    case 'DataSourceManagement':
      comp = DataSourceManagement;
      break;
    default:
      break;
  }
  return comp;
}
</script>

<template>
  <div class="page-des-data-catalog">
    <t-tabs
      :value="current"
      @change="changeTab"
      style="width: 100%;height:100%;"
    >
      <t-tab-panel
        v-for="data in tabPanelList"
        :key="data.value"
        :value="data.value"
        :label="data.label"
        :removable="data.removable"
        :destroyOnHide="false"
        lazy
      >
        <component :is="getComp(data.value)" :current="current"/>
      </t-tab-panel>
    </t-tabs>
  </div>
</template>

<style lang="less" scoped>
.page-des-data-catalog {
  padding: 2px 20px 0;
  height: 100%;
  :deep(.t-tabs__content) {
    height: calc(100% - 48px);
    overflow: auto;
  }
  :deep(.t-tab-panel) {
    height: 100%;
  }
}
</style>
