<script setup>
import { ref, defineExpose, defineProps } from 'vue';
import { cloneDeep, omit } from 'lodash';
import BasicDialog from '@/components/BasicDialog.vue';
import AuthorizeTree from './authorize/AuthorizeTree.vue';
import { InfoCircleIcon } from 'tdesign-icons-vue';
import { useForm } from '@/hooks';
import { useSpacesStore } from '@/stores/spaces';
import EllipsisWithTooltip from '@/components/EllipsisWithTooltip.vue';
import { MessagePlugin } from 'tdesign-vue';

const props = defineProps({
  spaceId: {
    type: String,
    default: '',
  },
  memberGroupOptions: {
    type: Array,
    default: () => [],
  },
  userOptions: {
    type: Array,
    default: () => [],
  },
});

const spaceStore = useSpacesStore();

const staffRef = ref(null);

const visible = ref(false);

const authDataDevType = ref('flow');
const initFormData = {
  id: '',
  authType: 'dataDevelopment',
  authAction: 'directAuthorise',
  transferTarget: [],
  memberList: [],
  userGroup: [],
  spaceUserGroupList: [],
  user: {
    staffId: '',
    engName: '',
    staffName: '',
  },
};
const initFormRules = {
  'user.staffId': [{ required: true, message: '请选择成员' }],
  authAction: [{ required: true }],
  authType: [{ required: true }],
  transferTarget: [{ required: true, message: '请选择转移对象' }],
  memberList: [{ required: true, message: '请选择转移用户' }],
  userGroup: [{ required: true, message: '请选择转移用户组' }],
};
const { form: formData, rules, formRef, validateForm, resetData } = useForm(initFormData, initFormRules);

// 数据目录数据
const data = ref([
  { key: '1', type: 'source', label: 'SR数据源1', permissions: [], children: [{ key: '1-1', type: 'database', label: 'retail_e_com', permissions: [], children: [{ key: '1-1-1', type: 'table', label: '表', permissions: [], children: [{ key: '1-1-1-1', type: 'fleid', label: 'retail_e_commer1', permissions: [] }] }] }] },
  { key: '2', type: 'source', label: 'SR数据源2', permissions: [], children: [{ key: '2-1', type: 'database', label: 'retail_e', permissions: [] }] },
]);
function getIcon(type) {
  let icon;
  switch (type) {
    // 数据源
    case 'source':
      icon = 'des-micon-shujuyuan';
      break;
    // 数据库
    case 'database':
      icon = 'des-micon-shujuku';
      break;
    // 表
    case 'table':
      icon = 'des-micon-biao';
      break;
    // 视图
    case 'view':
      icon = 'des-micon-shitu';
      break;
    // 函数
    case 'function':
      icon = 'des-micon-hanshu';
      break;
    case 'fleid':
      icon = 'des-icon-biao';
      break;
    default:
      icon = '';
  }
  return icon;
}
function getAuthOptions(type) {
  let options = [
    { label: '编辑', value: 'edit' },
    { label: '使用', value: 'use' },
  ];
  if (type === 'database') {
    options = [
      { label: '编辑', value: 'edit' },
      { label: '创建表/视图', value: 'createTable' },
      { label: '创建物化视图', value: 'createView' },
      { label: '创建函数', value: 'createFunction' },
    ];
  }
  if (type === 'table') {
    options = [
      { label: '编辑', value: 'edit' },
      { label: '可写', value: 'write' },
      { label: '可读', value: 'read' },
    ];
  }
  return options;
}
// 工作流数据
const flowData = ref([]);
// Git项目数据
const gitData = ref([]);

const selectedStaffList = ref([]);
function handleChangeStaff(val) {
  selectedStaffList.value = val;
}

function groupPermissionDisabled(data, permission) {
  return data?.groupPermissionEnums.includes(permission) || formData.authAction === 'transferPermission';
}

async function getFlowAuthDetail() {
  const { data } = await spaceStore.getSpacesFlowAuthDetail(props.spaceId, formData.user.staffId, { granteeTypeEnum: 'USER' });
  flowData.value = data?.map((item) => {
    const union = new Set([
      ...(item.selectPermissionEnums || []),
      ...(item.groupPermissionEnums || []),
    ]);
    return {
      ...item,
      label: item.processDefinitionName, // 用于树的搜索
      selectPermissionEnums: [...union],
      rawSelectPermissionEnums: cloneDeep([...union]),
      groupPermissionEnums: item.groupPermissionEnums || [],
    };
  }) || [];
}
async function getGitAuthDetail() {
  const { data } = await spaceStore.getSpacesGitAuthDetail(props.spaceId, formData.user.staffId, { granteeTypeEnum: 'USER' });
  gitData.value = data?.map((item) => {
    const union = new Set([
      ...(item.selectPermissionEnums || []),
      ...(item.groupPermissionEnums || []),
    ]);
    return {
      ...item,
      label: item.gitProjectName, // 用于树的搜索
      selectPermissionEnums: [...union],
      rawSelectPermissionEnums: cloneDeep([...union]),
      groupPermissionEnums: item.groupPermissionEnums || [],
    };
  }) || [];
}

function handleAuthActionChange() {
  // eslint-disable-next-line no-param-reassign
  flowData.value.forEach(item => item.selectPermissionEnums = cloneDeep(item.rawSelectPermissionEnums));
  // eslint-disable-next-line no-param-reassign
  gitData.value.forEach(item => item.selectPermissionEnums = cloneDeep(item.rawSelectPermissionEnums));
}

function flowCheckAll() {
  // eslint-disable-next-line no-param-reassign
  flowData.value.forEach(item => item.selectPermissionEnums = ['EDIT', 'RUN', 'READ', 'GRANT']);
}
function flowCheckReset() {
  // eslint-disable-next-line no-param-reassign
  flowData.value.forEach(item => item.selectPermissionEnums = item.groupPermissionEnums);
}
function gitCheckAll() {
  // eslint-disable-next-line no-param-reassign
  gitData.value.forEach(item => item.selectPermissionEnums = ['EDIT', 'USE', 'GRANT']);
}
function gitCheckReset() {
  // eslint-disable-next-line no-param-reassign
  gitData.value.forEach(item => item.selectPermissionEnums = item.groupPermissionEnums);
}

function handleClose() {
  close();
}
function getFlowSubData(basicParams) {
  return {
    ...basicParams,
    processPermissionList: flowData.value.map(item => ({
      ...omit(item, ['label', 'rawSelectPermissionEnums']),
      permissionList: item.selectPermissionEnums,
    })),
  };
}
function getGitSubData(basicParams) {
  return {
    ...basicParams,
    gitProjectPermissionList: gitData.value.map(item => ({
      ...omit(item, ['label', 'rawSelectPermissionEnums']),
      permissionList: item.selectPermissionEnums,
    })),
  };
}
function getTransferSubData(basicParams) {
  const subData = {
    ...basicParams,
    processPermissionList: flowData.value.map(item => omit(item, ['label', 'rawSelectPermissionEnums'])),
    gitPermissionTypes: gitData.value.map(item => omit(item, ['label', 'rawSelectPermissionEnums'])),
  };
  if (formData.transferTarget.includes('user')) {
    subData.memberList = selectedStaffList.value.map(item => ({
      staffId: item.StaffID,
      engName: item.EngName,
      staffName: item.StaffName,
    }));
  }
  if (formData.transferTarget.includes('userGroup')) {
    subData.spaceUserGroupList = formData.userGroup.map((item) => {
      const target = props.memberGroupOptions.find(option => option.value === item);
      return omit(target, ['label', 'value']);
    });
  }
  return subData;
}
async function handleConfirm() {
  const success = await validateForm();
  if (!success) return;
  // 数据目录的授权逻辑
  if (formData.authType === 'dataDirectory') {
    return;
  }
  // 数据开发-准备所有需要并行执行的异步操作
  const asyncOperations = [];
  const basicParams = {
    spaceId: props.spaceId,
    user: formData.user,
    granteeType: 'USER',
  };
  // 工作流授权
  if (flowData.value.length) {
    const subData = getFlowSubData(basicParams);
    asyncOperations.push(spaceStore.setSpacesFlowAuth(subData));
  }
  // Git授权
  if (gitData.value.length) {
    const subData = getGitSubData(basicParams);
    asyncOperations.push(spaceStore.setSpacesGitAuth(subData));
  }
  // 权限迁移
  if (formData.authAction === 'transferPermission') {
    const subData = getTransferSubData(basicParams);
    asyncOperations.push(spaceStore.privilegeMigration(subData));
  }
  // 并行执行所有异步操作
  await Promise.all(asyncOperations);
  MessagePlugin.success('操作成功');
  handleClose();
}

function handleChangeUser(value) {
  const target = props.userOptions.find(item => item.value === value);
  formData.user = { ...omit(target, ['label', 'value']) };
  formData.spaceUserGroupList = target.spaceUserGroupList;
  getFlowAuthDetail();
  getGitAuthDetail();
}

function open(data) {
  visible.value = true;
  formData.id = data.id;
  formData.user = cloneDeep(data.user);
  formData.spaceUserGroupList = data.spaceUserGroupList;
  getFlowAuthDetail();
  getGitAuthDetail();
}
function close() {
  visible.value = false;
  selectedStaffList.value = [];
  flowData.value = [];
  gitData.value = [];
  authDataDevType.value = 'flow';
  resetData();
}

function handleChangeFlowPermission(data, value, context) {
  const { current, type } = context;
  const array = value.filter(item => item === 'GRANT');
  if (current === 'RUN') {
    Object.assign(data, {
      selectPermissionEnums: type === 'check' ? ['RUN', 'EDIT', 'READ', ...array] : [...array],
    });
  }
  if (current === 'EDIT') {
    Object.assign(data, {
      selectPermissionEnums: type === 'check' ? ['EDIT', 'READ', ...array] : [...array],
    });
  }
}

function hasSameAuth(targetArray, array) {
  return array.some(element => targetArray.includes(element));
}

defineExpose({ open, close });
</script>

<template>
  <BasicDialog
    top="6vh"
    width="900"
    :visible.sync="visible"
    header="授权"
    :onClosed="handleClose"
    @handleClose="handleClose"
    @handleConfirm="handleConfirm"
  >
    <t-form :data="formData" :rules="rules" ref="formRef" label-width="88px">
      <t-form-item label="成员姓名" name="user.staffId">
         <t-select v-model="formData.user.staffId" filterable :options="userOptions" style="width: 200px;" @change="handleChangeUser"></t-select>
      </t-form-item>
      <t-form-item label="所在用户组">
        <span>{{ formData.spaceUserGroupList?.map(item => item.name).join('，') || '-' }}</span>
      </t-form-item>
      <t-form-item label="授权动作" name="authAction">
        <t-radio-group v-model="formData.authAction" @change="handleAuthActionChange">
          <t-radio-button value="directAuthorise">直接授权</t-radio-button>
          <t-radio-button value="transferPermission">权限转移</t-radio-button>
        </t-radio-group>
      </t-form-item>
      <t-form-item label="授权" name="authType">
        <t-radio-group v-model="formData.authType">
          <!-- 后续开发 -->
          <t-radio-button value="dataDirectory" disabled>数据目录</t-radio-button>
          <t-radio-button value="dataDevelopment">数据开发</t-radio-button>
        </t-radio-group>
      </t-form-item>
      <t-form-item>
        <div class="no-label-item">
          <template v-if="formData.authType === 'dataDirectory'">
            <authorize-tree :data="data" name="数据内容">
              <template #label="{ node }">
                <t-space :size="4">
                  <des-icon :name="getIcon(node.data.type)" size="14" />
                  <span>{{node.label}}</span>
                </t-space>
              </template>
              <template #operations="{ node }">
                <t-checkbox-group v-model="node.data.permissions">
                  <t-checkbox v-for="item in getAuthOptions(node.data.type)" :label="item.label" :value="item.value" :key="item.value"></t-checkbox>
                </t-checkbox-group>
              </template>
            </authorize-tree>
          </template>
          <template v-else>
            <t-tabs v-model="authDataDevType">
              <!-- 默认插槽 和 具名插槽（panel）都是用来渲染面板内容 -->
              <t-tab-panel value="flow" label="工作流">
                <authorize-tree :data="flowData" name="工作流" @check="flowCheckAll" @reset="flowCheckReset">
                  <template #tips v-if="formData.authAction === 'directAuthorise'">
                    <t-space :size="2" align="center" style="color: #FFB800; margin-bottom: 12px;">
                      <InfoCircleIcon size="16px"/>
                      <span>属于用户组的权限不可编辑</span>
                    </t-space>
                  </template>
                  <template #label="{ node }">
                    <div class="des-flex-align-center flex-wrap">
                      <des-icon name="des-micon-gongzuoliu" size="14" class="icon"/>
                      <span class="text">
                        <ellipsis-with-tooltip :text="node.data?.processDefinitionName"></ellipsis-with-tooltip>
                      </span>
                    </div>
                  </template>
                  <template #operations="{ node }">
                    <t-checkbox-group v-model="node.data.selectPermissionEnums" @change="(val, context) => handleChangeFlowPermission(node.data, val, context)" v-if="node.data?.selectPermissionEnums">
                      <t-checkbox label="运行" value="RUN" :disabled="groupPermissionDisabled(node.data, 'RUN')"></t-checkbox>
                      <t-checkbox label="编辑" value="EDIT" :disabled="groupPermissionDisabled(node.data, 'EDIT') || hasSameAuth(node.data.selectPermissionEnums, ['RUN'])"></t-checkbox>
                      <t-checkbox label="查看" value="READ" :disabled="groupPermissionDisabled(node.data, 'READ') || hasSameAuth(node.data.selectPermissionEnums, ['RUN', 'EDIT'])"></t-checkbox>
                      <t-checkbox label="授予" value="GRANT" :disabled="groupPermissionDisabled(node.data, 'GRANT')"></t-checkbox>
                    </t-checkbox-group>
                  </template>
                </authorize-tree>
              </t-tab-panel>
              <t-tab-panel value="git" label="Git项目">
                <authorize-tree :data="gitData" name="Git项目" @check="gitCheckAll" @reset="gitCheckReset">
                  <template #tips v-if="formData.authAction === 'directAuthorise'">
                    <t-space :size="2" align="center" style="color: #FFB800; margin-bottom: 12px;">
                      <InfoCircleIcon size="16px"/>
                      <span>属于用户组的权限不可编辑</span>
                    </t-space>
                  </template>
                  <template #label="{ node }">
                    <div class="des-flex-align-center flex-wrap">
                      <des-icon name="des-micon-gitxiangmu" size="14" class="icon"/>
                      <span class="text">
                        <ellipsis-with-tooltip :text="node.data?.gitProjectName"></ellipsis-with-tooltip>
                      </span>
                    </div>
                  </template>
                  <template #operations="{ node }">
                    <t-checkbox-group v-model="node.data.selectPermissionEnums" v-if="node.data?.selectPermissionEnums">
                      <t-checkbox label="编辑" value="EDIT" :disabled="groupPermissionDisabled(node.data, 'EDIT')"></t-checkbox>
                      <t-checkbox label="使用" value="USE" :disabled="groupPermissionDisabled(node.data, 'USE')"></t-checkbox>
                      <t-checkbox label="授予" value="GRANT" :disabled="groupPermissionDisabled(node.data, 'GRANT')"></t-checkbox>
                    </t-checkbox-group>
                  </template>
                </authorize-tree>
              </t-tab-panel>
            </t-tabs>
          </template>
        </div>
      </t-form-item>
      <template v-if="formData.authAction === 'transferPermission'">
        <t-form-item label="转移对象" name="transferTarget">
          <t-checkbox-group v-model="formData.transferTarget">
            <t-checkbox key="user" label="用户" value="user" />
            <t-checkbox key="userGroup" label="用户组" value="userGroup" />
          </t-checkbox-group>
        </t-form-item>
        <t-form-item label="转移用户" name="memberList" v-if="formData.transferTarget.includes('user')">
          <sdc-staff-selector ref="staffRef" v-model="formData.memberList" @change="handleChangeStaff" multiple style="width:498px;" placeholder="请选择人员" modalClass="des-sdc-modal--fix" size="small"></sdc-staff-selector>
        </t-form-item>
        <t-form-item label="转移用户组" name="userGroup" v-if="formData.transferTarget.includes('userGroup')">
          <t-select v-model="formData.userGroup" :options="memberGroupOptions" multiple filterable clearable style="width: 498px;"></t-select>
        </t-form-item>
        <t-form-item>
          <t-space :size="2" align="center" style="color: #FFB800;">
            <InfoCircleIcon size="16px"/>
            <span>权限转移，仅会在转移对象的原有权限基础上做权限扩充，不会覆盖对象原有权限</span>
          </t-space>
        </t-form-item>
      </template>
    </t-form>
  </BasicDialog>
</template>

<style lang="less" scoped>
.no-label-item {
  padding-left: 88px;
  .flex-wrap {
    width: 280px;
    gap: 4px;
    .icon {
      flex: 0 0 14px;
    }
    .text {
      flex: 1;
      overflow: hidden;
    }
  }
}
.t-tab-panel {
  padding-top: 12px;
}
</style>
