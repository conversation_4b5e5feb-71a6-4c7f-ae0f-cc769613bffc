<script setup>
import { computed } from 'vue';
import { useRoute } from 'vue-router/composables';
import { storeToRefs } from 'pinia';
import { useCommonStore } from '@/stores/common';
import { useSpacesStore } from '@/stores/spaces';
import { NoSpaces } from './components';

const route = useRoute();
const commonStore = useCommonStore();
const { operateCodeList } = storeToRefs(commonStore);

const spacesStore = useSpacesStore();
const { mySpaces } = storeToRefs(spacesStore);

const menu = computed(() => [
  // { name: '数据接入', path: '/workbench/data-access', icon: 'des-icon-shujuxieruhezidaoru' },
  { name: '数据目录', path: '/workbench/data-catalog', icon: 'des-icon-shujumulu', auth: 'Menu_dataCatalog' },
  { name: '数据开发', path: '/workbench/data-development', icon: 'des-icon-shujukaifa', auth: 'Menu_dataDev' },
  // { name: '数据发布', path: '/workbench/data-release', icon: 'des-icon-shujufabu' },
].filter((item) => {
  if (item.auth) {
    return operateCodeList.value.includes(item.auth);
  }
  return true;
}));
</script>
<template>
  <t-layout class="des-workbench">
    <template v-if="mySpaces.length">
      <t-aside class="des-workbench__left-menu">
        <ul>
          <li v-for="item in menu" :key="item.name">
            <router-link :to="{path: item.path, query: { spaceId: route.query.spaceId }}" :disabled="item.disabled">
              <des-icon :name="item.icon" :size="24"></des-icon>
              <span>{{ item.name }}</span>
            </router-link>
          </li>
        </ul>
      </t-aside>
      <t-content class="des-workbench__right-content">
        <router-view></router-view>
      </t-content>
    </template>
    <template v-else>
      <NoSpaces></NoSpaces>
    </template>
  </t-layout>
</template>

<style lang="less" scoped>
.des-workbench {
  height: 100%;
}
.des-workbench__left-menu {
  width: 76px;
  padding: 12px 6px;
  border-right: 1px solid #EBF0F7;
  ul {
    display: flex;
    gap: 12px;
    flex-direction: column;
  }
  li a {
    display: flex;
    width: 64px;
    height: 64px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 2px;
    flex-shrink: 0;
    color: #999999;
    span {
      text-align: center;
      font-family: "PingFang SC";
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 18px;
    }
    &:hover,
    &.router-link-active {
      color: #3464e0;
    }
  }
}

.des-workbench__right-content {
  width: 100%;
  overflow: auto;
}
</style>
