import * as monaco from 'monaco-editor';
import { language } from 'monaco-editor/esm/vs/basic-languages/sql/sql.js';

// 注册SQL关键字提示
const registerLanguagesHint = () => {
  monaco.languages.registerCompletionItemProvider('sql', {
    provideCompletionItems: () => {
      const suggestions = [];
      language.keywords.forEach((item) => {
        suggestions.push({
          label: item,
          kind: monaco.languages.CompletionItemKind.Keyword,
          insertText: `${item} `,
          detail: '内置关键字',
        });
      });
      return {
        suggestions,
      };
    },
  });
};

export default registerLanguagesHint();
