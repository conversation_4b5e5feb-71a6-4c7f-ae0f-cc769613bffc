// 公共样式
// 公共的dialog样式
.des-common-dialog {
  .t-dialog {
    padding: 0;
  }
  .t-dialog__header {
    padding: 12px 20px;
    color: #333333;
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
    box-shadow: 0 -1px 0 0 #EEE inset;
  }
  .t-dialog__body {
    padding: 16px 20px;
  }
  .t-dialog__footer {
    padding: 11px 20px;
    box-shadow: 0 1px 0 0 #EEE inset;
  }
}

// 设置弹性布局并垂直居中
.des-flex-align-center {
  display: flex;
  align-items: center;
}

// sdc人员选择器或组织选择器弹窗层级覆盖，避免层级低于tdesign，需要手动给组件设置modalClass
.des-sdc-modal--fix {
  &.t-dialog__ctx.t-dialog__ctx--fixed {
    z-index: 2501;
  }
}

.des-common-dialog-plugin {
  .t-dialog {
    padding: 24px;
  }
  .t-dialog__header {
    color: #333333;
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
    .t-dialog__header-content {
      align-items: center;
    }
    .t-icon {
      font-size: 18px;
    }
  }
  .t-dialog__body {
    padding-top: 8px;
    color: #666666;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
  }
  .t-dialog__footer {
    padding-top: 24px;
  }
  &.des-common-dialog--nobody {
    .t-dialog__body {
      padding: 0;
    }
  }
}
.des-common-warn-text {
  color: #FF7548;
  font-weight: normal;
}
.des-common-primary-text {
  color: var(--des-color-theme);
  font-weight: normal;
}
.des-common-popup-content--no-padding {
  .t-popup__content {
    padding: 0;
  }
}
.des-highlight-matched-text {
  b {
    color: var(--des-color-theme);
    font-weight: 400;
  }
}
.des-ellipsis-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
// 选择器特殊placeholder样式
.des-common-select-placeholder {
  .t-input__inner::placeholder {
    color: #333;
  }
}
// 状态点+颜色
.des-green-dot,
.des-orange-dot,
.des-blue-dot,
.des-red-dot {
  position: relative;
  padding-left: 10px;
  &:before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #50ba5e;
    transform: translateY(-50%);
  }
}

.des-orange-dot {
  &:before {
    background-color: #ff734b;
  }
}
.des-blue-dot {
  &:before {
    background-color: #3464e0;
  }
}
.des-red-dot {
  &:before {
    background-color: #f81d22;
  }
}