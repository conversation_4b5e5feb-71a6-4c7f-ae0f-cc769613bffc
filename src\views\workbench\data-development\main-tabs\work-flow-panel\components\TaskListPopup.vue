<script setup>
import { ref, defineProps, defineEmits } from 'vue';
import { SUBPROCESS, BIDSPLUS, STARROCKS_MATERIALIZED_VIEW, JDBC_SQL, STARROCKS_SQL, HTTP } from '../utils/task-type.js';

defineProps({
  placement: {
    type: String,
    default: 'bottom-left',
  },
  trigger: {
    type: String,
    default: 'click',
  },
});

const emit = defineEmits(['clickNode']);

const taskTypes = ref([
  {
    type: 'FunctionalNodes',
    name: '功能节点',
    color: '#25DC93',
    children: [
      { label: 'Subprocess', value: SUBPROCESS },
      // { label: 'Dependent', value: DEPENDENT },
      // { label: 'Conditions', value: CONDITIONS },
      // { label: 'Switch', value: SWITCH },
      // { label: '消息通知', value: MESSAGE },
    ],
  },
  {
    type: 'DataIntegration',
    name: '数据集成',
    color: '#25CDE0',
    children: [
      { label: '管道任务', value: BIDSPLUS },
    ],
  },
  {
    type: 'DataDevelopment',
    name: '数据开发',
    color: '#8B70F6',
    children: [
      { label: 'StarRocks物化视图', value: STARROCKS_MATERIALIZED_VIEW },
      { label: 'JdbcSQL', value: JDBC_SQL },
      // { label: 'Shell', value: SHELL },
      // { label: 'Python', value: PYTHON },
      { label: 'StarRocksSQL', value: STARROCKS_SQL },
      { label: 'Http', value: HTTP },
    ],
  },
]);

function handleNode(parent, item) {
  emit('clickNode', parent, item);
}
</script>

<template>
  <t-popup :placement="placement" :trigger="trigger" overlayClassName="des-common-popup-content--no-padding">
    <slot></slot>
    <template #content>
      <div class="tools-task-list">
        <div v-for="item in taskTypes" :key="item.type">
          <div>{{ item.name }}</div>
          <ul>
            <li v-for="child in item.children" :key="child.value" @click="handleNode(item, child)">
              <des-icon name="des-icon-linwujiedian" size="16" :style="{marginRight: '4px', color: item.color}"></des-icon>
              <span>{{ child.label }}</span>
            </li>
          </ul>
        </div>
      </div>
    </template>
  </t-popup>
</template>

<style lang="less" scoped>
.tools-task-list {
  padding: 8px 0;
  min-width: 200px;
  & > div {
    & > div {
      padding: 5px 12px;
      color: #333333;
      font-family: "PingFang SC";
      font-size: 12px;
      font-style: normal;
      font-weight: 600;
      line-height: 20px;
    }
    &:not(:last-child) {
      &::after {
        display: block;
        content: ' ';
        height: 1px;
        width: 100%;
        background-color: #EEEEEE;
        margin: 6px 0 5px 0;
      }
    }
  }
  ul {
    padding: 0 12px;
    li {
      display: flex;
      padding: 4px 8px;
      align-items: center;
      gap: 4px;
      align-self: stretch;
      border-radius: 4px;
      background: #F5F7F9;
      height: 28px;
      cursor: pointer;
      &:hover {
        color: var(--des-color-theme);
      }
      &:not(:last-child) {
        margin-bottom: 8px;
      }
    }
  }
}
</style>
