// tdesign的样式重置
.t-table th {
  background: #F5F7F9;
  color: #999999;
}
.t-table__header--fixed:not(.t-table__header--multiple) > tr > th {
  background: #F5F7F9;
  color: #999999;
}
.t-table__header {
  th {
    height: 40px;
    padding: 9px 16px;
  }
}
.t-table__body {
  td {
    color: #333333;
  }
}
.t-pagination__jump {
  background-color: #fff;
}
.t-pagination__number.t-is-current {
  background-color: #F5F6F7;
  color: var(--des-color-theme);
  border-color: #F5F6F7;
}
// 处理表单校验时的样式
.t-form__item {
  &:not(.t-form__item-with-extra) {
    margin-bottom: 16px;
  }
  .t-form__label {
    padding-right: 12px;
  }
}
// 修复最后一个form-item由于定位导致文本脱离文档流产生滚动条
.t-form:not(.t-form-inline) .t-form__item:last-of-type {
  .t-input__extra {
    position: initial;
  }
}

// 填充型单选选控件，默认选中状态的颜色
.t-radio-group.t-radio-group--filled {
  padding: 4px;
  background: #EEEEEE;
  .t-radio-button {
    height: 24px;
    line-height: 24px;
  }
  .t-radio-group__bg-block {
    height: 24px;
    margin-top: 2px;
  }
}
.t-radio-group.t-radio-group--filled .t-radio-button.t-is-checked {
  color: var(--des-color-theme);
  font-weight: 600;
}

.t-menu {
  .t-menu__item {
    height: 38px;
    line-height: 38px;
  }
  .t-is-active {
    font-weight: 600;
  }
}

.t-layout {
  background: #EBF0F7;
}

// 全局选择框选中态字体颜色
.t-select {
  .t-input__inner {
    color: #333;
  }
}
// tabs激活态的线高
.t-tabs__bar.t-is-top {
  height: 2.5px;
}
// 选项卡超出最大宽度时，选项卡按钮的高度
.t-tabs__btn.t-size-m {
  height: 38px;
  line-height: 38px;
}
