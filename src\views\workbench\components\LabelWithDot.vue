<template>
  <t-space align="center" size="4px">
    <div class="status-dot" :style="style"></div>
    <span>{{ label }}</span>
  </t-space>
</template>

<script setup>
import { computed, defineProps } from 'vue';

const props = defineProps({
  label: String,
  color: {
    type: String,
    default: '#999999',
  },
});

const style = computed(() => {
  const obj = {};
  obj.background = props.color;
  return obj;
});
</script>

<style scoped lang="less">
.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
}
</style>
