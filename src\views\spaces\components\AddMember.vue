<script setup>
import { ref, defineExpose, defineProps, defineEmits } from 'vue';
import BasicDialog from '@/components/BasicDialog.vue';
import { useSpacesStore } from '@/stores/spaces';
import { MessagePlugin } from 'tdesign-vue';
import { useForm } from '@/hooks';

const props = defineProps({
  spaceId: {
    type: String,
    default: '',
  },
  memberGroupOptions: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits('update');
const spaceStore = useSpacesStore();
const visible = ref(false);
const staffRef = ref(null);

const initFormData = {
  staffList: [],
  userGroupList: [],
};
const initFormRules = {
  staffList: [{ required: true, message: '请选择成员' }],
};
const { form: formData, rules, formRef, validateForm, resetForm } = useForm(initFormData, initFormRules);

const selectedStaffList = ref([]);
function handleChangeStaff(val) {
  selectedStaffList.value = val;
}

async function handleConfirm() {
  const success = await validateForm();
  if (success) {
    const params = {
      spaceId: props.spaceId,
      memberList: selectedStaffList.value.map(item => ({
        staffId: item.StaffID,
        engName: item.EngName,
        staffName: item.StaffName,
      })),
      spaceUserGroupList: props.memberGroupOptions.filter(item => formData.userGroupList.includes(item.value)),
    };
    await spaceStore.addSpacesMembers(params);
    close();
    emit('update');
    emit('updateOwnerGroupMembers');
    MessagePlugin.success('添加成功');
  };
}

function open() {
  visible.value = true;
}
function close() {
  resetForm();
  staffRef.value.clearSelected();
  selectedStaffList.value = [];
  visible.value = false;
}

defineExpose({ open, close });
</script>

<template>
  <BasicDialog
    width="560"
    :visible.sync="visible"
    header="添加成员"
    :onClosed="close"
    @handleClose="close"
    @handleConfirm="handleConfirm"
  >
    <t-form class="des-space__add-menber" :data="formData" style="padding-right:12px;" ref="formRef" labelWidth="98px" :rules="rules">
      <t-form-item label="成员姓名" name="staffList">
        <sdc-staff-selector ref="staffRef" v-model="formData.staffList" @change="handleChangeStaff" multiple style="width:100%;" placeholder="请选择人员" modalClass="des-sdc-modal--fix" size="small"></sdc-staff-selector>
      </t-form-item>
      <!-- <t-form-item label="成员角色" name="permissionType" class="role-form-item">
        <t-radio-group class="vertical-radio-group" v-model="formData.permissionType">
          <div class="radio-item">
            <t-radio :value="1">空间负责人</t-radio>
            <p>拥有整个空间的管理权限</p>
          </div>
          <div class="radio-item">
            <t-radio :value="2">数据开发人员</t-radio>
            <p>一句该角色对应的权限范围说明，方便用户选择</p>
          </div>
        </t-radio-group>
      </t-form-item> -->
      <t-form-item label="加入用户组">
        <t-select v-model="formData.userGroupList" :options="memberGroupOptions" multiple clearable filterable></t-select>
      </t-form-item>
    </t-form>
  </BasicDialog>
</template>

<style lang="less" scoped>
.des-space__add-menber {
  :deep(.t-form__label--right) {
    padding-right: 12px;
  }
}
</style>
