<script setup>
import { ref, defineExpose, defineProps, computed, reactive, defineEmits } from 'vue';
import BasicDialog from '@/components/BasicDialog.vue';
import UserName from '@/components/UserName.vue';
import { useDataDevelopmentStore } from '@/stores/data-development';
import { showDialogConfirm } from '@/utils/dialog';
import { MessagePlugin } from 'tdesign-vue';
import emitter, { EVENT_NAMES } from '@/utils/emitter';
import { formatTime } from '@/utils/util';

const props = defineProps({
  readonly: {
    type: Boolean,
    default: false,
  },
  spaceId: {
    type: String,
    default: '',
  },
});
const visible = ref(false);
const loading = ref(false);
const formData = reactive({
  processCode: '',
  processId: '',
});
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  onChange: (pageInfo) => {
    pagination.current = pageInfo.current;
    if (pagination.pageSize !== pageInfo.pageSize) {
      pagination.page = 1;
    }
    pagination.pageSize = pageInfo.pageSize;
    fetchData();
  },
});

const emit = defineEmits(['refresh']);
const dataDevelopmentStore = useDataDevelopmentStore();

const colums = computed(() => {
  const columList = [
    { colKey: 'serial-number', title: '序号', width: 61 },
    {
      colKey: 'version',
      title: '版本',
      width: 122,
      cell: (h, { row }) => (
        <div>
          <t-link theme="primary" hover="color" onClick={ () => handleGoFlowByVersion(row) }>V{ row.version }</t-link>
          { (row.id === formData.processId) && <t-tag color="#125FFF" variant="light">当前版本</t-tag> }
        </div>
      ),
    },
    { colKey: 'description', title: '描述', ellipsis: true },
    {
      colKey: 'createUser',
      title: '创建人',
      width: 180,
      cell: (h, { row }) => <UserName fullName={row.createUser}></UserName>,
    },
    { colKey: 'createTime', title: '创建时间', width: 176, cell: (h, { row }) => formatTime(row.createTime) },
    {
      colKey: 'updateUser',
      title: '修改人',
      width: 180,
      cell: (h, { row }) => <UserName fullName={row.updateUser}></UserName>,
    },
    { colKey: 'updateTime', title: '修改时间', width: 176, cell: (h, { row }) => formatTime(row.updateTime) },
    {
      colKey: 'publishUser',
      title: '发布人',
      width: 180,
      cell: (h, { row }) => <UserName fullName={row.publishUser}></UserName>,
    },
    { colKey: 'publishTime', title: '发布时间', width: 176, cell: (h, { row }) => formatTime(row.publishTime) },
  ];
  if (!props.readonly) {
    columList.push({
      colKey: 'operation',
      title: '操作',
      width: 104,
      cell: (h, { row }) => (
        <t-space size={16}>
          <t-link hover="color" theme="primary" disabled={row.id === formData.processId} onClick={() => handleSwitch(row)}>切换</t-link>
          <t-link hover="color" theme="primary" disabled={row.id === formData.processId} onClick={() => handleRemove(row)}>删除</t-link>
        </t-space>
      ),
    });
  }
  return columList;
});
const tableData = ref([]);

async function fetchData() {
  loading.value = true;
  try {
    const { data } = await dataDevelopmentStore.fetchFlowDefineVersionList({
      queryData: {
        queryKey: '',
        spaceId: props.spaceId,
        processCode: formData.processCode,
      },
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    });
    tableData.value = data.list;
    pagination.total = data.total;
  } catch (error) {
  }
  loading.value = false;
}

async function handleSwitch(row) {
  console.log('切换', row);
  const result = await showDialogConfirm({
    title: '提示',
    body: '切换操作将用历史版本内容生成新的版本，历史版本切换仅为任务代码配置信息等回滚，是否确认切换?',
    width: '440px',
  });
  if (result.type === 'cancel') {
    return;
  }
  const { data } = await dataDevelopmentStore.switchFlowDefine({
    currentProcessId: formData.processId,
    historyProcessId: row.id,
  });
  MessagePlugin.success('操作成功');
  formData.processId = data.id;
  fetchData();
  emit('refresh');
  // 组件在WorkFlowPanel使用时, 刷新工作流定义的列表页
  if (onWorkFlowPanel.value) {
    emitter.emit(EVENT_NAMES.DATA_DEVELOPMENT.REFRESH_FLOW_DEFINE);
  }
}

async function handleRemove(row) {
  console.log('删除', row);
  const result = await showDialogConfirm({
    title: `确定版本「${row.version}」吗？`,
    body: () => (<span>删除后<b class="des-common-warn-text">无法恢复</b>，请谨慎操作。</span>),
    width: '440px',
  });
  if (result.type === 'success') {
    await dataDevelopmentStore.fetchFlowDefineVersionDel(row.id);
    MessagePlugin.success('删除成功');
    if (pagination.current > 1 && tableData.value.length === 1) {
      pagination.current -= 1;
    }
    fetchData();
    emit('refresh');
  }
}

function handleGoFlowByVersion(row) {
  console.log('查看版本', row);

  const newTab = {
    label: `查看工作流 ${row.name} (V${row.version})`,
    value: `ViewFlow_${row.id}`,
    component: 'WorkFlowPanel',
    removable: true,
    childPanel: 'FlowDefine', // 子面板
    props: {
      id: row.id,
      name: row.name,
      // code: row.code,
      readonly: true,
      version: row.version,
      isHistory: true,
    },
  };
  dataDevelopmentStore.addTabPanelList(newTab);
  dataDevelopmentStore.setCurrentTabValue(newTab.value);

  handleClose();
}

function handleClose() {
  close();
}

// 组件是否在WorkFlowPanel使用
const onWorkFlowPanel = ref(false);
function open(data) {
  visible.value = true;

  pagination.current = 1;
  pagination.pageSize = 10;
  pagination.total = 0;

  formData.processCode = data.processCode;
  formData.processId = data.processId;
  onWorkFlowPanel.value = data.onWorkFlowPanel;
  fetchData();
}
function close() {
  visible.value = false;
  formData.processCode = '';
  formData.processId = '';
  tableData.value = [];
}

defineExpose({ open, close });
</script>

<template>
  <BasicDialog
    width="1732"
    :visible.sync="visible"
    header="历史版本"
    @handleClose="handleClose"
    :footer="false"
    :showFooter="false"
  >
    <t-table row-key="id" :data="tableData" :columns="colums" :loading="loading" :pagination="pagination" />
  </BasicDialog>
</template>

<style lang="less" scoped>
</style>
