<template>
  <div class="statistics-info__wrap">
    <span class="label">表大小</span><span class="content">retail_e_commer1</span>
    <span class="label">记录数</span><span class="content">retail_e_commer1</span>
    <span class="label">创建时间</span><span class="content">retail_e_commer1</span>
  </div>
</template>

<script setup>
import { defineProps } from 'vue';

defineProps({
  data: Object,
});
</script>

<style lang="less" scoped>
.statistics-info__wrap {
  height: 100%;
  overflow-y: auto;

  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  span {
    margin-bottom: 8px;
  }
  .label {
    display: inline-block;
    width: 52px;
    margin-right: 8px;
    color: #999999;
    text-align: left;
  }
  .content {
    display: inline-block;
    color: #333333;
    width: calc(~"100% - 60px");
  }
}
</style>
