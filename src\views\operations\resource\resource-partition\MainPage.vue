<template>
  <div style="height: 100%;">
    <PageLayout>
      <template #search>
        <BasicSearch v-model="searchKey" @reset="handleReset" @change="searchChange"></BasicSearch>
      </template>

      <template #button>
        <t-button theme="primary" @click="handleAdd"><add-icon slot="icon" />新增资源</t-button>
      </template>

      <template #table>
        <t-table
          rowKey="id"
          :data="tableData"
          :columns="columns"
          :pagination="pagination"
          max-height="68vh"
        ></t-table>
      </template>
    </PageLayout>
    <ResourcePartitionDialog ref="dialogRef" @fetchData="fetchData"></ResourcePartitionDialog>
    <AuthManageDrawer ref="drawerRef"></AuthManageDrawer>
  </div>
</template>

<script setup lang="jsx" name="resource-partition">
import { to, formatTime } from '@/utils/util';
import { useSearch, usePagination } from '@/hooks';
import { ref, onMounted } from 'vue';
import { AddIcon } from 'tdesign-icons-vue';
import { MessagePlugin } from 'tdesign-vue';
import { showDialogConfirm, showDialogAlert } from '@/utils/dialog';
import { useOperationsStore } from '@/stores/operations';
import { PageLayout } from '@/views/operations/components';
import UserName from '@/components/UserName.vue';
import AuthManageDrawer from './AuthManageDrawer.vue';
import BasicSearch from '@/components/BasicSearch.vue';
import ResourcePartitionDialog from './ResourcePartitionDialog.vue';

const { fetchResourceGroupsList, fetchResourceGroupsDel, fetchResourceGroupsCheckDel } = useOperationsStore();

// 表格列
const columns = ref([
  { colKey: 'serial-number', width: 61, title: '序号', fixed: 'left' },
  {
    colKey: 'code',
    title: '资源标识',
    width: 248,
    ellipsis: true,
  },
  {
    colKey: 'cluster.code',
    title: '所属集群',
    width: 159,
    ellipsis: true,
  },
  {
    colKey: 'resourceDetail',
    title: '命名空间',
    width: 159,
  },
  {
    colKey: 'createUser',
    title: '创建人',
    width: 248,
    cell: (h, { row }) => <UserName fullName={row.createUser}></UserName>,
  },
  {
    colKey: 'updateTime',
    title: '更新时间',
    width: 248,
    cell: (h, { row }) => formatTime(row.updateTime),
  },
  {
    title: '操作',
    colKey: 'operate',
    width: 150,
    cell: (h, { row }) => (
      <t-space>
        <t-link theme="primary" hover="color" onClick={() => handleEdit(row)}>
          编辑
        </t-link>
        <t-link theme="primary" hover="color" onClick={() => handleAuth(row)}>
          授权
        </t-link>
        <t-link theme="primary" hover="color" onClick={() => handleDel(row)}>
          删除
        </t-link>
      </t-space>
    ),
  },
]);

// 表格数据
const tableData = ref([]);

const fetchData = async () => {
  const { current: pageNum, pageSize } = pagination;
  const params = { pageNum, pageSize, queryData: { queryKey: searchKey.value } };
  const [err, data] = await to(fetchResourceGroupsList(params));
  if (err) {
    return;
  }
  const { total, list } = data;
  setPaginationTotal(total);
  tableData.value = list;
};

const { pagination, setPaginationCurrent, setPaginationTotal } = usePagination(fetchData);
const { searchKey, setSearchKey, onSearch } = useSearch(fetchData);

const searchChange = () => {
  setPaginationCurrent(1);
  onSearch();
};

const handleReset = () => {
  setSearchKey('');
  setPaginationCurrent(1);
  fetchData();
};

const dialogRef = ref(null);
// 新增操作
const handleAdd = () => {
  dialogRef.value.openAddDialog();
};
// 编辑操作
const handleEdit = (row) => {
  dialogRef.value.openEditDialog(row);
};

const drawerRef = ref(null);
const handleAuth = (row) => {
  drawerRef.value.openDrawer(row);
};

const fetchDel = async (row) => {
  const [err, data] = await to(fetchResourceGroupsDel(row.id));
  if (err) return;
  if (data) {
    fetchData();
    MessagePlugin('success', '操作成功');
  }
};

const handleDel = async (row) => {
  const result = await showDialogConfirm({
    title: `确定删除资源划分「${row.code}」吗？`,
    body: '删除后不可恢复',
    width: '440px',
  });
  if (result.type === 'cancel') return;
  // 预校验
  const [err, data] = await to(fetchResourceGroupsCheckDel(row.id));
  if (err) return;
  if (data) {
    fetchDel(row);
    return;
  }
  showDialogAlert({
    title: `不可删除资源划分「${row.code}」`,
    body: '当前资源划分已被工作流引用，不可删除',
    width: '440px',
  });
};

// 生命周期钩子
onMounted(() => {
  fetchData();
});
</script>

<style lang="less" scoped></style>
