<template>
  <BasicDialog
    width="560"
    :visible.sync="visible"
    :header="header"
    :submitDisabled="submitDisabled"
    :onClosed="onClose"
    @handleClose="onClose"
    @handleConfirm="onConfirm"
  >
    <t-form ref="formRef" :data="form" :rules="rules" label-width="102px">
      <t-form-item label="集群类型" name="type">
        <t-select v-model="form.type" :options="options" placeholder="请选择" />
      </t-form-item>
      <t-form-item label="集群标识" name="code">
        <t-input v-model="form.code" placeholder="请输入" :maxlength="30" show-limit-number :disabled="!isAddType" />
      </t-form-item>
      <t-form-item label="配置" name="specData">
        <t-textarea
          v-model="form.specData"
          placeholder="请输入"
          :autosize="{ minRows: 3, maxRows: 3 }"
        />
      </t-form-item>
    </t-form>
  </BasicDialog>
</template>

<script setup>
import BasicDialog from '@/components/BasicDialog.vue';
import { useForm } from '@/hooks';
import { cloneDeep } from 'lodash';
import { MessagePlugin } from 'tdesign-vue';
import { to } from '@/utils/util';
import { useDialog } from '@/views/operations/hooks';
import { useOperationsStore } from '@/stores/operations';
import { ref, defineExpose, computed, defineEmits, onMounted } from 'vue';

const emit = defineEmits(['fetchData']);
const { fetchClustersCheckCode, fetchClustersPost, fetchClustersPut, fetchClustersType } = useOperationsStore();

const initFormData = {
  type: '',
  code: '',
  specData: '',
};
const initFormRules = {
  type: [
    {
      required: true,
      message: '必选',
      type: 'error',
      trigger: 'change',
    },
  ],
  code: [
    {
      required: true,
      message: '必填',
      type: 'error',
      trigger: 'change',
    },
    {
      pattern: /^[a-zA-Z0-9_-]+$/,
      message: '只允许包含英文字母、数字、下划线和中划线',
    },
  ],
  specData: [
    {
      required: true,
      message: '必填',
      type: 'error',
      trigger: 'change',
    },
    {
      whitespace: true,
      message: '不能为空',
    },
  ],
};

// 表单数据
const { form, rules, formRef, validateForm, resetForm } = useForm(
  initFormData,
  initFormRules,
);

// 提交按钮禁用状态
const submitDisabled = computed(() => !(form.type && form.code && form.specData));

const isAddType = computed(() => mode.value === 'ADD');
// 弹窗标题
const header = computed(() => (isAddType.value ? '新增资源群' : '编辑资源群'));

let backupForm = {};
const editCb = (data) => {
  Object.assign(form, cloneDeep(data));
  backupForm = cloneDeep(form);
};

const closeCb = () => {
  resetForm();
};

const checkCodeRepeat = async () => {
  const params = { code: form.code };
  const [err, repeat] = await to(fetchClustersCheckCode(params));
  if (err) {
    return;
  }
  repeat && MessagePlugin('error', '集群标识重复');
  return repeat;
};

const addCluster = async () => {
  const repeat = await checkCodeRepeat();
  if (repeat) return;
  const params = { ...form };
  const [err] = await to(fetchClustersPost(params));
  if (err) {
    return;
  }
  return true;
};

const updateCluster = async () => {
  if (backupForm.code !== form.code) {
    const repeat = await checkCodeRepeat();
    if (repeat) return;
  }
  const params = { ...form };
  const [err] = await to(fetchClustersPut(params));
  if (err) {
    return;
  }
  return true;
};

const confirmCb = async () => {
  const valid = await validateForm();
  if (!valid) return;
  const func = isAddType.value ? addCluster : updateCluster;
  const result = await func();
  if (!result) return;
  emit('fetchData');
  MessagePlugin('success', '操作成功');
  return true;
};

// 弹窗数据
const { mode, visible, openAddDialog, openEditDialog, onConfirm, onClose } = useDialog({
  editCb,
  confirmCb,
  closeCb,
});

// 集群类型选择器数据
const options = ref([]);

const initOptions = async () => {
  const [err, data] = await to(fetchClustersType(false));
  if (err) {
    return;
  }
  options.value = data.map(item => ({ label: item, value: item }));
};

onMounted(() => {
  initOptions();
});

defineExpose({ openAddDialog, openEditDialog });
</script>

<style lang="less" scoped>

</style>
