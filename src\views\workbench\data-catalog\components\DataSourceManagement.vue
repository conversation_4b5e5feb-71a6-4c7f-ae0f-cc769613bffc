<template>
  <div class="page-catalog__data-source-management">
    <div class="search-wrap">
      <BasicSearch v-model="form.queryKey" placeholder="名称/创建人/标识..." @reset="handleReset" @change="searchChange">
        <template #prefix>
          <TypeRadioGoup v-model="form.type" @change="typeChange"></TypeRadioGoup>
        </template>
      </BasicSearch>
      <t-button @click="handleAddDataSource"><add-icon slot="icon" />新增业务数据源</t-button>
    </div>

    <!-- 数据表格 -->
    <t-table row-key="code" :data="tableData" :columns="columns" :pagination="pagination" max-height="100%"/>

    <AuthDrawer ref="drawerRef" @fetchData="fetchData"></AuthDrawer>
  </div>
</template>

<script setup>
import { storeToRefs } from 'pinia';
import { formatTime, to, uuid } from '@/utils/util';
import { AddIcon } from 'tdesign-icons-vue';
import { MessagePlugin } from 'tdesign-vue';
import { useRoute } from 'vue-router/composables';
import { showDialogConfirm, showDialogAlert } from '@/utils/dialog';
import eventBus, { EVENT_NAMES } from '@/utils/emitter';
import { useDataCatalogStore } from '@/stores/data-catalog';
import { useForm, useSearch, usePagination } from '@/hooks';
import { TypeRadioGoup } from '@/views/workbench/components';
import { computed, onBeforeUnmount, onMounted, ref, defineProps, nextTick, watch } from 'vue';
import UserName from '@/components/UserName.vue';
import OwnedSpace from '@/components/OwnedSpace.vue';
import BasicSearch from '@/components/BasicSearch.vue';
import AuthDrawer from './datasource-management/AuthDrawer.vue';
import { useSpacesStore } from '@/stores/spaces';

const route = useRoute();

const props = defineProps({
  current: String,
});

const spacesStore = useSpacesStore();
const { isSpaceOwner } = storeToRefs(spacesStore);
const dataCatalogStore = useDataCatalogStore();
const { tabPanelList } = storeToRefs(dataCatalogStore);
const { fetchBusiDatasourceList, fetchBusiDatasourceDel } = dataCatalogStore;

// 表格数据
const tableData = ref([]);

const hasAuth = (row, opreate) => {
  if (isSpaceOwner.value) {
    return true;
  }
  return row.operateList?.includes(opreate);
};

const columns = computed(() => [
  { title: '序号', colKey: 'serial-number', width: 60, fixed: 'left', align: 'center' },
  { title: '业务数据源标识', colKey: 'businessDatasourceFlag', width: 130 },
  {
    title: '业务数据源名称',
    colKey: 'name',
    width: 139,
    cell: (h, { row }) => <t-link theme="primary" hover="color" onClick={() => handleView(row)}>{ row.name }</t-link>,
  },
  { title: '数据源类型', colKey: 'type', width: 142 },
  { title: 'IP主机名', colKey: 'ip', width: 126 },
  { title: '数据库名', colKey: 'databaseName', width: 125 },
  { title: '用户名', colKey: 'userName', width: 125 },
  {
    title: '所属空间',
    colKey: 'spaceName',
    width: 125,
    cell: (h, { row }) => (<OwnedSpace name={row.spaceName}></OwnedSpace>),
  },
  {
    title: '创建人',
    colKey: 'createByName',
    width: 169,
    cell: (h, { row }) => <UserName fullName={row.createByName}></UserName>,
  },
  {
    title: '更新时间',
    colKey: 'updateTime',
    width: 189,
    cell: (h, { row }) => formatTime(row.updateTime),
  },
  {
    title: '操作',
    colKey: 'operation',
    width: 160,
    fixed: 'right',
    cell: (h, { row }) => (
      <t-space>
        <t-link theme="primary" hover="color" disabled={ !hasAuth(row, 'edit') } onClick={() => handleEdit(row) }>
          编辑
        </t-link>
        <t-link theme="primary" hover="color" disabled={ !hasAuth(row, 'grant') } onClick={() => handleAuth(row)}>
          授权
        </t-link>
        <t-link theme="primary" hover="color" disabled={ !hasAuth(row, 'edit') } onClick={() => handleDelete(row)}>
          删除
        </t-link>
      </t-space>
    ),
  },
]);

const fetchData = async () => {
  const { current: pageNum, pageSize } = pagination;
  const { queryKey, type } = form;
  const queryData = { type, queryKey, spaceId: route.query.spaceId };
  const params = { pageNum, pageSize, queryData };
  const [err, data] = await to(fetchBusiDatasourceList(params));
  if (err) {
    return;
  }
  const { total, list } = data;
  setPaginationTotal(total);
  tableData.value = list;
};

const { pagination, setPaginationCurrent, setPaginationSize, setPaginationTotal } = usePagination(fetchData);

const { onSearch } = useSearch(fetchData);

const searchChange = () => {
  setPaginationCurrent(1);
  onSearch();
};

const typeChange = () => {
  setPaginationCurrent(1);
  fetchData();
};

// 表单数据
const initFormData = {
  type: 'my',
  spaceId: '',
  queryKey: '',
};

const { form, resetData } = useForm(initFormData);

const handleReset = () => {
  setPaginationCurrent(1);
  resetData();
  fetchData();
};

const handleAddDataSource = () => {
  const value = `AddDataSource_${uuid()}`;
  const newTab = {
    label: '新增业务数据源',
    value,
    component: 'DataSourcePanel',
    removable: true,
    props: {
      tabValue: value,
    },
  };
  dataCatalogStore.addTabPanelList(newTab);
  dataCatalogStore.setCurrentTabValue(newTab.value);
};

const handleEdit = (row) => {
  const value = `EditDataSource_${row.id}`;
  const exist = tabPanelList.value.some(item => item.value === value);
  // 编辑时存在已打开过的数据源，切换到该数据源的页签
  if (exist) {
    dataCatalogStore.setCurrentTabValue(value);
    return;
  }
  const newTab = {
    label: `编辑业务数据源 ${row.name}`,
    value,
    component: 'DataSourcePanel',
    removable: true,
    props: {
      id: row.id,
      tabValue: value,
    },
  };
  dataCatalogStore.addTabPanelList(newTab);
  dataCatalogStore.setCurrentTabValue(newTab.value);
};

const handleView = (row) => {
  const value = `DataSourceDetail${row.id}`;
  const exist = tabPanelList.value.some(item => item.value === value);
  // 查看时存在已打开过的数据源，切换到该数据源的页签
  if (exist) {
    dataCatalogStore.setCurrentTabValue(value);
    return;
  }

  const newTab = {
    label: `查看业务数据源 ${row.name}`,
    value,
    component: 'DataSourceDetail',
    removable: true,
    props: {
      id: row.id,
    },
  };
  dataCatalogStore.addTabPanelList(newTab);
  dataCatalogStore.setCurrentTabValue(newTab.value);
};

const handleDelete = async (row) => {
  const result = await showDialogConfirm({
    title: `确定删除业务数据源「${row.name}」吗？`,
    body: '删除后不可恢复',
  });
  if (result.type === 'cancel') return;
  const [err] = await to(fetchBusiDatasourceDel({ datasourceId: row.id }, { showErrorMsg: false }));
  if (err) {
    showDialogAlert({
      title: `不可删除业务数据源「${row.name}」`,
      body: err.msg,
    });
    return;
  }
  fetchData();
  MessagePlugin('success', '操作成功');
};

const drawerRef = ref(null);
const handleAuth = (row) => {
  drawerRef.value.openDrawer(row);
};

const onRefresh = () => {
  eventBus.on(EVENT_NAMES.DATA_CATALOG.REFRESH_BUSINESS_DATASOURCE, () => {
    fetchData();
  });
};
const dispose = () => {
  eventBus.off(EVENT_NAMES.DATA_CATALOG.REFRESH_BUSINESS_DATASOURCE);
};

watch(() => props.current, (val) => {
  if (val !== 'DataSourceManagement') return;
  nextTick(() => fetchData());
}, { immediate: true });

watch(() => route.query.spaceId, (val) => {
  if (!val) return;
  if (props.current !== 'DataSourceManagement') return;
  setPaginationCurrent(1);
  setPaginationSize(10);
  fetchData();
});

onMounted(() => {
  onRefresh();
});
onBeforeUnmount(() => {
  dispose();
});
</script>

<style lang="less" scoped>
.page-catalog__data-source-management {
  height: 100%;
  .search-wrap {
    padding: 16px 0;
    display: flex;
    justify-content: space-between;
  }
  :deep(.t-table) {
    height: calc(100% - 64px);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
}
</style>
