<template>
  <div class="page-development__task-instance">
    <div class="search-wrap">
      <BasicSearch v-model.trim="form.queryKey" placeholder="项目名称/创建人/标识…" @reset="handleReset" @change="searchChange">
        <template #prefix>
          <TypeRadioGoup v-model="form.searchType" @change="typeChange"></TypeRadioGoup>
        </template>
      </BasicSearch>
      <t-button @click="handleAddProject"><add-icon slot="icon" />新增项目</t-button>
    </div>

    <!-- 数据表格 -->
    <t-table row-key="code" :data="tableData" :columns="columns"  :pagination="pagination" max-height="100%"/>

    <AuthDrawer ref="drawerRef" :spaceId="route.query.spaceId" @fetchData="fetchData"></AuthDrawer>
  </div>
</template>

<script setup>
import { storeToRefs } from 'pinia';
import { MessagePlugin } from 'tdesign-vue';
import { AddIcon } from 'tdesign-icons-vue';
import { useRoute } from 'vue-router/composables';
import { to, uuid, formatTime } from '@/utils/util';
import eventBus, { EVENT_NAMES } from '@/utils/emitter';
import { useForm, useSearch, usePagination } from '@/hooks';
import { showDialogConfirm, showDialogAlert } from '@/utils/dialog';
import { useDataDevelopmentStore } from '@/stores/data-development';
import { TypeRadioGoup, LabelWithDot } from '@/views/workbench/components';
import { computed, onBeforeUnmount, onMounted, ref, watch, defineProps, nextTick } from 'vue';
import UserName from '@/components/UserName.vue';
import AuthDrawer from './git-manage/AuthDrawer.vue';
import BasicSearch from '@/components/BasicSearch.vue';
import { hasAuth } from '../utils/auth';

const route = useRoute();

const props = defineProps({
  current: String,
});

const dataDevelopmentStore = useDataDevelopmentStore();
const { tabPanelList } = storeToRefs(dataDevelopmentStore);
const { fetchGitManagerList, fetchGitManagerBuild, fetchGitManagerCheck, fetchGitManagerDel } = dataDevelopmentStore;

const fetchData = async () => {
  const { current: pageNum, pageSize } = pagination;
  const { queryKey, searchType } = form;
  const queryData = {
    queryKey,
    searchType: searchType === 'my' ? 'PER' : 'ALL',
    spaceId: route.query.spaceId,
  };
  const params = { pageNum, pageSize, queryData };
  const [err, data] = await to(fetchGitManagerList(params));
  if (err) {
    return;
  }
  const { total, list } = data;
  setPaginationTotal(total);
  tableData.value = list;
};

const { pagination, setPaginationCurrent, setPaginationSize, setPaginationTotal } = usePagination(fetchData);

const { onSearch } = useSearch(fetchData);

const searchChange = () => {
  setPaginationCurrent(1);
  onSearch();
};

const typeChange = () => {
  setPaginationCurrent(1);
  fetchData();
};

// 表单数据
const initFormData = {
  searchType: 'my',
  queryKey: '',
};
const { form, resetData } = useForm(initFormData);

const handleReset = () => {
  setPaginationCurrent(1);
  resetData();
  fetchData();
};

const tableData = ref([]);

const getCell = row => (
  <t-space>
    <t-link theme="primary" hover="color" disabled={ !hasAuth(row, 'EDIT') } onClick={() => handleEdit(row) }>
      编辑
    </t-link>
    <t-link theme="primary" hover="color" disabled={ !hasAuth(row, 'GRANT') } onClick={() => handleGitAuth(row)}>
      授权
    </t-link>
    <t-link theme="primary" hover="color" disabled={ !hasAuth(row, 'EDIT') } onClick={() => handleConstruct(row)}>
      构建
    </t-link>
    {/* <t-link theme="danger" hover="color" onClick={() => handleParse(row)}>
      停止
    </t-link> */}
    <t-link theme="primary" hover="color" disabled={ !hasAuth(row, 'EDIT') } onClick={() => handleDelete(row)}>
      删除
    </t-link>
  </t-space>
);
const getStatusLabel = status => ({
  0: '未构建',
  1: '构建中',
  2: '构建成功',
  3: '构建失败',
  4: '需重新构建',
}[status]);
const getStatusColor = status => ({
  0: '#999999',
  1: '#3464E0',
  2: '#50BA5E',
  3: '#F81D22',
  4: '#999999',
}[status]);
const columns = computed(() => [
  { title: '序号', colKey: 'serial-number', width: 60, fixed: 'left', align: 'center' },
  { title: 'Git项目标识', colKey: 'gitProjectIdentifier', width: 108, ellipsis: true },
  { title: '项目名称', colKey: 'gitProjectName', width: 101, ellipsis: true },
  { title: '版本号', colKey: 'commitShortVersion', width: 139, cell: (h, { row }) => <t-link theme="primary" hover="color" href={ row.linkUrl } target="_blank">{ row.commitShortVersion }</t-link> },
  { title: 'Branch/Tag', colKey: 'reference', width: 112 },
  { title: '最新构建时间', colKey: 'buildTime', width: 191, cell: (h, { row }) => formatTime(row.buildTime) },
  {
    title: '构建状态',
    colKey: 'status',
    width: 98,
    cell: (h, { row }) => (
      <LabelWithDot color={ getStatusColor(row.status)} label={ getStatusLabel(row.status) }></LabelWithDot>
    ),
  },
  { title: '创建人', colKey: 'createUser', width: 171, cell: (h, { row }) => <UserName fullName={row.createUser}></UserName> },
  { title: '创建时间', colKey: 'createTime', width: 191, cell: (h, { row }) => formatTime(row.createTime) },
  {
    title: '操作',
    colKey: 'operation',
    width: 198,
    fixed: 'right',
    cell: (h, { row }) => getCell(row),
  },
]);

const handleAddProject = () => {
  const value = `AddGitProject_${uuid()}`;
  const newTab = {
    label: '新增Git项目',
    value,
    component: 'GitProjectPanel',
    removable: true,
    childPanel: 'GitManage', // 子面板
    props: {
      tabValue: value,
    },
  };
  dataDevelopmentStore.addTabPanelList(newTab);
  dataDevelopmentStore.setCurrentTabValue(newTab.value);
};

const handleConstruct = async (row) => {
  const result = await showDialogConfirm({
    title: '是否进行项目构建',
    width: '440px',
  });
  if (result.type === 'cancel') {
    return;
  }
  Object.assign(row, { status: 1 });
  const [err, data] = await to(fetchGitManagerBuild({ id: row.id }));
  if (err) {
    return;
  }
  MessagePlugin('success', data ? '构建成功' : '构建失败');
  fetchData();
};

// const handleParse = async (row) => {
//   const result = await showDialogConfirm({
//     title: `确定停止「${row.gitProjectName}」吗？`,
//     width: '440px',
//   });
//   if (result.type === 'cancel') return;
// };

const fetchDel = async (row) => {
  const [err, binded] = await to(fetchGitManagerCheck({ id: row.id, spaceId: route.query.spaceId }));
  if (err) return;
  if (binded) {
    showDialogAlert({
      title: `不可删除Git项目「${row.gitProjectName}」`,
      body: '当前Git项目已绑定工作流',
      width: '440px',
    });
    return;
  }
  const [error, data] = await to(fetchGitManagerDel(row.id));
  if (error) return;
  if (data) {
    fetchData();
    MessagePlugin('success', '操作成功');
  }
};
const handleDelete = async (row) => {
  const result = await showDialogConfirm({
    title: `确定删除Git项目「${row.gitProjectName}」吗？`,
    body: '删除后不可恢复',
    width: '440px',
  });
  if (result.type === 'cancel') return;
  fetchDel(row);
};

const handleEdit = (row) => {
  const value = `EditGitProject_${row.id}`;
  const exist = tabPanelList.value.some(item => item.value === value);
  // 编辑时存在已打开过的数据源，切换到该数据源的页签
  if (exist) {
    dataDevelopmentStore.setCurrentTabValue(value);
    return;
  }
  const newTab = {
    label: `编辑Git项目 ${row.gitProjectName}`,
    value,
    component: 'GitProjectPanel',
    removable: true,
    childPanel: 'GitManage', // 子面板
    props: {
      id: row.id,
      tabValue: value,
    },
  };
  dataDevelopmentStore.addTabPanelList(newTab);
  dataDevelopmentStore.setCurrentTabValue(newTab.value);
};

// 授权Drawer
const drawerRef = ref(null);
const handleGitAuth = async (row) => {
  drawerRef.value.openDrawer(row);
};

const onRefresh = () => {
  eventBus.on(EVENT_NAMES.DATA_DEVELOPMENT.REFRESH_GIT_PROJECT, () => {
    fetchData();
  });
};
const dispose = () => {
  eventBus.off(EVENT_NAMES.DATA_DEVELOPMENT.REFRESH_GIT_PROJECT);
};

watch(() => props.current, (val) => {
  if (val !== 'GitManage') return;
  nextTick(() => fetchData());
}, { immediate: true });

watch(() => route.query.spaceId, (val) => {
  if (!val) return;
  if (props.current !== 'GitManage') return;
  setPaginationCurrent(1);
  setPaginationSize(10);
  fetchData();
});

onMounted(() => {
  onRefresh();
});
onBeforeUnmount(() => {
  dispose();
});
</script>

<style lang="less" scoped>
.page-development__task-instance {
  height: 100%;
  .search-wrap {
    padding: 16px 0;
    display: flex;
    justify-content: space-between;
  }
  :deep(.t-table) {
    height: calc(100% - 64px);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
}
</style>
