<template>
  <BasicDialog width="560" :visible.sync="visible" :header="header" :footer="false" :showFooter="false">
    <t-table row-key="index" :data="list" :columns="columns" max-height="400" />
  </BasicDialog>
</template>

<script setup>
import OwnedSpace from '@/components/OwnedSpace.vue';
import BasicDialog from '@/components/BasicDialog.vue';
import { to } from '@/utils/util';
import { computed, defineExpose, ref } from 'vue';
import { useDialog } from '@/views/operations/hooks';
import { useOperationsStore } from '@/stores/operations';

const { fetchDatasourcesRelated } = useOperationsStore();
// 弹窗数据
const { visible, openAddDialog } = useDialog({});

const columns = computed(() => [
  {
    colKey: 'description',
    title: currentType.value === 'flow' ? '工作流名称' : '空间名称',
    cell: (h, { row }) => {
      const icon = currentType.value === 'flow' ? 'des-micon-gongzuoliu' : 'des-icon-kongjianfuzerenbiaoshi';
      return (
        <OwnedSpace icon={icon} name={row.name}></OwnedSpace>
      );
    },
  },
  {
    colKey: 'operation',
    title: '操作',
    width: 95,
    cell: () => <span>-</span>,
  },
]);

const currentType = ref('flow');
const list = ref([]);
const header = computed(() => (currentType.value === 'flow' ? '关联的工作流' : '关联的空间'));

const init = async (id, type) => {
  const params = { datasourceId: id };
  const [err, data] = await to(fetchDatasourcesRelated(params));
  if (err) {
    return;
  }
  const { srDatasourceSpaceRelationDTOList, srDatasourceProcessRelationDTOList } = data;
  list.value = type === 'flow' ? srDatasourceProcessRelationDTOList : srDatasourceSpaceRelationDTOList;
  currentType.value = type;
};

const show = async (id, type) => {
  await init(id, type);
  openAddDialog();
};

defineExpose({ show });
</script>

<style lang="less" scoped>

</style>
