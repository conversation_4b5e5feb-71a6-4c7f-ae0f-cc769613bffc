import { defineStore, acceptHMRUpdate } from 'pinia';
// import axios from '@/server/client';
// import { API_COMMON_CURRENT_USER_INFO } from '@/server/api/common';
// import waterMark from '@/utils/water-mark';
import { systemObj } from './system-storage';

export const useCommonStore = defineStore('common', {
  state: () => ({
    staffId: systemObj.staffId || '',
    staffName: systemObj.staffName || '',
    staffDisplayName: systemObj.staffDisplayName || '',
    operateCodeList: systemObj.operateCodeList || [], // 权限清单
    // 左侧菜单是否展开
    expand: true,
  }),
  actions: {
    // async getUserInfo() {
    //   const res = await axios({
    //     url: API_COMMON_CURRENT_USER_INFO,
    //     method: 'get',
    //   });
    //   const { data } = res;
    //   this.staffId = data.staffId;
    //   this.staffName = data.staffName;
    //   this.operateCodeList = data.operateCodeList || [];
    //   waterMark(data.staffName, 'water-mark');
    //   return res;
    // },
    toggleExpand() {
      this.expand = !this.expand;
    },
  },
});

if (import.meta.webpackHot) {
  import.meta.webpackHot.accept(acceptHMRUpdate(useCommonStore, import.meta.webpackHot));
}
