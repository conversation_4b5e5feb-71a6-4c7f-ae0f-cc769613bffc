<template>
  <t-tooltip
    :disabled="!overText"
    :content="`${text}`"
    :placement="placement"
  >
    <span
      @mouseenter="onMouseenter"
      :class="textClass"
      :style="textStyle"
      @click="textClick(text)"
      ref="text">{{ text }}</span>
  </t-tooltip>
</template>

<script>
import { debounce } from 'lodash';
import { addListener, removeListener } from 'resize-detector';

export default {
  name: 'EllipsisWithTooltip',
  props: {
    text: {
      type: [String, Number],
      default: '',
    },
    placement: {
      type: String,
      default: 'top',
    },
    lineClamp: {
      type: Number,
      default: 1,
    },
  },
  data() {
    return {
      overText: false,
    };
  },
  computed: {
    textStyle() {
      if (this.lineClamp > 1) {
        return {
          display: '-webkit-box',
          '-webkit-line-clamp': this.lineClamp,
          '-webkit-box-orient': 'vertical',
        };
      }
      return {};
    },
    textClass() {
      const classList = ['des-ellipsis-with-tooltip__text'];
      if (this.lineClamp >= 1) {
        classList.push(`des-line-clamp-${this.lineClamp}`);
      }
      return classList;
    },
  },
  mounted() {
    this.detectTip();
  },
  beforeDestroy() {
    this.unDetectTip();
  },
  methods: {
    detectTip() {
      const textRef = this.$refs.text;
      if (textRef) {
        this.resizeHandler = debounce(this.triggerText, 100);
        addListener(textRef, this.resizeHandler);
      }
    },
    // 鼠标移入才检测，量大的时候不会存在过多的dom操作
    onMouseenter() {
      this.triggerText();
    },
    triggerText() {
      const textRef = this.$refs.text;
      if (textRef) {
        // height可能存在一些误差
        if ((textRef.scrollWidth - textRef.clientWidth > 0) || (textRef.scrollHeight - textRef.clientHeight) > 2) {
          this.overText = true;
        } else {
          this.overText = false;
        }
      }
    },
    unDetectTip() {
      try {
        if (this.$refs.text) {
          removeListener(this.$refs.text, this.resizeHandler);
        }
      } catch (error) {
      }
    },
    textClick(value) {
      this.$emit('click', value);
    },
  },
};
</script>

<style lang="less" scoped>
.des-ellipsis-with-tooltip__text {
  display: inline-block;
  max-width: 100%;

  &.des-line-clamp-1 {
    white-space: nowrap;
  }

  &[class*=des-line-clamp-] {
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: middle;
  }
}
</style>
