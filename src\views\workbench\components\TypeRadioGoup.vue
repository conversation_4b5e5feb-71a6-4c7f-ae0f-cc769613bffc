<template>
  <t-radio-group :value="value" variant="default-filled" @change="handleChange">
    <t-radio-button value="my">我的</t-radio-button>
    <t-radio-button value="all">全部</t-radio-button>
  </t-radio-group>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue';
defineProps({
  value: {
    type: String,
    required: true,
    default: 'my',
  },
});
const emit = defineEmits(['input', 'change']);

const handleChange = (checked) => {
  emit('input', checked);
  emit('change', checked);
};
</script>
