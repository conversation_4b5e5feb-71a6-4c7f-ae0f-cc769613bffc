import { ref } from 'vue';
import { isFunction } from 'lodash';

const modeTypes = {
  ADD: 'ADD',
  EDIT: 'EDIT',
};

export function useDialog({ confirmCb, closeCb, editCb } = {}) {
  const mode = ref(modeTypes.ADD);
  const visible = ref(false);

  const openAddDialog = () => {
    mode.value = modeTypes.ADD;
    visible.value = true;
  };

  const openEditDialog = (data) => {
    if (isFunction(editCb)) {
      editCb(data);
    }
    mode.value = modeTypes.EDIT;
    visible.value = true;
  };

  const onConfirm = async () => {
    if (isFunction(confirmCb)) {
      const res = await confirmCb();
      if (res) onClose();
    }
  };

  const onClose = () => {
    if (isFunction(closeCb)) {
      closeCb();
    }
    visible.value = false;
  };
  return { mode, visible, openAddDialog, openEditDialog, onConfirm, onClose };
}
