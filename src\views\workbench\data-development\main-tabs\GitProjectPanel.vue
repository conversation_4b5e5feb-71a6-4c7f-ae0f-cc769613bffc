<template>
  <div class="page-des-git-project-panel">
    <section>
      <BlockHeader title="基本信息" style="margin-bottom: 12px"></BlockHeader>

      <t-form ref="formRef" :data="form" :rules="rules" label-width="106px" style="width: 480px;">
        <t-form-item label="仓库地址" name="gitRepoAddr">
          <t-input v-model="form.gitRepoAddr" placeholder="请输入" @change="handleAddrChange"/>
        </t-form-item>
        <t-form-item label="项目名称" name="gitProjectName">
          <t-input v-model="form.gitProjectName" placeholder="请输入" />
        </t-form-item>
        <t-form-item label="项目标识" name="gitProjectIdentifier">
          <t-input v-model="form.gitProjectIdentifier" placeholder="请输入" :disabled="isEditType" />
        </t-form-item>
        <t-form-item label="描述" name="description">
          <t-textarea
            v-model="form.description"
            placeholder="请输入"
            :maxlength="200"
            :autosize="{ minRows: 3, maxRows: 3 }"
          />
        </t-form-item>
        <t-form-item label="用户名" name="userName">
          <t-input v-model="form.userName" placeholder="请输入" @change="handleChange"/>
        </t-form-item>
        <t-form-item label="密码" name="passWord">
          <PasswrodInput v-model="form.passWord" :mode="mode" :inputing="inputing" @updateInputing="setInputing(true)" @change="handleChange"></PasswrodInput>
        </t-form-item>
        <t-form-item label="Branch/Tag" name="reference">
          <t-select v-model="form.reference" :options="options" :loading="loading" filterable placeholder="请选择" loading-text="数据加载中" empty="暂无数据，输入仓库地址、用户名、密码获取" @popup-visible-change="initBranches" />
        </t-form-item>
      </t-form>
    </section>

    <BasicPageFooter height="72" @confirm="handleConfirm" @cancel="handleCancel"></BasicPageFooter>
  </div>
</template>

<script setup>
import { useForm, usePassword } from '@/hooks';
import { to } from '@/utils/util';
import { cloneDeep } from 'lodash';
import { MessagePlugin } from 'tdesign-vue';
import { useRoute } from 'vue-router/composables';
import eventBus, { EVENT_NAMES } from '@/utils/emitter';
import { useDataDevelopmentStore } from '@/stores/data-development';
import { ref, defineProps, defineEmits, computed, onMounted } from 'vue';
import BlockHeader from '@/components/BlockHeader.vue';
import PasswrodInput from '@/components/PasswrodInput.vue';
import BasicPageFooter from '@/components/BasicPageFooter.vue';

const route = useRoute();

const { inputing, setInputing } = usePassword();

const dataDevelopmentStore = useDataDevelopmentStore();
const { fetchGitManagerBranches, fetchGitManagerAdd, fetchGitManagerGet, fetchGitManagerUpdate, fetchGitManagerCheckName } = dataDevelopmentStore;

const emit = defineEmits(['remove', 'switch']);
const props = defineProps({
  id: String,
  tabValue: String,
});

// 编辑状态
const isEditType = computed(() => Boolean(props.id));
const mode = computed(() => (props.id ? 'EDIT' : 'ADD'));

const initFormData = {
  gitRepoAddr: '',
  gitProjectName: '',
  gitProjectIdentifier: '',
  description: '',
  userName: '',
  passWord: '',
  reference: '',
};
const checkPassWord = (val) => {
  if ((mode.value === 'ADD' || inputing.value) && !val) {
    return {
      result: false,
      message: '请输入',
      type: 'error',
    };
  }
  return true;
};
const getRule = (array = []) => [
  {
    required: true,
    message: '必填',
    type: 'error',
    trigger: 'change',
  },
  {
    whitespace: true,
    message: '不能为空',
  },
  ...array,
];
const initFormRules = {
  gitRepoAddr: getRule(),
  gitProjectName: getRule(),
  gitProjectIdentifier: getRule(),
  userName: getRule(),
  passWord: [
    { whitespace: true, message: '不能为空' },
    { validator: checkPassWord, trigger: 'change' },
  ],
  reference: [
    {
      required: true,
      message: '必选',
      type: 'error',
      trigger: 'change',
    },
  ],
};
const { form, formRef, rules, validateForm } = useForm(initFormData, initFormRules);

const checkRepeat = async () => {
  if (isEditType) {
    if (form.gitProjectName === backupForm.gitProjectName) {
      return false;
    }
  }
  const params = { spaceId: route.query.spaceId, projectName: form.gitProjectName };
  const [err, data] = await to(fetchGitManagerCheckName(params));
  if (err) {
    return true;
  }
  data && MessagePlugin('error', 'Git项目名称重复');
  return data;
};

const handleConfirm = async () => {
  const valid = await validateForm();
  if (!valid) return;

  const isRepeat = await checkRepeat();
  if (isRepeat) return;

  const params = { ...form, spaceId: route.query.spaceId };
  const api = isEditType.value ? fetchGitManagerUpdate : fetchGitManagerAdd;
  const [err] = await to(api(params));
  if (err) {
    return;
  }
  MessagePlugin('success', '操作成功');
  handleCancel();
  handleSwitch();
  handleRefresh();
};

// 关闭当前tab
const handleCancel = () => {
  emit('remove', { value: props.tabValue });
};
// 切换到指定的tab
const handleSwitch = () => {
  emit('switch', 'DataDevelopment', {});
};
// 刷新Git管理列表数据
const handleRefresh = () => {
  eventBus.emit(EVENT_NAMES.DATA_DEVELOPMENT.REFRESH_GIT_PROJECT);
};

const loading = ref(false);
const options = ref([]);
const initBranches = async (visible) => {
  if (!visible) return;
  const fields = ['gitRepoAddr', 'userName', 'passWord'];
  const valid = await validateForm(fields);
  if (!valid) return MessagePlugin('warning', '仓库地址、用户名、密码必填');

  if (options.value.length) return;

  loading.value = true;
  const params = { ...form };
  const [err, data] = await to(fetchGitManagerBranches(params));
  loading.value = false;
  if (err) {
    options.value = [];
    return;
  }
  options.value = data.branches.map(item => ({ label: item, value: item }));
};

const handleAddrChange = (value) => {
  handleChange();
  if (!value) return; // 防止空值报错

  const parts = value.split('/');
  if (parts.length === 0) return; // 防止分割失败

  let projectName = parts[parts.length - 1]; // 获取最后一部分
  projectName = projectName.replace('.git', ''); // 移除.git后缀

  form.gitProjectName = projectName;
  form.gitProjectIdentifier = projectName;
};

const handleChange = () => {
  options.value = [];
  Object.assign(form, { reference: '' });
};

let backupForm = {};
const fetchData = async () => {
  if (!isEditType.value) return;
  const [err, data] = await to(fetchGitManagerGet(props.id));
  if (err) {
    return;
  }
  Object.assign(form, data);
  backupForm = cloneDeep(form);
  initBranches();
};

onMounted(() => {
  fetchData();
});
</script>

<style lang="less" scoped>
.page-des-git-project-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  > section {
    padding: 20px;
  }
  :deep(.basic-page-footer__wrap) {
    justify-content: start;
    padding-left: 126px;
  }
}
</style>
