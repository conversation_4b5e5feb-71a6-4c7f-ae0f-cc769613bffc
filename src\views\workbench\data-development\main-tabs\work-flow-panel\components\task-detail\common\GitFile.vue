<script setup>
import { debounce } from 'lodash';
import { useRoute } from 'vue-router/composables';
import { ref, watchEffect, defineProps, onMounted } from 'vue';
import { useDataDevelopmentStore } from '@/stores/data-development';
import CodePanel from './CodePanel.vue';
import FilePathSelect from '@/components/FilePathSelect.vue';
import { formatTime, isEmptyValue } from '@/utils/util';
import { showDialogAlert } from '@/utils/dialog';

const props = defineProps({
  formData: {
    type: Object,
    required: true,
  },
});

const route = useRoute();

const dataDevelopmentStore = useDataDevelopmentStore();
// 项目列表
const projectOptions = ref([]);
// 文件内容
const fileContent = ref('');
const loading = ref(false);
const projectRemoteMethod = debounce((query) => {
  loading.value = true;
  dataDevelopmentStore.fetchGitManagerList({
    queryData: {
      queryKey: query,
      spaceId: route.query.spaceId,
      searchType: 'PER_DRAW',
    },
    pageNum: 1,
    pageSize: 50,
  }, { loading: false }).then((res) => {
    projectOptions.value = res.data.list.map(item => ({
      ...item,
      label: item.gitProjectName,
      value: item.id,
    }));
  })
    .finally(() => {
      loading.value = false;
    });
}, 500);

function queryFile(query) {
  return dataDevelopmentStore.fetchGitManagerFilePath({
    id: props.formData.taskProperties.gitProjectId,
    userInputPath: query,
  }, { loading: false });
}

const tip = ref('');
function queryFileContent(path) {
  return dataDevelopmentStore.fetchGitManagerFileContent({
    gitManagerId: props.formData.taskProperties.gitProjectId,
    filePath: path,
  }, { loading: false }).then((res) => {
    fileContent.value = res.data;
    tip.value = '';

    if (res.data.includes('File not found')) {
      fileContent.value = '';
      const lastSlashIndex = path.lastIndexOf('/');
      const fileName = path.substring(lastSlashIndex + 1);
      if (fileName.includes('.')) {
        tip.value = '请输入已存在的文件路径';
      } else {
        tip.value = '不支持输入目录，请输入文件路径';
      }
    }
  });
}

function handleChange(val) {
  const target = projectOptions.value.find(item => item.value === val);
  if (!target) return;

  const { buildTime, status } = target;
  const dialogConfig = {
    title: '系统提示',
    width: '440px',
    body: null,
  };

  if (isEmptyValue(buildTime) && status === 0) {
    dialogConfig.body = () => (
      <div style="padding-left: 26px;">
        <p>项目未构建，请先在Git项目管理执行构建</p>
      </div>
    );
  } else {
    dialogConfig.body = () => (
      <div style="padding-left: 26px;">
        <p>当前Git项目构建时间：{formatTime(buildTime)}</p>
        <p>如有更新，建议在Git项目管理重新构建一次</p>
      </div>
    );
  }

  showDialogAlert(dialogConfig);
}

watchEffect(() => {
  if (props.formData.taskProperties.gitFile && props.formData.taskProperties.gitProjectId) {
    queryFileContent(props.formData.taskProperties.gitFile);
  }
});

onMounted(() => {
  projectRemoteMethod('');
});
</script>

<template>
  <!-- eslint-disable vue/no-mutating-props -->
  <section style="margin-top: 16px;">
    <t-form-item label="项目" name="taskProperties.gitProjectId">
      <t-select
        v-model="formData.taskProperties.gitProjectId"
        placeholder="请选择"
        filterable
        :loading="loading"
        :onSearch="projectRemoteMethod"
        :options="projectOptions"
        @change="handleChange"
        style="width: 374px"
      ></t-select>
    </t-form-item>
    <t-form-item label="文件" name="taskProperties.gitFile">
      <div style="display: flex;flex-direction: column;">
        <file-path-select
          v-model="formData.taskProperties.gitFile"
          :disabled="!formData.taskProperties.gitProjectId"
          :onSearch="queryFile"
          style="width: 374px"
        ></file-path-select>
        <p style="color: #f81d22; font-size: 12px;" v-if="tip">{{ tip }}</p>
      </div>
    </t-form-item>
    <t-form-item label="文件内容">
      <code-panel :content="fileContent" style="width: 100%"></code-panel>
      <!-- <p class="file-content">{{ fileContent }}</p> -->
    </t-form-item>
  </section>
</template>
