<template>
  <div class="page-layout">
    <!-- 搜索和操作区域 -->
    <div class="operation-wrap">
      <div class="search-wrap">
        <slot name="search"></slot>
      </div>
      <div class="button-wrap">
        <slot name="button"></slot>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-wrap">
      <slot name="table"></slot>
    </div>
  </div>
</template>

<script setup lang="jsx" name="page-layout"></script>

<style lang="less" scoped>
.page-layout {
  height: 100%;
  display: flex;
  flex-direction: column;
  .operation-wrap {
    flex-shrink: 0;
    padding: 16px 0;
    display: flex;
    justify-content: space-between;

    .search-wrap {
      display: flex;
    }
  }

  .table-wrap {
    flex: 1;

    :deep(.t-table) {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 100%;
    }
  }
}
</style>
