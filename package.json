{"name": "des-web", "version": "0.1.0", "private": true, "scripts": {"postinstall": "patch-package", "serve": "vue-cli-service serve --mode serve", "build:dev": "vue-cli-service build --mode dev", "build:test": "vue-cli-service build --mode test", "build:uat": "vue-cli-service build --mode uat", "build:sim": "vue-cli-service build --mode sim", "build": "vue-cli-service build --mode prod", "test:unit": "vue-cli-service test:unit", "lint": "vue-cli-service lint", "prepare": "husky"}, "dependencies": {"@antv/layout": "^0.3.25", "@antv/x6": "^2.18.1", "@antv/x6-plugin-keyboard": "^2.2.3", "@antv/x6-plugin-selection": "^2.2.2", "@antv/x6-vue-shape": "^2.1.2", "@tencent/sdc-webui-vue": "0.0.5", "axios": "^1.8.4", "core-js": "^3.8.3", "cronosjs": "^1.7.1", "dayjs": "^1.11.13", "mitt": "^3.0.1", "monaco-editor": "^0.52.2", "pinia": "^2.3.1", "pinia-plugin-persistedstate": "^4.2.0", "resize-detector": "^0.3.0", "splitpanes": "^2.4.1", "tdesign-vue": "1.11.1-<PERSON><PERSON><PERSON>", "vue": "^2.7.16", "vue-crontab-ui": "^0.1.0", "vue-router": "^3.6.5"}, "overrides": {"tdesign-vue": "$tdesign-vue"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@babel/plugin-transform-class-static-block": "^7.26.0", "@tencent/eslint-config-tencent": "^1.1.2", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-unit-jest": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/eslint-config-airbnb": "^6.0.0", "@vue/test-utils": "^1.1.3", "@vue/vue2-jest": "^27.0.0-alpha.2", "babel-jest": "^27.0.6", "eslint": "^8.57.1", "eslint-plugin-import": "^2.25.3", "eslint-plugin-vue": "^8.0.3", "eslint-plugin-vuejs-accessibility": "^1.1.0", "husky": "^9.1.7", "jest": "^27.0.5", "less": "^4.0.0", "less-loader": "^8.0.0", "monaco-editor-webpack-plugin": "^7.1.0", "patch-package": "^8.0.0", "vue-template-compiler": "^2.7.16"}}