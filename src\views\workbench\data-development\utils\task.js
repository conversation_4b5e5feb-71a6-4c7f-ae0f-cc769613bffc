import { useDataDevelopmentStore } from '@/stores/data-development';
import { to } from '@/utils/util';

// 获取调度日志
export async function getScheduleLog(row) {
  const dataDevelopmentStore = useDataDevelopmentStore();
  const { taskInstanceCode } = row;
  const [err, data] = await to(dataDevelopmentStore.fetchTaskInstanceLog({ taskInstanceId: taskInstanceCode }));
  if (err) {
    return;
  }
  return data;
}
// 获取执行日志
export async function getExecuteLog(row, spaceId) {
  const dataDevelopmentStore = useDataDevelopmentStore();
  const { taskInstanceCode, processCode, startTime } = row;
  const [err, data] = await to(dataDevelopmentStore.fetchTaskInstancePodLog({ spaceId, processCode, taskInstanceId: taskInstanceCode, startTime }));
  if (err) {
    return;
  }
  return data;
}
/**
 * 查找任务依赖链
 * @param {object} taskRelations
 * @returns {array} 任务依赖链(code)
 */
export function findDependencyChain(taskRelations) {
  // 1. 构建依赖关系图：preTaskCode -> postTaskCode
  const dependencyMap = new Map();
  // 记录所有postTaskCode，用于判断某个preTaskCode是否是其他任务的起点
  const allPostTaskCodes = new Set();

  for (const task of taskRelations) {
    if (task.preTaskCode !== '0') {
      dependencyMap.set(task.preTaskCode, task.postTaskCode);
    }
    allPostTaskCodes.add(task.postTaskCode);
  }

  // 2. 找到所有起点（preTaskCode为'0'的postTaskCode）
  const startPoints = [];
  const noDependencyTasks = []; // 存放无依赖的任务（preTaskCode为'0'且无后续依赖）
  for (const task of taskRelations) {
    if (task.preTaskCode === '0') {
      if (dependencyMap.has(task.postTaskCode)) {
        startPoints.push(task.postTaskCode);
      } else {
        noDependencyTasks.push(task.postTaskCode);
      }
    }
  }

  // 3. 从起点开始遍历依赖链
  const dependencyChain = [];
  for (const start of startPoints) {
    let current = start;
    const chain = [current];

    while (dependencyMap.has(current)) {
      current = dependencyMap.get(current);
      chain.push(current);
    }

    if (chain.length > 1) {
      dependencyChain.push(...chain);
    }
  }

  // 4. 合并依赖链和无依赖任务
  return [...dependencyChain, ...noDependencyTasks];
}
