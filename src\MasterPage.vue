<template>
  <div class="des-app">
    <head-menu @logout="handleLogout" :avatar="avatar">
      <template #header-left>
        <div class="des-logo" @click="clickLogo">
          <img src="@/assets/head_logo.svg" />
        </div>
      </template>
      <template #header-center>
        <ul class="des-menu-items">
          <router-link class="des-menu-item" v-for="item in menus" :key="item.value" :to="{name: item.value}" @click.native="handleClick(item.value)">{{ item.label }}</router-link>
        </ul>
      </template>
      <template #header-right>
        <SelectWithIcon
          style="margin-right: 24px;"
          v-model="currentSpace"
          :options="spaces"
          :icon="icon"
          :autoWidth="true"
          :popupProps="popupProps"
          @change="spaceChange"
          class="des-space-select"
          v-show="showSpaceSelect">
        </SelectWithIcon>
        <!-- <NotificationIcon @click="handleNotice" class="des-notice" /> -->
      </template>
    </head-menu>
    <div class="des-page-container">
      <router-view/>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, reactive, ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { useRoute, useRouter } from 'vue-router/composables';
// import { NotificationIcon } from 'tdesign-icons-vue';
import HeadMenu from '@/components/HeadMenu.vue';
import emitter, { EVENT_NAMES } from '@/utils/emitter';
import SelectWithIcon from '@/components/SelectWithIcon.vue';
import { useCommonStore } from '@/stores/common';
import { useSpacesStore } from '@/stores/spaces';
import { showDialogConfirm } from './utils/dialog';
import { useDataDevelopmentStore } from '@/stores/data-development';

const route = useRoute();
const router = useRouter();

const commonStore = useCommonStore();
const { staffName, operateCodeList } = storeToRefs(commonStore);
const avatar = computed(() => `https://rhrc.woa.com/photo/150/${staffName.value}.png`);
const menus = computed(() => [
  { label: '工作台', value: 'workbench', auth: 'Menu_workbench' },
  { label: '空间管理', value: 'spaces', auth: 'Menu_spaceManage' },
  { label: '系统运维', value: 'operations', auth: 'Menu_systemOperation' },
  { label: '任务监控', value: 'monitor', auth: 'Menu_taskMonitor' },
].filter(item => operateCodeList.value.includes(item.auth)));

// 定义下拉宽度始终与触发浮层的元素（即select组件）等宽
const popupProps = reactive({
  overlayInnerStyle: trigger => ({
    width: `${trigger.offsetWidth}px`,
  }),
});

const icon = 'des-icon-kongjianfuzerenbiaoshi';
const showSpaceSelect = computed(() => route.path.includes('/workbench'));
const spaces = ref([]);

const spaceStore = useSpacesStore();
const { currentSpace } = storeToRefs(spaceStore);

const dataDevelopmentStore = useDataDevelopmentStore();

function spaceChange(value) {
  spaceStore.setCurrentSpace(value);
  updateRouteQuery(value, 'push');
}

// 判断持久化选中的空间是否存在
function isExist(mySpaces) {
  if (currentSpace.value) {
    return mySpaces.some(item => item.id === currentSpace.value);
  }
  return false;
}

async function initMySpaces() {
  const result = await spaceStore.getMySpaces({ queryData: { areaId: '', queryKey: '' }, pageNum: 1, pageSize: 999 });
  spaces.value = result.data.list.map(item => ({ label: item.name, value: item.id }));
  // 持久化的空间不存在时，更新当前空间
  if (!isExist(result.data.list)) {
    const spaceId = spaces.value[0]?.value;
    spaceStore.setCurrentSpace(spaceId);
  }
  // 保存我的空间列表
  spaceStore.setMySpaces(result.data.list);
  // 更新路由参数
  updateRouteQuery(currentSpace?.value, 'replace');
}

async function updateRouteQuery(spaceId, method) {
  if (spaceId) {
    const query = { ...route.query, spaceId };
    router[method]({ query });
    // 判断是否是当前空间的负责人
    spaceStore.checkSpaceIsOwner(spaceId);
  }
}

function clickLogo() {
  window.location.href = '/';
}

// function handleNotice() {
//   console.log('handleNotice');
// }

async function handleLogout() {
  console.log('handleLogout');
  const { closeReminder, needCompareNodeData } = dataDevelopmentStore.currentTabPanel;
  if (!closeReminder) {
    logoutHandle();
    return;
  }

  if (needCompareNodeData) {
    emitter.emit(EVENT_NAMES.WORK_FLOW_PANEL.GRAPH_DATA_COMPARE, { panel: dataDevelopmentStore.currentTabValue });
    if (dataDevelopmentStore.flowDataIsEqual) {
      logoutHandle();
      return;
    }
  }

  const { type } = await showDialogConfirm({
    title: '确定退出配置吗？',
    body: '退出后，您编辑的内容将不会保存',
    width: '440px',
  });

  if (type === 'success') {
    logoutHandle();
  }
}

function logoutHandle() {
  localStorage.removeItem('spaces');
  const url = encodeURIComponent(window.location.origin);
  if (process.env.VUE_APP_ENV === 'prod') {
    window.location.href = `https://${window.location.host}/_logout/?url=${url}`;
  } else {
    window.location.href = `https://uat.passport.woa.com/modules/passport/signout.ashx?url=${url}`;
  }
}


function handleClick(value) {
  if (value === 'workbench') {
    initMySpaces();
  }
}

//  点击浏览器的回退更新当前空间
watch(() => route.query.spaceId, (val) => {
  if (val) {
    spaceStore.setCurrentSpace(val);
  }
});

onMounted(() => {
  if (showSpaceSelect.value) {
    initMySpaces();
  }
});
</script>

<style lang="less" scoped>
// .des-head-menu {
//   background: linear-gradient(270deg, #3A8BF2 0%, #0B5DDE 100%);
// }
.des-app {
  background-color: #EBF0F7;
  height: 100%;
  .des-logo {
    display: flex;
    width: 207px;
    line-height: 60px;
    color: #fff;
    align-items: center;
    height: 60px;
    img {
      display: block;
      height: 28px;
    }
  }
  .des-menu-items {
    position: relative;
    display: flex;
    &::before {
      position: absolute;
      left: 0;
      top: 20px;
      width: 1px;
      height: 20px;
      background-color: #fff;
      opacity: 0.5;
      content: ' ';
    }
    .des-menu-item {
      position: relative;
      color: #ffffff;
      text-align: center;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 60px;
      opacity: 0.5;
      margin-left: 28px;
      cursor: pointer;
      &:first-child {
        margin-left: 20px;
      }
      // &.actived,
      &.router-link-active {
        opacity: 1;
        &::after {
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 2px;
          background-color: #fff;
          content: ' ';
        }
      }
    }
  }
  :deep(.des-space-select) {
    .t-input__wrap {
      .t-input.t-is-readonly {
        color: #fff;
        border-radius: 4px;
        border: 1px solid rgba(255, 255, 255, 0.10);
        background: rgba(255, 255, 255, 0.10);
      }
      .t-fake-arrow {
        color: #fff;
      }
    }
    .t-input--auto-width {
      min-width: 180px;
    }
  }
  .des-notice {
    cursor: pointer;
    margin: 0 24px;
  }
  .des-page-container {
    height: calc(~'100% - 60px');
    overflow: auto;
  }
}
</style>
