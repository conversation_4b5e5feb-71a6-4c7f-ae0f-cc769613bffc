<template>
  <BasicDialog
    width="900"
    :visible.sync="visible"
    header="测试黑名单"
    :submitDisabled="submitDisabled"
    :onClosed="onClose"
    @handleClose="onClose"
    @handleConfirm="onConfirm"
  >
    <section class="test-editor-wrap" v-if="visible">
      <BasicSqlEditor v-model="form.sqlRegex" title="测试 SQL"></BasicSqlEditor>
    </section>
  </BasicDialog>
</template>

<script setup>
import { to } from '@/utils/util';
import { defineExpose, reactive, computed, defineProps } from 'vue';
import { useDialog } from '@/views/operations/hooks';
import BasicDialog from '@/components/BasicDialog.vue';
import BasicSqlEditor from '@/components/BasicSqlEditor.vue';
import { useOperationsStore } from '@/stores/operations';
import { MessagePlugin } from 'tdesign-vue';

const operationsStore = useOperationsStore();

const props = defineProps({
  sql: {
    type: String,
    default: '',
  },
});

const form = reactive({ sqlRegex: '' });

// 提交按钮禁用状态
const submitDisabled = computed(() => !form.sqlRegex);

const closeCb = () => {
  form.sqlRegex = '';
};

const confirmCb = async () => {
  const [err, data] = await to(operationsStore.fetchBlacklistCheck({ sql: props.sql, sqlRegex: form.sqlRegex }));
  if (err) {
    return;
  }
  if (data) {
    onClose();
    MessagePlugin.success('测试通过');
    return;
  }
  MessagePlugin.error('测试不通过');
};

// 弹窗数据
const { visible, openAddDialog, onConfirm, onClose } = useDialog({
  confirmCb,
  closeCb,
});

defineExpose({ openAddDialog });
</script>

<style lang="less" scoped>
:deep(.t-dialog__body) {
  height: 378px;
}
.test-editor-wrap {
  height: 346px;
}
</style>
