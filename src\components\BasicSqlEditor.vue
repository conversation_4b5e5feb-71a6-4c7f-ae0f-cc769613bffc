<template>
  <div class="basic-sql-editor-wrap">
    <div class="basic-title-wrap" v-if="title">
      <div>{{ title }}</div>
      <slot></slot>
    </div>
    <div ref="editorRef" class="editor"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, onBeforeUnmount, defineProps, defineEmits } from 'vue';
import * as monaco from 'monaco-editor';
import { omit } from 'lodash';

// 默认配置
const defaultOptions = {
  // 语言
  language: 'sql',
  stickyScroll: {
    enabled: false, // 禁用粘性滚动功能
  },
  // 主题
  theme: 'vs', // vs、vs-dark
  // 行高
  lineHeight: 18,
  // 选中行高亮
  renderLineHighlight: 'none',
  // 右侧条栏
  overviewRulerBorder: false,
  // 行号的宽度
  lineDecorationsWidth: 0,
  // 行号宽度
  lineNumbersMinChars: 2,
  // 略缩图
  minimap: {
    enabled: false,
  },
  // 自动调整布局
  // Enable that the editor will install an interval to check if its container dom node size has changed.
  // Enabling this might have a severe performance impact. Defaults to false.
  automaticLayout: false,
  // 禁止滚动到最后一行之后
  scrollBeyondLastLine: false,
  // 不显示行号
  lineNumbers: 'off',
  // 换行
  wordWrap: 'on',
  scrollbar: {
    /**
     * 箭头的大小（如果显示的话）。
     * 默认值为11。
     * **注意**：此选项无法通过 `updateOptions()` 更新
     */
    arrowSize: 11,
    /**
     * 渲染垂直滚动条。
     * 默认值为'auto'。
     */
    vertical: 'auto',
    /**
     * 渲染水平滚动条。
     * 默认值为'auto'。
     */
    horizontal: 'auto',
    /**
     * 当内容滚动时显示水平和垂直阴影。
     * 默认值为true，鼠标移出会隐藏
     * **注意**：此选项无法通过 `updateOptions()` 更新
     */
    useShadows: true,
    /**
     * 监听鼠标滚轮事件并通过滚动来响应。
     * 默认值为true。
     */
    handleMouseWheel: true,
    /**
     * 始终拦截鼠标滚轮事件（总是在浏览器事件上调用 preventDefault() 和 stopPropagation()）。
     * 默认值为true，这意味着滚轮事件不会传播到编辑器之外的其他元素
     * **注意**：此选项无法通过 `updateOptions()` 更新
     */
    alwaysConsumeMouseWheel: false,
    /**
     * 垂直滚动条的宽度（像素）。
     * 默认值为10（像素）。
     */
    verticalScrollbarSize: 4,
    /**
     * 垂直滑块的宽度（像素）。
     * 默认值等于 `verticalScrollbarSize`。
     * **注意**：此选项无法通过 `updateOptions()` 更新
     */
    verticalSliderSize: 4,
    /**
     * 水平滚动条的宽度（像素）。
     * 默认值为10（像素）。
     */
    horizontalScrollbarSize: 4,
    /**
     * 水平滑块的宽度（像素）。
     * 默认值等于 `verticalScrollbarSize`。
     * **注意**：此选项无法通过 `updateOptions()` 更新
     */
    horizontalSliderSize: 4,
    /**
     * 点击滚动槽时是按页滚动还是跳转到点击位置。
     * 默认值为false。
     */
    scrollByPage: false,
    /**
     * 设置后，水平滚动条不会影响编辑器的内容高度
     * 默认值为false，会占用编辑器的内容高度
     */
    ignoreHorizontalScrollbarInContentHeight: true,
  },
};

const props = defineProps({
  value: {
    type: String,
    default: '-- 在这里编写 SQL 查询\nSELECT * FROM users;',
  },
  theme: {
    type: String,
    default: 'vs', // 支持的主题：vs, vs-dark, hc-black
  },
  language: {
    type: String,
    default: 'sql',
  },
  readOnly: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
  },
});

// 定义 Emits
const emit = defineEmits(['input']);

// DOM 引用和编辑器实例
const editorRef = ref(null);
let editor = null;

const initEditor = () => {
  if (editorRef.value) {
    editor = monaco.editor.create(editorRef.value, {
      ...defaultOptions,
      // omit 忽略对象的属性
      ...omit(props, ['title']),
    });

    // 监听内容变化
    editor.onDidChangeModelContent(() => {
      emit('input', editor.getValue());
    });
  }
};

// 监听父组件传入的值变化
watch(
  () => props.value,
  (newValue) => {
    if (editor && editor.getValue() !== newValue) {
      editor.setValue(newValue);
    }
  },
);

// 监听主题变化
watch(
  () => props.theme,
  (newTheme) => {
    if (editor) {
      editor.setTheme(newTheme);
    }
  },
);

// 监听只读模式变化
watch(
  () => props.readOnly,
  (newValue) => {
    if (editor) {
      editor.updateOptions({ readOnly: newValue });
    }
  },
);

onMounted(() => {
  initEditor();
});

// 销毁编辑器实例
onBeforeUnmount(() => {
  if (editor) editor.dispose();
});
</script>

<style lang="less" scoped>
.basic-sql-editor-wrap {
  height: 100%;
  display: flex;
  flex-direction: column;
  .basic-title-wrap {
    flex-shrink: 0;
    height: 38px;
    padding: 8px 16px;
    background: #eee;
    border: 1px solid #dcdcdc;
    border-radius: 5px 5px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .editor {
    flex: 1;
    width: 100%;
    border: 1px solid #dcdcdc;
    border-top: 0;
  }
}
</style>
