import axios from '@/server/client';
import { defineStore, acceptHMRUpdate } from 'pinia';
import {
  API_OPERATIONS_DATASOURCES_LIST,
  API_OPERATIONS_DATASOURCES_ALL_LIST,
  API_OPERATIONS_DATASOURCES_CANCEL,
  API_OPERATIONS_DATASOURCES_ADD,
  API_OPERATIONS_DATASOURCES_UPDATE,
  API_OPERATIONS_DATASOURCES_TEST,
  API_OPERATIONS_DATASOURCES_RELATED,
  API_OPERATIONS_DATASOURCES_BY_SPACEID,
  API_OPERATIONS_CLUSTERS_LIST,
  API_OPERATIONS_CLUSTERS_TYPE,
  API_OPERATIONS_CLUSTERS_CHECKCODE,
  API_OPERATIONS_CLUSTERS_CHECK_DEL,
  API_OPERATIONS_CLUSTERS,
  API_OPERATIONS_RESOURCE_GROUPS_LIST,
  API_OPERATIONS_RESOURCE_GROUPS_TEST,
  API_OPERATIONS_RESOURCE_GROUPS_CHECKCODE,
  API_OPERATIONS_RESOURCE_GROUPS_CHECK_DEL,
  API_OPERATIONS_RESOURCE_GROUPS_BY_SPACEID,
  API_OPERATIONS_RESOURCE_GROUPS,
  API_OPERATIONS_RESOURCE_GRANT_LIST,
  API_OPERATIONS_RESOURCE_GRANT_AUTH,
  API_OPERATIONS_BLACKLIST_LIST,
  API_OPERATIONS_BLACKLIST,
  API_OPERATIONS_BLACKLIST_CHECK,
  API_OPERATIONS_CUR_QUERY_LIST,
  API_OPERATIONS_CUR_QUERY,
  API_OPERATIONS_SOURCE_GROUP_LIST,
  API_OPERATIONS_SOURCE_GROUP_ADD,
  API_OPERATIONS_SOURCE_GROUP_EDIT,
  API_OPERATIONS_SOURCE_GROUP_DEL,
  API_OPERATIONS_SOURCE_GROUP_CHECK_REGEX,
  API_OPERATIONS_SOURCE_GROUP_CHECK_NAME,
  API_OPERATIONS_SOURCE_GROUP_CHECK_CODE,
  API_OPERATIONS_DYNAMIC_PARAM_LIST,
  API_OPERATIONS_DYNAMIC_PARAM_SET,
} from '@/server/api/operations';

export const useOperationsStore = defineStore('operations', {
  state: () => ({
    // 不分页的全部SR数据源
    allDataSources: [],
  }),
  getters: {},
  actions: {
    // 查询SR数据源列表
    fetchDatasourcesList(data) {
      return axios({
        url: API_OPERATIONS_DATASOURCES_LIST,
        method: 'post',
        data,
      });
    },
    // 查询所有SR数据源，不分页
    async fetchDatasourcesAllList() {
      const res = await axios({
        url: API_OPERATIONS_DATASOURCES_ALL_LIST,
        method: 'post',
        loading: false,
      });
      this.allDataSources = res.data || [];
      return res;
    },
    // 注销SR数据源
    fetchDatasourcesCancel(params, options = {}) {
      return axios({
        url: API_OPERATIONS_DATASOURCES_CANCEL,
        method: 'get',
        params,
        ...options,
      });
    },
    // 添加SR数据源
    fetchDatasourcesAdd(data) {
      return axios({
        url: API_OPERATIONS_DATASOURCES_ADD,
        method: 'post',
        data,
      });
    },
    // 编辑SR数据源
    fetchDatasourcesUpdate(data) {
      return axios({
        url: API_OPERATIONS_DATASOURCES_UPDATE,
        method: 'post',
        data,
      });
    },
    // 测试SR数据源连通性
    fetchDatasourcesTest(data) {
      return axios({
        url: API_OPERATIONS_DATASOURCES_TEST,
        method: 'post',
        data,
      });
    },
    // 查询关联的空间、工作流
    fetchDatasourcesRelated(params) {
      return axios({
        url: API_OPERATIONS_DATASOURCES_RELATED,
        method: 'get',
        params,
      });
    },
    // 通过空间ID获取SR数据源
    fetchDatasourcesBySpaceId(params) {
      return axios({
        url: API_OPERATIONS_DATASOURCES_BY_SPACEID,
        method: 'get',
        params,
        loading: false,
      });
    },

    // 查询资源群管理列表
    fetchClustersList(data, loading = true) {
      return axios({
        url: API_OPERATIONS_CLUSTERS_LIST,
        method: 'post',
        data,
        loading,
      });
    },
    // 查询资源群类型
    fetchClustersType(loading = true) {
      return axios({
        url: API_OPERATIONS_CLUSTERS_TYPE,
        method: 'get',
        loading,
      });
    },
    // 查询资源群code是否重复
    fetchClustersCheckCode(params) {
      return axios({
        url: API_OPERATIONS_CLUSTERS_CHECKCODE,
        method: 'get',
        params,
      });
    },
    // 查询资源群能否删除
    fetchClustersCheckDel(id) {
      return axios({
        url: API_OPERATIONS_CLUSTERS_CHECK_DEL(id),
        method: 'get',
      });
    },
    // 新增资源群
    fetchClustersPost(data) {
      return axios({
        url: API_OPERATIONS_CLUSTERS(),
        method: 'post',
        data,
      });
    },
    // 删除资源群
    fetchClustersDel(id) {
      return axios({
        url: API_OPERATIONS_CLUSTERS(id),
        method: 'delete',
      });
    },
    // 编辑资源群
    fetchClustersPut(data) {
      return axios({
        url: API_OPERATIONS_CLUSTERS(data.id),
        method: 'put',
        data,
      });
    },
    // 查询资源群
    fetchClustersGet(id) {
      return axios({
        url: API_OPERATIONS_CLUSTERS(id),
        method: 'get',
      });
    },

    // 查询资源划分管理列表
    fetchResourceGroupsList(data) {
      return axios({
        url: API_OPERATIONS_RESOURCE_GROUPS_LIST,
        method: 'post',
        data,
      });
    },
    // 测试资源划分的连通性
    fetchResourceGroupsTest(params) {
      return axios({
        url: API_OPERATIONS_RESOURCE_GROUPS_TEST,
        method: 'get',
        params,
      });
    },
    // 查询资源划分code是否重复
    fetchResourceGroupsCheckCode(params) {
      return axios({
        url: API_OPERATIONS_RESOURCE_GROUPS_CHECKCODE,
        method: 'get',
        params,
      });
    },
    // 查询资源划分能否删除
    fetchResourceGroupsCheckDel(id) {
      return axios({
        url: API_OPERATIONS_RESOURCE_GROUPS_CHECK_DEL(id),
        method: 'get',
      });
    },
    // 通过空间ID获取资源划分信息（资源划分中的，命名空间（集群））
    fetchResourceGroupsGetBySpaceId(params) {
      return axios({
        url: API_OPERATIONS_RESOURCE_GROUPS_BY_SPACEID,
        method: 'get',
        params,
      });
    },
    // 新增资源划分
    fetchResourceGroupsPost(data) {
      return axios({
        url: API_OPERATIONS_RESOURCE_GROUPS(),
        method: 'post',
        data,
      });
    },
    // 删除资源划分
    fetchResourceGroupsDel(id) {
      return axios({
        url: API_OPERATIONS_RESOURCE_GROUPS(id),
        method: 'delete',
      });
    },
    // 编辑资源划分
    fetchResourceGroupsPut(data) {
      return axios({
        url: API_OPERATIONS_RESOURCE_GROUPS(data.id),
        method: 'put',
        data,
      });
    },
    // 查询资源划分
    fetchResourceGroupsGet(id) {
      return axios({
        url: API_OPERATIONS_RESOURCE_GROUPS(id),
        method: 'get',
      });
    },

    // 按资源划分获取空间信息
    fetResourceGrantList(params) {
      return axios({
        url: API_OPERATIONS_RESOURCE_GRANT_LIST,
        method: 'get',
        params,
      });
    },
    // 授权
    fetchResourceGrantAuth(data) {
      return axios({
        url: API_OPERATIONS_RESOURCE_GRANT_AUTH,
        method: 'post',
        data,
      });
    },

    // 黑名单管理
    // 分页列表
    fetchBlacklistList(data) {
      return axios({
        url: API_OPERATIONS_BLACKLIST_LIST,
        method: 'post',
        data,
      });
    },
    // 新增
    fetchBlacklistAdd(data) {
      return axios({
        url: API_OPERATIONS_BLACKLIST(),
        method: 'post',
        data,
      });
    },
    // 删除
    fetchBlacklistDel(id) {
      return axios({
        url: API_OPERATIONS_BLACKLIST(id),
        method: 'delete',
      });
    },
    // 编辑
    fetchBlacklistEdit(data) {
      return axios({
        url: API_OPERATIONS_BLACKLIST(data.id),
        method: 'put',
        data,
      });
    },
    // 详情
    fetchBlacklistDetail(id) {
      return axios({
        url: API_OPERATIONS_BLACKLIST(id),
        method: 'get',
      });
    },
    // 测试正则
    fetchBlacklistCheck(data) {
      return axios({
        url: API_OPERATIONS_BLACKLIST_CHECK,
        method: 'post',
        data,
      });
    },

    // 当前执行SQL管理
    // 分页列表
    fetchCurQueryList(datasourceId, data) {
      return axios({
        url: API_OPERATIONS_CUR_QUERY_LIST(datasourceId),
        method: 'post',
        data,
      });
    },
    // 终止SQL执行
    fetchCurQueryStop(datasourceId, id) {
      return axios({
        url: API_OPERATIONS_CUR_QUERY(datasourceId, id),
        method: 'delete',
      });
    },

    // 资源组管理
    // 分页列表
    fetchSourceGroupList(datasourceId, data) {
      return axios({
        url: API_OPERATIONS_SOURCE_GROUP_LIST(datasourceId),
        method: 'post',
        data,
      });
    },
    // 新增
    fetchSourceGroupAdd(data) {
      return axios({
        url: API_OPERATIONS_SOURCE_GROUP_ADD(data.dataSourceId),
        method: 'post',
        data,
      });
    },
    // 编辑
    fetchSourceGroupEdit(data) {
      return axios({
        url: API_OPERATIONS_SOURCE_GROUP_EDIT(data.dataSourceId, data.code),
        method: 'put',
        data,
      });
    },
    // 查询
    fetchSourceGroupGet(datasourceId, code) {
      return axios({
        url: API_OPERATIONS_SOURCE_GROUP_EDIT(datasourceId, code),
        method: 'get',
      });
    },
    // 删除
    fetchSourceGroupDel(datasourceId, name) {
      return axios({
        url: API_OPERATIONS_SOURCE_GROUP_DEL(datasourceId, name),
        method: 'delete',
      });
    },
    // 校验正则
    fetchSourceGroupCheckRegex(datasourceId, data) {
      return axios({
        url: API_OPERATIONS_SOURCE_GROUP_CHECK_REGEX(datasourceId),
        method: 'post',
        data,
      });
    },
    // 校验名称
    fetchSourceGroupCheckName(datasourceId, name) {
      return axios({
        url: API_OPERATIONS_SOURCE_GROUP_CHECK_NAME(datasourceId, name),
        method: 'get',
        loading: false,
      });
    },
    // 校验标识
    fetchSourceGroupCheckCode(datasourceId, code) {
      return axios({
        url: API_OPERATIONS_SOURCE_GROUP_CHECK_CODE(datasourceId, code),
        method: 'get',
        loading: false,
      });
    },

    // 动态参数展示与修改
    // 分页列表
    fetchDynamicParamList(datasourceId, data) {
      return axios({
        url: API_OPERATIONS_DYNAMIC_PARAM_LIST(datasourceId),
        method: 'post',
        data,
      });
    },
    // 编辑
    fetchDynamicParamSet(datasourceId, data) {
      return axios({
        url: API_OPERATIONS_DYNAMIC_PARAM_SET(datasourceId),
        method: 'put',
        data,
      });
    },
  },
});

if (import.meta.webpackHot) {
  import.meta.webpackHot.accept(acceptHMRUpdate(useOperationsStore, import.meta.webpackHot));
}
