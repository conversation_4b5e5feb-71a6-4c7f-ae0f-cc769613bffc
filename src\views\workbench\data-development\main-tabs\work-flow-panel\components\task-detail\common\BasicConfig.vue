<script setup>
import { defineProps } from 'vue';
// import { InfoCircleIcon } from 'tdesign-icons-vue';
import BlockHeader from '@/components/BlockHeader.vue';
// import FormTable from '@/components/FormTable.vue';

defineProps({
  formData: {
    type: Object,
    required: true,
  },
});
// function modelValue(target, name, val) {
//   // eslint-disable-next-line no-param-reassign
//   target[name] = val;
// }
// const tableFormCol = [
//   {
//     colKey: 'name',
//     required: true,
//     title: '参数名',
//     rules: [{ required: true, message: '请输入参数名' }],
//     cell: (h, { row }) => (<t-input value={row.name} onChange={val => modelValue(row, 'name', val)}></t-input>),
//   },
//   {
//     colKey: 'relation',
//     title: '关系',
//     cell: (h, { row }) => (<t-select value={row.relation} onChange={val => modelValue(row, 'relation', val)}></t-select>),
//   },
//   {
//     colKey: 'type',
//     title: '类型',
//     cell: (h, { row }) => (<t-select value={row.type} onChange={val => modelValue(row, 'type', val)}></t-select>),
//   },
//   {
//     colKey: 'parameter',
//     title: '参数',
//     cell: (h, { row }) => (<t-input value={row.parameter} onChange={val => modelValue(row, 'parameter', val)}></t-input>),
//   },
// ];

// function handleAddRow() {
//   // eslint-disable-next-line vue/no-mutating-props
//   props.formData.params.push({
//     id: `${Date.now()}`,
//     name: '',
//     relation: '',
//     type: '',
//     parameter: '',
//   });
// }
</script>

<template>
  <!-- eslint-disable vue/no-mutating-props -->
  <section>
    <block-header title="基本配置" style="margin-bottom: 12px;" />
    <t-form-item label="节点名称" name="name">
      <t-input v-model="formData.name" placeholder="请输入" style="width: 374px;"></t-input>
    </t-form-item>
    <t-form-item label="节点标识" name="code">
      <!-- <section>
        <t-input v-model="formData.code" placeholder="请输入" style="width: 374px;"></t-input>
        <p style="margin-top: 10px;"><InfoCircleIcon />标识只能包含小写字母，数字和"-"，并且以字母开头，字母数字结尾</p>
      </section> -->
      <div>{{ formData.code }}</div>
    </t-form-item>
    <t-form-item label="描述" name="description">
      <t-textarea v-model="formData.description" placeholder="请输入描述内容" :maxlength="200" show-limit-number style="width: 374px;"></t-textarea>
    </t-form-item>
    <t-form-item label="失败重试次数" name="failRetryTimes">
      <t-input-number v-model="formData.failRetryTimes" placeholder="请输入" :min="0" :step="1" :decimalPlaces="0" theme="column" style="width: 374px;"></t-input-number>
    </t-form-item>
    <t-form-item label="失败重试间隔" name="failRetryInterval">
      <t-input-number v-model="formData.failRetryInterval" placeholder="请输入" :min="0" :step="1" :decimalPlaces="0" theme="column" style="width: 374px;">
        <template #suffix><span>分钟</span></template>
      </t-input-number>
    </t-form-item>
    <t-form-item label="延迟执行" name="delayTime">
      <t-input-number v-model="formData.delayTime" placeholder="请输入" :min="0" :step="1" :decimalPlaces="0" theme="column" style="width: 374px;">
        <template #suffix><span>分钟</span></template>
      </t-input-number>
    </t-form-item>
    <!-- <t-form-item label="自定义参数" name="params">
      <form-table style="width: 784px;" rowKey="key" :columns="tableFormCol" :data="formData.params" prefixFormItemName="params" :showRemove="true" :onAdd="handleAddRow"></form-table>
    </t-form-item> -->
  </section>
</template>
