export default function (mark, id = 'water-mark') {
  const width = 280;
  const height = 240;
  const content = document.querySelector(`#${id}`);
  const canvasEl = document.createElement('canvas');
  const divEl = content || document.createElement('div');
  divEl.setAttribute('id', id);
  divEl.style.position = 'absolute';
  divEl.style.left = '0';
  divEl.style.right = '0';
  divEl.style.top = '0';
  divEl.style.bottom = '0';
  divEl.style.pointerEvents = 'none';
  divEl.style.zIndex = '9999';
  canvasEl.width = width;
  canvasEl.height = height;
  canvasEl.style.fillStyle = '#F8F8F8';
  const canvas = canvasEl.getContext('2d');
  canvas.font = '26px Helvetica Neue Microsoft JhengHei';
  canvas.fillStyle = 'rgba(196,196,196,0.15)';
  canvas.textAlign = 'center';
  canvas.translate(width / 2, height / 2);
  const angle = -30;
  canvas.rotate(angle * Math.PI / 180);
  canvas.fillText(mark, 0, 0, 200);
  divEl.style.backgroundImage = `url(${canvasEl.toDataURL('image/png')})`;
  if (!content) {
    document.body.appendChild(divEl);
  }
}
