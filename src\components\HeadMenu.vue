<template>
  <div class="des-common-header">
    <header class="header-main header-inner">
      <div class="header-left">
        <slot name="header-left"></slot>
      </div>
      <div class="header-center">
        <slot name="header-center"></slot>
      </div>
      <div class="header-right">
        <slot name="header-right"></slot>
        <t-dropdown @click="handleCommand">
          <div class="header-avatar">
            <t-avatar :image="avatar">
              <img src="@/assets/avatar.png" />
            </t-avatar>
            <div>
              <i class="el-icon-arrow-down el-icon--right"></i>
            </div>
          </div>
          <t-dropdown-menu slot="dropdown" class="des-header__dropdown-menu">
            <t-dropdown-item value="logout">退出</t-dropdown-item>
          </t-dropdown-menu>
        </t-dropdown>
      </div>
    </header>
    <div class="header-bottom">
      <slot name="header-bottom"></slot>
    </div>
  </div>
</template>

<script>
// 页面顶部的菜单
export default {
  props: {
    avatar: {
      type: String,
    },
  },
  methods: {
    handleCommand(item) {
      if (item.value === 'logout') {
        this.$emit('logout');
      }
    },
  },
};
</script>

<style lang="less" scoped>
.des-common-header {
  background: linear-gradient(to right, #3464e0, #1890ff);
  .header-main {
    display: flex;
    height: 60px;
    justify-content: space-between;
  }
  .header-left {
    padding-left: 20px;
  }
  .header-center {
    flex: 1;
  }
  .header-right {
    display: flex;
    align-items: center;
    padding-right: 30px;
    color: #fff;
  }
  .header-avatar {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: #fff;
    &:hover {
      .el-icon-arrow-down {
        transform: rotate(180deg);
      }
    }
    .el-icon-arrow-down {
      transition: all ease-in-out 0.3s;
    }
  }
}
.des-header__dropdown-menu {
  min-width: 100px;
}
</style>
