<script setup>
import { ref, reactive, defineEmits, onMounted, onBeforeUnmount, watch, computed } from 'vue';
import { useRoute } from 'vue-router/composables';
import { AddIcon, ChevronDownIcon } from 'tdesign-icons-vue';
import { debounce, omit } from 'lodash';
import UserName from '@/components/UserName.vue';
import { useDataDevelopmentStore } from '@/stores/data-development';
import { showDialogConfirm } from '@/utils/dialog';
import { downloadFile, uuid, formatTime } from '@/utils/util';
import emitter, { EVENT_NAMES } from '@/utils/emitter';
import ReRecorded from './flow-define/ReRecorded.vue';
import FlowTimed from './flow-define/FlowTimed.vue';
import FlowVersion from './flow-define/FlowVersion.vue';
// import FlowApply from './flow-define/FlowApply.vue';
import FlowAuth from './flow-define/FlowAuth.vue';
import FlowImport from './flow-define/FlowImport.vue';
import FlowBatchAuth from './flow-define/FlowBatchAuth.vue';
import FlowBatchOperateResult from './flow-define/FlowBatchOperateResult';
import FlowBatchPublish from './flow-define/FlowBatchPublish';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue';
import { useSpacesStore } from '@/stores/spaces';
import { editFlow, viewFlow } from '../main-tabs/work-flow-panel/utils/flow';
import { hasAuth } from '../utils/auth';

const spacesStore = useSpacesStore();
const dataDevelopmentStore = useDataDevelopmentStore();
const reRecordedRef = ref(null);
const flowTimedRef = ref(null);
const flowVersionRef = ref(null);
// const flowApplyRef = ref(null);
const flowAuthRef = ref(null);
const flowImportRef = ref(null);
const flowBatchAuthRef = ref(null);
const flowBatchOperateResultRef = ref(null);
const flowBatchPublishRef = ref(null);
const emit = defineEmits(['switch']);

const route = useRoute();

const formData = reactive({
  searchType: 'PER',
  releaseState: null,
  scheduleState: null,
  publishState: null,
  queryKey: '',
  code: null,
});

function getOperationMoreOptions(row) {
  return [
    { content: '运行', value: 'Run', disabled: row.releaseState !== 'ONLINE' || !hasAuth(row, 'RUN') },
    { content: '发布', value: 'Publish', disabled: (row.publishState === 'PUBLISHED' && row.releaseState === 'ONLINE') || !hasAuth(row, 'RUN') },
    { content: '下线', value: 'Offline', divider: true, disabled: ['OFFLINE', null].includes(row.releaseState) || !hasAuth(row, 'RUN') },
    { content: '补录', value: 'Re-recorded', disabled: row.releaseState !== 'ONLINE' || !hasAuth(row, 'RUN') },
    // { content: '导出', value: 'Export', divider: true },
    { content: '授权', value: 'Grant', disabled: !hasAuth(row, 'GRANT') },
    { content: '定时管理', value: 'Timed', disabled: row.releaseState !== 'ONLINE' || !hasAuth(row, 'EDIT') },
    { content: '历史版本', value: 'Version', disabled: !hasAuth(row, 'EDIT'), divider: true },
    { content: '删除', value: 'Delete', theme: 'error', disabled: row.releaseState === 'ONLINE' || !hasAuth(row, 'EDIT') },
  ];
}
function stateCell(state) {
  const stateMap = {
    ONLINE: '已上线',
    OFFLINE: '已下线',
  };
  const text = stateMap[state];
  const className = state === 'ONLINE' ? 'des-green-dot' : 'des-orange-dot';
  if (text) {
    return <div class={className}>{text}</div>;
  }
  return <div>-</div>;
}
function publishStateCell(state) {
  const stateMap = {
    UNPUBLISHED: '待发布',
    PUBLISHED: '已发布',
    DRAFT: '草稿中',
  };
  const text = stateMap[state];
  const className = state === 'PUBLISHED' ? 'des-green-dot' : 'des-blue-dot';
  if (text) {
    return <div class={className}>{text}</div>;
  }
  return <div>-</div>;
}
const colums = ref([
  { colKey: 'row-select', type: 'multiple', width: 60, fixed: 'left', align: 'center' },
  { title: '序号', colKey: 'serial-number', width: 60, fixed: 'left', align: 'center' },
  { title: '工作流标识', colKey: 'code', width: 250 },
  { title: '工作流名称', colKey: 'name', width: 250, cell: (h, { row }) => (<t-link hover="color" theme="primary" onClick={() => handleView(row)} disabled={!hasAuth(row, 'READ')}>{row.name}</t-link>) },
  { title: '版本', colKey: 'version', width: 80, cell: (h, { row }) => `V${row.version}` },
  { title: '在线状态', colKey: 'releaseState', width: 95, cell: (h, { row }) => stateCell(row.releaseState) },
  { title: '定时状态', colKey: 'scheduleReleaseState', width: 93, cell: (h, { row }) => stateCell(row.scheduleReleaseState) },
  { title: 'Crontab', colKey: 'crontab', width: 105, ellipsis: true },
  { title: '发布状态', colKey: 'publishState', width: 95, cell: (h, { row }) => publishStateCell(row.publishState) },
  {
    title: '创建人',
    colKey: 'createUser',
    width: 198,
    cell: (h, { row }) => <UserName fullName={row.createUser}></UserName>,
  },
  { title: '创建时间', colKey: 'createTime', width: 198, cell: (h, { row }) => formatTime(row.createTime) },
  {
    title: '修改人',
    colKey: 'updateUser',
    width: 198,
    cell: (h, { row }) => <UserName fullName={row.updateUser}></UserName>,
  },
  { title: '修改时间', colKey: 'updateTime', width: 198, cell: (h, { row }) => formatTime(row.updateTime) },
  {
    title: '发布人',
    colKey: 'publishUser',
    width: 198,
    cell: (h, { row }) => <UserName fullName={row.publishUser}></UserName>,
  },
  { title: '发布时间', colKey: 'publishTime', width: 198, cell: (h, { row }) => formatTime(row.publishTime) },
  { title: '描述', colKey: 'description', width: 200, ellipsis: true },
  {
    title: '操作',
    colKey: 'operation',
    width: 190,
    fixed: 'right',
    cell: (h, { row }) => (
      <t-space size={16}>
        {/* <t-link hover="color" theme="primary" onClick={() => handleApprove(row)}>申请权限</t-link> */}
        <t-link hover="color" theme="primary" onClick={() => handleEdit(row)} disabled={!hasAuth(row, 'EDIT')}>编辑</t-link>
        <t-link hover="color" theme="primary" onClick={() => handleFlowInstance(row)}>查看实例</t-link>
        <t-dropdown options={getOperationMoreOptions(row)} placement="bottom-right" onClick={option => handleCommand(option, row)} trigger="click">
          <t-link hover="color" theme="primary">更多<des-icon name="des-icon-line-jiantou-xia" size="14px"></des-icon></t-link>
        </t-dropdown>
      </t-space>
    ),
  },
]);
function handleCommand(option, row) {
  // 补录
  if (option.value === 'Re-recorded') {
    reRecordedRef.value.open(row);
  }
  // 定时管理
  if (option.value === 'Timed') {
    flowTimedRef.value.open(row);
  }
  // 历史版本
  if (option.value === 'Version') {
    flowVersionRef.value.open({
      processCode: row.code,
      processId: row.id,
    });
  }
  // 导出
  if (option.value === 'Export') {
    console.log('导出');
  }
  // 授权
  if (option.value === 'Grant') {
    console.log('授权');
    flowAuthRef.value.open(row);
  }
  if (option.value === 'Publish') {
    console.log('发布');
    dataDevelopmentStore.publishFlowDefine(route.query.spaceId, row.code).then(() => {
      MessagePlugin.success('操作成功');
      resetChoosedData();
      fetchData();
    });
  }
  if (option.value === 'Offline') {
    console.log('下线');
    dataDevelopmentStore.offlineFlowDefine(route.query.spaceId, row.code).then(() => {
      MessagePlugin.success('操作成功');
      resetChoosedData();
      fetchData();
    });
  }
  if (option.value === 'Run') {
    console.log('运行');
    runFlow(row);
  }
  if (option.value === 'Delete') {
    removeFlow(row);
  }
}
function handleFlowInstance(row) {
  console.log('查看实例');
  emit('switch', 'FlowInstance', {});
  emitter.emit(EVENT_NAMES.DATA_DEVELOPMENT.REFRESH_FLOW_INSTANCE, { jobNames: [row.code] });
}
async function runFlow(item) {
  const result = await showDialogConfirm({
    title: `确定运行工作流「${item.name}」吗？`,
    width: '440px',
  });
  if (result.type === 'success') {
    dataDevelopmentStore.runFlowDefine(route.query.spaceId, item.code).then(() => {
      MessagePlugin.success('操作成功');
    });
  };
}
async function removeFlow(item) {
  const result = await showDialogConfirm({
    title: `确定删除工作流「${item.name}」吗？`,
    body: () => (
      <span>
        删除后对应工作流实例将同步删除，且数据<b class="des-common-warn-text">无法恢复</b>
        ，请谨慎操作。
      </span>
    ),
    width: '440px',
  });
  if (result.type === 'success') {
    console.log('删除请求');
    await dataDevelopmentStore.deleteFlowDefine(route.query.spaceId, item.code);
    MessagePlugin.success('操作成功');
    if (pagination.current > 1 && flowDataList.value.length === 1) {
      pagination.current = pagination.current - 1;
    }
    fetchData();
  }
}
function handleResetQuery() {
  formData.searchType = 'PER';
  formData.releaseState = null;
  formData.scheduleState = null;
  formData.publishState = null;
  formData.queryKey = '';
  formData.code = null;
  pagination.current = 1;
  fetchData();
}
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 10,
  showJumper: true,
  onChange: (pageInfo) => {
    pagination.current = pageInfo.current;
    if (pagination.pageSize !== pageInfo.pageSize) {
      pagination.page = 1;
    }
    pagination.pageSize = pageInfo.pageSize;
    fetchData();
  },
});
const releaseOptions = ref([
  { label: '全部在线状态', value: null },
  { label: '已上线', value: 'ONLINE ' },
  { label: '已下线', value: 'OFFLINE' },
  { label: '未知（-）', value: 'INIT' },
]);
const scheduleOptions = ref([
  { label: '全部定时状态', value: null },
  { label: '已上线', value: 'ONLINE ' },
  { label: '已下线', value: 'OFFLINE' },
  { label: '未知（-）', value: 'INIT' },
]);
const publishStateOptions = ref([
  { label: '全部发布状态', value: null },
  { label: '待发布', value: 'UNPUBLISHED' },
  { label: '已发布', value: 'PUBLISHED' },
  { label: '草稿中', value: 'DRAFT' },
  { label: '未知（-）', value: 'INIT' },
]);
const flowDataList = ref([]);
async function fetchData() {
  if (!route.query.spaceId) return;
  const { data } = await dataDevelopmentStore.fetchFlowDefineList(route.query.spaceId, {
    queryData: {
      spaceId: route.query.spaceId,
      searchType: formData.searchType,
      queryKey: formData.queryKey,
      releaseState: formData.releaseState,
      scheduleState: formData.scheduleState,
      publishState: formData.publishState,
      code: formData.code,
    },
    pageNum: pagination.current,
    pageSize: pagination.pageSize,
  });
  flowDataList.value = data.list;
  pagination.total = data.total;
}
function handleSearchTypeChange() {
  selectedRowCodes.value = [];
  onSearch();
}
const onSearch = debounce((query) => {
  pagination.current = 1;
  fetchData(query);
}, 500);

watch(
  () => route.query.spaceId,
  (val) => {
    if (val) {
      pagination.current = 1;
      pagination.pageSize = 10;
      fetchData();
      resetChoosedData();
    }
  },
);

function setFormData(data) {
  Object.assign(formData, data);
}

function handleRefreshFlowDefine(data = {}) {
  if (Object.keys(data).length > 0) {
    setFormData({
      searchType: 'PER',
      releaseState: null,
      scheduleState: null,
      publishState: null,
      queryKey: '',
      ...data,
    });
  }
  fetchData();
}

function handleAddFlow() {
  const value = `AddFlow_${uuid()}`;
  const newTab = {
    label: '新增工作流',
    value,
    component: 'WorkFlowPanel',
    removable: true,
    childPanel: 'FlowDefine', // 子面板
    closeReminder: true, // 关闭提醒
    props: {
      name: value,
    },
  };
  dataDevelopmentStore.addTabPanelList(newTab);
  dataDevelopmentStore.setCurrentTabValue(newTab.value);
}
// function handleApprove(row) {
//   console.log('申请权限', row);
//   flowApplyRef.value.open(row);
// }
async function handleEdit(row) {
  console.log('编辑', row);
  editFlow(row, route.query.spaceId);
}
function handleView(row) {
  console.log('查看', row);
  viewFlow(row);
}
function handleImport() {
  console.log('导入工作流');
  flowImportRef.value.open();
}

const selectedRowCodes = ref([]);
const selectedRowNames = ref([]);
const selectedData = ref([]);
function rehandleSelectChange(codeList, { selectedRowData }) {
  selectedRowCodes.value = codeList;
  selectedRowNames.value = selectedRowData.map(item => item.name);
  selectedData.value = selectedRowData;
}
async function handleBatchPublish() {
  if (selectedRowCodes.value.length === 0) {
    MessagePlugin.warning('请选择工作流');
    return;
  }
  const result = await showDialogConfirm({
    title: '是否确认批量发布以下工作流',
    body: () => (
      <div style="max-height: 220px; overflow-y: auto; padding-left: 26px;">
        <ul>
          { selectedRowNames.value.map((name, index) => <li key={ index }>{ index + 1 }、{ name }</li>) }
        </ul>
      </div>
    ),
    width: '440px',
  });
  if (result.type === 'cancel') {
    return;
  }
  const postData = selectedData.value.map(item => ({ code: item.code, name: item.name }));
  const { data } = await dataDevelopmentStore.batchPublishFlowDefine(route.query.spaceId, postData);
  // data是一个批标识
  flowBatchPublishRef.value.open(data);
  resetChoosedData();
}
async function handleBatchOffline() {
  if (selectedRowCodes.value.length === 0) {
    MessagePlugin.warning('请选择工作流');
    return;
  }
  const result = await showDialogConfirm({
    title: '是否确认批量下线以下工作流',
    body: () => (
      <div style="max-height: 220px; overflow-y: auto; padding-left: 26px;">
        <ul>
          { selectedRowNames.value.map((name, index) => <li key={ index }>{ index + 1 }、{ name }</li>) }
        </ul>
      </div>
    ),
    width: '440px',
  });
  if (result.type === 'cancel') {
    return;
  }
  const { data } = await dataDevelopmentStore.batchOfflineFlowDefine(route.query.spaceId, selectedRowCodes.value);
  flowBatchOperateResultRef.value.open(data, 'batchOffline');
  fetchData();
  resetChoosedData();
  MessagePlugin.success('操作成功');
}
function resetChoosedData() {
  selectedRowCodes.value = [];
  selectedRowNames.value = [];
  selectedData.value = [];
}
function handleBatchAuth() {
  if (selectedRowCodes.value.length === 0) {
    MessagePlugin.warning('请选择工作流');
    return;
  }
  flowBatchAuthRef.value.open({
    codeList: selectedRowCodes.value,
    nameList: selectedRowNames.value,
  });
}
// 创建进度条弹窗
function createProgressDialog(title) {
  return DialogPlugin.alert({
    theme: 'info',
    header: title,
    body: () => <t-progress percentage={0} />,
    className: 'des-common-dialog-plugin',
    confirmBtn: null,
    cancelBtn: null,
    closeBtn: false,
    width: 440,
  });
}
// 更新进度条
function updateProgressDialog(dialog, current, total) {
  const percentage = Math.floor((current / total) * 100);
  dialog.update({
    body: () => <t-progress percentage={percentage} />,
  });
}
function getExportData(data) {
  // 公共属性
  const props = ['id', 'createUser', 'updateUser', 'createTime', 'updateTime', 'spaceId', 'flag'];
  const exportData = {
    ...omit(data, [...props, 'releaseState', 'publishState', 'scheduleReleaseState', 'permissionList', 'crontab', 'codeList', 'alertChannels', 'alertConfigId']),
    taskDefinitions: data.taskDefinitions.map(item => ({
      ...omit(item, [...props, 'processDefinitionCode', 'processDefinitionId', 'resourceIds', 'minCpuCores', 'minMemorySpace', 'resourceGroupId', 'taskInstanceDTO']),
    })),
    taskRelations: data.taskRelations.map(item => ({
      ...omit(item, ['id', 'spaceId', 'processDefinitionId']),
    })),
  };
  return exportData;
}
async function handleBatchExport() {
  if (selectedRowCodes.value.length === 0) {
    MessagePlugin.warning('请选择工作流');
    return;
  }
  // 创建进度条弹窗
  const progressDialog = createProgressDialog('工作流导出中');
  // 用于跟踪当前处理项的索引
  let currentIndex = 0;
  // 用于存储导出结果
  const exportArray = [];
  for (const item of selectedData.value) {
    try {
      const { data } = await dataDevelopmentStore.fetchFlowDefineDetail(route.query.spaceId, item.code, { loading: false, showErrorMsg: false });
      const exportData = getExportData(data);
      // 将数据转为JSON字符串并创建Blob对象
      const jsonStr = JSON.stringify(exportData, null, 2);
      const blob = new Blob([jsonStr], { type: 'application/json' });
      // 创建下载链接
      const url = URL.createObjectURL(blob);
      downloadFile(url, `${data.name}.json`);
      exportArray.push({ processName: item.name, processCode: item.code, isSuccess: true, errMsg: '' });
    } catch (error) {
      exportArray.push({ processName: item.name, processCode: item.code, isSuccess: false, errMsg: error.msg });
    }
    // eslint-disable-next-line no-plusplus
    updateProgressDialog(progressDialog, ++currentIndex, selectedRowCodes.value.length);
    // 添加延迟，避免进度条更新太快
    await new Promise(resolve => setTimeout(resolve, 200));
  }
  resetChoosedData();
  // 关闭进度条弹窗
  progressDialog.destroy();
  flowBatchOperateResultRef.value.open(exportArray, 'batchExport');

  const successArray = exportArray.filter(item => item.isSuccess);
  successArray.length && MessagePlugin.success(`导出成功，共导出${successArray.length}条工作流`);
}

const operateOptions = computed(() => {
  const options = [
    { content: '批量发布', value: 'batchPublish' },
    { content: '批量下线', value: 'batchOffline' },
    { content: '批量导出', value: 'batchExport' },
  ];
  if (spacesStore.isSpaceOwner) {
    options.push({ content: '批量授权', value: 'batchGrant' });
  }
  return options;
});

function handleBatchOperate(option) {
  if (option.value === 'batchPublish') {
    handleBatchPublish();
  }
  if (option.value === 'batchOffline') {
    handleBatchOffline();
  }
  if (option.value === 'batchExport') {
    handleBatchExport();
  }
  if (option.value === 'batchGrant') {
    handleBatchAuth();
  }
}

onMounted(() => {
  fetchData();
  emitter.on(EVENT_NAMES.DATA_DEVELOPMENT.REFRESH_FLOW_DEFINE, handleRefreshFlowDefine);
});
onBeforeUnmount(() => {
  emitter.off(EVENT_NAMES.DATA_DEVELOPMENT.REFRESH_FLOW_DEFINE, handleRefreshFlowDefine);
});
</script>

<template>
  <div class="page-development__flow-dfeine">
    <div class="search-wrap">
      <t-space :size="12">
        <t-radio-group v-model="formData.searchType" variant="default-filled" @change="handleSearchTypeChange">
          <t-radio-button value="PER">我的</t-radio-button>
          <t-radio-button value="ALL">全部</t-radio-button>
        </t-radio-group>
        <!-- 工作流状态 -->
        <t-select v-model="formData.releaseState" style="width: 134px;" :options="releaseOptions" @change="onSearch"></t-select>
        <!-- 定时状态（调度状态） -->
        <t-select v-model="formData.scheduleState" style="width: 134px;" :options="scheduleOptions" @change="onSearch"></t-select>
        <!-- 发布状态 -->
        <t-select v-model="formData.publishState" style="width: 134px;" :options="publishStateOptions" @change="onSearch"></t-select>
        <t-input v-model.trim="formData.code" style="width: 173px;" @change="onSearch" clearable placeholder="工作流标识"></t-input>
        <t-input v-model.trim="formData.queryKey" style="width: 173px;" @change="onSearch" clearable placeholder="标识/名称/修改人/描述"></t-input>
        <t-button theme="default" variant="outline" @click="handleResetQuery">重置</t-button>
      </t-space>
      <t-space :size="12">
        <t-button variant="outline" @click="handleImport">导入工作流</t-button>
        <t-button @click="handleAddFlow"><add-icon slot="icon" />新增工作流</t-button>
        <t-dropdown :options="operateOptions" @click="handleBatchOperate">
          <t-button theme="default" variant="outline">
            批量操作
            <chevron-down-icon size="16" slot="suffix" />
          </t-button>
        </t-dropdown>
      </t-space>
    </div>
    <t-table row-key="code" :data="flowDataList" :columns="colums" :pagination="pagination" :selected-row-keys="selectedRowCodes" @select-change="rehandleSelectChange" max-height="100%" />
    <!-- 补录 -->
    <re-recorded ref="reRecordedRef" :spaceId="route.query.spaceId"></re-recorded>
    <!-- 定时管理 -->
    <flow-timed ref="flowTimedRef" :spaceId="route.query.spaceId" @refresh="fetchData"></flow-timed>
    <!-- 历史版本 -->
    <flow-version ref="flowVersionRef" :spaceId="route.query.spaceId" @refresh="fetchData"></flow-version>
    <!-- 申请权限 -->
    <!-- <flow-apply ref="flowApplyRef"></flow-apply> -->
    <!-- 授权 -->
    <flow-auth ref="flowAuthRef" :spaceId="route.query.spaceId" @refresh="fetchData"></flow-auth>
    <!-- 导入工作流 -->
    <flow-import ref="flowImportRef" @refresh="fetchData"></flow-import>
    <!-- 批量授权 -->
    <flow-batch-auth ref="flowBatchAuthRef" :spaceId="route.query.spaceId" @refresh="fetchData" @reset="resetChoosedData"></flow-batch-auth>
    <!-- 批量操作的结果 -->
    <flow-batch-operate-result ref="flowBatchOperateResultRef"></flow-batch-operate-result>
    <!-- 批量发布工作流 -->
    <flow-batch-publish ref='flowBatchPublishRef' @refresh="fetchData"></flow-batch-publish>
  </div>
</template>

<style lang="less" scoped>
.page-development__flow-dfeine {
  height: 100%;
  .search-wrap {
    display: flex;
    justify-content: space-between;
    padding: 16px 0;
    flex-wrap: wrap;
    // 左侧搜索部分
    .t-space:first-child {
      flex-wrap: wrap;
      flex: 1;
      min-width: 0;
    }
    // 右侧按钮部分
    .t-space:last-child {
      flex-wrap: nowrap;
    }
    :deep(.t-space-item) {
      height: 32px;
    }
  }
  :deep(.t-table) {
    height: calc(100% - 108px);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
}
</style>
