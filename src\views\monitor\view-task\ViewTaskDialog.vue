<template>
  <BasicDialog
    width="1240"
    :visible.sync="visible"
    :showConfirmButton="false"
    header="查看"
    :onClosed="onClose"
    @handleClose="onClose"
  >
    <BlockHeader title="基本信息"></BlockHeader>
    <div class="view-task-detail-info">
      <t-row :gutter="[12, 12]">
        <t-col class="des-flex-align-center" :span="4" ><span class="label">物化视图</span><span class="content">202502250002</span></t-col>
        <t-col class="des-flex-align-center" :span="4" ><span class="label">QUERY_ID</span ><span class="content">916619319350666</span></t-col>
        <t-col class="des-flex-align-center" :span="4" ><span class="label">任务名称</span ><span class="content">sr视图任务1</span></t-col>
        <t-col class="des-flex-align-center" :span="4" ><span class="label">创建时间</span><span class="content">2025-01-06 19:19:41</span></t-col>
        <t-col class="des-flex-align-center" :span="4" ><span class="label">结束时间</span><span class="content">2025-01-06 19:19:41</span></t-col>
        <t-col class="des-flex-align-center" :span="4" ><span class="label">CATALOG</span><span class="content">information_schema</span></t-col>
        <t-col class="des-flex-align-center" :span="4" ><span class="label">数据库</span><span class="content">EOF</span></t-col>
        <t-col class="des-flex-align-center" :span="4" ><span class="label">SQL定义</span><span class="content">0</span></t-col>
        <t-col class="des-flex-align-center" :span="4" ><span class="label">过期时间</span><span class="content">2025-01-06 19:19:41</span></t-col>
        <t-col class="des-flex-align-center" :span="4" ><span class="label">TASK_NAME</span><span class="content">视图1</span></t-col>
        <t-col class="des-flex-align-center" :span="4" ><span class="label">错误码</span><span class="content">0</span></t-col>
        <t-col class="des-flex-align-center" :span="4" ><span class="label">错误信息</span><span class="content">0</span></t-col>
        <t-col class="des-flex-align-center" :span="4" ><span class="label">任务进度</span><span class="content">完成</span></t-col>
        <t-col class="des-flex-align-center" :span="4" ><span class="label">额外信息</span><span class="content">96231</span></t-col>
      </t-row>
    </div>
  </BasicDialog>
</template>

<script setup>
import { defineExpose } from 'vue';
import { useDialog } from '@/views/operations/hooks';
import BasicDialog from '@/components/BasicDialog.vue';
import BlockHeader from '@/components/BlockHeader.vue';

// 弹窗数据
const { visible, openAddDialog, openEditDialog, onClose } = useDialog();

defineExpose({ openAddDialog, openEditDialog });
</script>

<style lang="less" scoped>
.view-task-detail-info {
  margin-top: 12px;
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  .label {
    display: inline-block;
    width: 134px;
    margin-right: 8px;
    color: #999999;
    text-align: right;
  }
  .content {
    display: inline-block;
    color: #333333;
    width: calc(~"100% - 142px");
  }
}
</style>
