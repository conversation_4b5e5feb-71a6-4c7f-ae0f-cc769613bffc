<script setup>
// 页面内容的头部，由标题和一个返回按钮组成。
import { defineProps } from 'vue';

const props = defineProps({
  title: String,
  showIcon: <PERSON>ole<PERSON>,
  back: Function,
});
function handleBack() {
  if (props.back) {
    props.back();
  }
}
</script>

 <template>
  <div class="des-common-page-header">
    <t-button class="des-common-header__back-icon" theme="default" variant="outline" shape="square" v-if="showIcon" @click="handleBack">
      <t-icon name="chevron-left" size="16" />
    </t-button>
    <span class="des-common-header__title">{{ title }}</span>
  </div>
 </template>

 <style lang="less" scoped>
 .des-common-page-header {
  display: flex;
  padding: 10px 20px;
  align-items: center;
  background-color: #ffffff;
  height: 48px;
  border-radius: 6px 6px 0 0;
 }
 .des-common-header__back-icon {
  margin-right: 12px;
  width: 28px;
  height: 28px;
 }
 .des-common-header__title {
  color: #333333;
  font-family: "PingFang SC";
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 24px;
 }
</style>
