import { ref, inject } from 'vue';
import { createEdge } from '@/utils/graph';

export function usePreTaskHooks() {
  const nodeList = inject('nodeList');
  const graph = inject('graph');
  // id是当前节点的id
  const preTaskIds = ref([]);
  function findCurrentNode(id) {
    return nodeList.value.find(item => item.value === id);
  }
  function getEdges() {
    return graph.value.getEdges().map(edge => ({
      id: edge.id,
      source: edge.getSourceNode().id,
      target: edge.getTargetNode().id,
    }));
  }
  function findEdgesByCurrentId(id) {
    const edges = getEdges();
    return edges.filter(edge => edge.target === id);
  }
  // 从连线中找到前置节点
  function findPreTask(id) {
    const currentNode = findCurrentNode(id);
    if (!currentNode) return;
    const edges = getEdges();
    return edges.filter(edge => edge.target === currentNode.value).map(edge => edge.source);
  }
  // 根据前置节点id连线
  function linkPreTask(id) {
    // 先删除所有，再重画
    const edges = findEdgesByCurrentId(id).map(edge => edge.id);
    if (edges.length) {
      graph.value.removeCells(edges);
    }
    const currentNode = findCurrentNode(id);
    preTaskIds.value.forEach((item) => {
      createEdge(item, currentNode.value, graph.value);
    });
  }
  // 同步前置节点id
  function syncPreTaskId(id) {
    preTaskIds.value = findPreTask(id);
  }
  return {
    nodeList,
    preTaskIds,
    syncPreTaskId,
    findPreTask,
    linkPreTask,
  };
}
