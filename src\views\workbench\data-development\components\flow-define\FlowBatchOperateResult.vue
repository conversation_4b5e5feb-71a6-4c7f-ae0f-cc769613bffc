<script setup>
import { ref, defineExpose, computed } from 'vue';
import BasicDialog from '@/components/BasicDialog.vue';
import BlockHeader from '@/components/BlockHeader.vue';

const visible = ref(false);

const type = ref('');

// 批量发布结果 批量下线结果
const header = computed(() => ({
  batchOffline: '批量下线结果',
  batchExport: '批量导出结果',
}[type.value]));

const tableData = ref([]);

const columns = ref([
  { colKey: 'processName', title: '工作流名称', width: 173, ellipsis: true },
  { colKey: 'processCode', title: '工作流标识', width: 173, ellipsis: true },
  { colKey: 'isSuccess', title: '操作状态', width: 100, cell: (h, { row }) =>  (row.isSuccess ? '成功' : '失败') },
  { colKey: 'errMsg', title: '操作信息', width: 173, ellipsis: true },
]);

function handleClose() {
  close();
}

function open(data, headerType) {
  visible.value = true;
  tableData.value = data;
  type.value = headerType;
}
function close() {
  visible.value = false;
}
defineExpose({ open, close });
</script>

<template>
  <BasicDialog
    width="957"
    :visible.sync="visible"
    :header="header"
    :footer="null"
    :showFooter="null"
    @handleClose="handleClose"
  >
    <BlockHeader title="变更的工作流" style="margin-bottom: 16px;" v-if="type !== 'batchExport'"></BlockHeader>

    <t-table row-key="id" :data="tableData" :columns="columns" maxHeight="500" />
  </BasicDialog>
</template>

<style lang="less" scoped>
</style>
