<script setup>
import { reactive, ref, defineExpose, watch, defineProps } from 'vue';
import BlockHeader from '@/components/BlockHeader.vue';
import BasicConfig from './common/BasicConfig.vue';
import ResourceConfig from './common/ResourceConfig.vue';
import AlarmConfig from './common/AlarmConfig.vue';
import PreTask from './common/PreTask.vue';
import GitFile from './common/GitFile.vue';
import { STARROCKS_SQL } from '../../utils/task-type.js';
import { srSqlRules } from '../../utils/rules.js';
import BasicSqlEditor from '@/components/BasicSqlEditor.vue';
import { useOperationsStore } from '@/stores/operations';
import { useRoute } from 'vue-router/composables';

defineProps({
  currentFlowName: {
    type: String,
    default: '',
  },
  readonly: {
    type: Boolean,
    default: false,
  },
});
const route = useRoute();
const formRef = ref(null);
const formData = reactive({
  taskType: STARROCKS_SQL,
  id: '',
  // 节点名称
  name: '',
  // 节点标识
  code: '',
  // 节点描述
  description: '',
  // 失败重试次数
  failRetryTimes: 0,
  // 失败重试间隔
  failRetryInterval: 0,
  // 延迟执行
  delayTime: 0,
  // 自定义参数
  // params: [
  //   { key: '1', name: '参数1', relation: 'in', type: 'string', parameter: 'value' },
  // ],
  // 运行集群
  resourceGroupId: '',
  // cpu
  minCpuCores: 0.5,
  // 内存
  minMemorySpace: 512,
  // 超时告警开关
  timeoutFlag: false,
  // 超时时长
  timeout: 0,
  // 超时是否失败
  timeoutFailed: false,
  taskProperties: {
    // starrocks集群
    srDataSourceId: '',
    // 脚本来源
    scriptType: '',
    // SQL语句
    scriptContent: '',
    // 项目
    gitProjectId: '',
    // 文件
    gitFile: '',
  },
});
const nodeId = ref('');

function getData() {
  return {
    taskType: formData.taskType,
    id: formData.id,
    name: formData.name,
    code: formData.code,
    description: formData.description,
    failRetryTimes: formData.failRetryTimes,
    failRetryInterval: formData.failRetryInterval,
    delayTime: formData.delayTime,
    resourceGroupId: formData.resourceGroupId,
    minCpuCores: formData.minCpuCores,
    minMemorySpace: formData.minMemorySpace,
    timeoutFlag: formData.timeoutFlag,
    timeout: formData.timeout,
    timeoutFailed: formData.timeoutFailed,
    taskProperties: {
      srDataSourceId: formData.taskProperties.srDataSourceId,
      scriptType: formData.taskProperties.scriptType,
      scriptContent: formData.taskProperties.scriptContent,
      gitProjectId: formData.taskProperties.gitProjectId,
      gitFile: formData.taskProperties.gitFile,
    },
  };
}
const scriptTypeOptions = ref([
  { label: '手工编写', value: 'CONTENT' },
  { label: 'GIT项目', value: 'GIT_FILE' },
]);

const operationsStore = useOperationsStore();
// 集群
const srDataSourceOptions = ref([]);
async function initSrDataSourceOptions() {
  const res = await operationsStore.fetchDatasourcesBySpaceId({ spaceId: route.query.spaceId });
  srDataSourceOptions.value = res.data.map(item => ({
    label: item.name,
    value: item.id,
  }));
}
watch(() => route.query.spaceId, () => {
  initSrDataSourceOptions();
}, { immediate: true });
// function handleDebug() {
//   console.log('调试SQL 跳转到SQL工作台');
// }
async function save() {
  const result = await formRef.value.validate();
  if (result !== true) {
    return;
  }
  const data = getData();
  return {
    nodeId: nodeId.value,
    data,
  };
}
// function reset() {
//   formRef.value.reset();
// }
function setData(nodeid, data) {
  nodeId.value = nodeid;
  Object.keys(data).forEach((key) => {
    formData[key] = data[key];
  });
}

defineExpose({
  setData,
  // reset,
  save,
});
</script>

<template>
  <div class="des-workflow-job-form__starrocks-materialized-view">
    <t-form :data="formData" ref="formRef" label-width="117px" :rules="srSqlRules" :disabled="readonly">
      <basic-config :formData="formData"></basic-config>
      <resource-config :formData="formData"></resource-config>
      <alarm-config :formData="formData"></alarm-config>
      <section>
        <block-header title="更多设置" style="margin-bottom: 12px;" />
        <t-form-item label="Starrocks集群" name="taskProperties.srDataSourceId">
          <t-select v-model="formData.taskProperties.srDataSourceId" placeholder="请选择" style="width: 374px;" :options="srDataSourceOptions"></t-select>
        </t-form-item>
        <t-form-item label="脚本来源" name="taskProperties.scriptType">
          <t-select v-model="formData.taskProperties.scriptType" placeholder="请选择" style="width: 374px;" :options="scriptTypeOptions"></t-select>
        </t-form-item>
        <div style="padding-left:117px;" v-if="formData.taskProperties.scriptType === 'CONTENT'">
          <basic-sql-editor v-model="formData.taskProperties.scriptContent" title="SQL语句" style="height:280px;" :readOnly="readonly">
            <!-- <t-link hover="color" theme="primary" @click="handleDebug"><des-icon name="des-icon-daimacod2" size="16px" style="margin-right: 2px;"></des-icon>SQL调试</t-link> -->
          </basic-sql-editor>
        </div>
        <template v-if="formData.taskProperties.scriptType === 'GIT_FILE'">
          <git-file :formData="formData"></git-file>
        </template>
      </section>
      <section>
        <block-header title="任务关联" style="margin-bottom: 12px;" />
        <!-- 前置任务 -->
        <pre-task :currentId="formData._id" :currentFlowName="currentFlowName"></pre-task>
      </section>
    </t-form>
  </div>
</template>

<style lang="less" scoped>
// .file-content {
//   max-height: 200px;
//   overflow: auto;
// }
</style>
