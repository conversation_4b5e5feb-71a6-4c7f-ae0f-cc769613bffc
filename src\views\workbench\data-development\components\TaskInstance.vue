<template>
  <div class="page-development__task-instance">
    <div class="search-wrap">
      <BasicSearch v-model.trim="form.taskInstanceId" clearable placeholder="任务实例标识" @reset="handleReset" @change="searchChange">
        <template #suffix>
          <t-input v-model.trim="form.jobInstanceId" clearable placeholder="工作流实例标识" @change="searchChange" style="width: 209px;">
            <template #suffixIcon>
              <search-icon :style="{ cursor: 'pointer' }" />
            </template>
          </t-input>
          <t-input v-model.trim="form.searchValue" clearable placeholder="工作流标识/工作流名称/任务节点名称" @change="searchChange" style="width: 306px;">
            <template #suffixIcon>
              <search-icon :style="{ cursor: 'pointer' }" />
            </template>
          </t-input>
          <t-input v-model.trim="form.taskInstanceName" clearable placeholder="任务节点标识" @change="searchChange" style="width: 209px;">
            <template #suffixIcon>
              <search-icon :style="{ cursor: 'pointer' }" />
            </template>
          </t-input>
          <t-select class="des-common-select-placeholder" v-model="form.taskStates" :options="options" multiple clearable :minCollapsedNum="1" placeholder="全部状态" @change="taskStatesChange" style="width: 160px;"></t-select>
          <t-date-range-picker v-model="form.time" :presets="presets" allow-input clearable enable-time-picker :placeholder="['开始时间', '结束时间']" separator="至" @change="handleTimeChange" style="width: 388px;">
            <template #suffixIcon>
              <time-icon />
            </template>
          </t-date-range-picker>
        </template>
        <template #button-behind>
          <t-button variant="outline" theme="default" @click="fetchData">刷新</t-button>
        </template>
      </BasicSearch>
    </div>

    <!-- 数据表格 -->
    <t-table row-key="code" :data="tableData" :columns="columns"  :pagination="pagination" max-height="100%"/>

    <ViewLog ref="dialogRef" :getScheduleApi="getScheduleLog" :getExecuteApi="getExecuteLog"></ViewLog>
    <DownloadDialog ref="downloadDialogRef"></DownloadDialog>
  </div>
</template>

<script setup>
import { MessagePlugin } from 'tdesign-vue';
import { useRoute } from 'vue-router/composables';
import emitter, { EVENT_NAMES } from '@/utils/emitter';
import { TimeIcon, SearchIcon } from 'tdesign-icons-vue';
import { to, formatTime, secondConvertTime } from '@/utils/util';
import { useForm, useSearch, usePagination } from '@/hooks';
import { useDataDevelopmentStore } from '@/stores/data-development';
import { LabelWithDot, ViewLog } from '@/views/workbench/components';
import { ref, defineProps, watch, nextTick, reactive, onMounted, onBeforeUnmount } from 'vue';
import { viewFlow } from '../main-tabs/work-flow-panel/utils/flow';
import { getScheduleLog, getExecuteLog } from '../utils/task';
import { hasAuth } from '../utils/auth';
import DownloadDialog from './task-instance/DownloadDialog.vue';
import BasicSearch from '@/components/BasicSearch.vue';
import { without } from 'lodash';
import dayjs from 'dayjs';

const route = useRoute();

const props = defineProps({
  current: String,
});

const dataDevelopmentStore = useDataDevelopmentStore();
const { fetchTaskInstanceList, fetchTaskInstanceOperate } = dataDevelopmentStore;

const otherStates = [
  'unknown',
  'pause',
  'dispatch',
  'kill',
  'forced_success',
  'delay_execution',
  'need_fault_tolerance',
  'stop',
];
const fetchData = async () => {
  const { current: pageNum, pageSize } = pagination;
  const query = Object.entries(form).reduce((prev, item) => {
    const [key, value] = item;
    if (value && value?.length && value[0] !== '') {
      if (key === 'time') {
        const [startTime, endTime] = value;
        Object.assign(prev, { startTime, endTime });
      } else if (key === 'taskStates') {
        if (value.includes('other')) {
          Object.assign(prev, { taskStates: [...without(value, 'other'), ...otherStates] });
        } else {
          Object.assign(prev, { [key]: value });
        }
      } else {
        Object.assign(prev, { [key]: value });
      }
    }
    return prev;
  }, {});
  const queryData = {
    ...query,
    spaceId: route.query.spaceId,
  };
  const params = { pageNum, pageSize, queryData };
  const [err, data] = await to(fetchTaskInstanceList(params));
  if (err) {
    return;
  }
  const { total, list } = data;
  setPaginationTotal(total);
  tableData.value = list || [];
};

const { pagination, setPaginationCurrent, setPaginationSize, setPaginationTotal } = usePagination(fetchData);

const { onSearch } = useSearch(fetchData);

const searchChange = () => {
  setPaginationCurrent(1);
  onSearch();
};

const taskStatesChange = () => {
  setPaginationCurrent(1);
  fetchData();
};

const getRecentlyTime = (n) => {
  // 获取当前时间
  const now = dayjs();

  // 计算n天前的0点0分0秒
  const startDay = now.subtract(n, 'day').startOf('day');

  // 获取今天的23点59分59秒999毫秒
  const endDay = now.endOf('day');

  return [startDay.toDate(), endDay.toDate()];
};


// 表单数据
const initFormData = {
  taskInstanceId: '',
  jobInstanceId: '',
  searchValue: '',
  taskInstanceName: '',
  taskStates: [],
  time: getRecentlyTime(1).map(item => formatTime(item)),
};
const { form, resetData } = useForm(initFormData);

const handleReset = () => {
  setPaginationCurrent(1);
  resetData();
  fetchData();
};

const presets = reactive({
  最近7天: getRecentlyTime(6),
  最近3天: getRecentlyTime(2),
  今天: getRecentlyTime(0),
});


const handleTimeChange = () => {
  setPaginationCurrent(1);
  fetchData();
};

const handleView = (row) => {
  console.log('查看工作流', row);
  const data = {
    id: row.processId,
    name: row.processName,
    code: row.processCode,
  };
  viewFlow(data);
};

const tableData = ref([]);

const options = ref([
  { value: 'running', label: '运行中' },
  { value: 'submit', label: '提交' },
  { value: 'failed', label: '失败' },
  { value: 'success', label: '成功' },
  { value: 'other', label: '其它' },
]);
const getStatusLabel = status => ({
  unknown: '未知',
  running: '运行中',
  submit: '提交',
  failed: '失败',
  success: '成功',
  pause: '暂停',
  dispatch: '派送',
  kill: 'kill',
  forced_success: '强制成功',
  delay_execution: '推迟执行',
  need_fault_tolerance: '需要容错',
  stop: '停止',
}[status]);
const getStatusColor = status => ({
  unknown: undefined,
  running: '#3464E0',
  submit: '#50BA5E',
  failed: '#F81D22',
  success: '#50BA5E',
  pause: '#F81D22',
  dispatch: '#3464E0',
  kill: '#F81D22',
  forced_success: '#50BA5E',
  delay_execution: '#F81D22',
  need_fault_tolerance: '#3464E0',
  stop: '#F81D22',
}[status]);
const getTaskType = type => ({
  BIDSPLUS: '数据集成平台相关',
  SQL: 'sql',
  K8S: 'k8s',
  SUB_PROCESS: '子流程',
  HTTP: 'http',
  PYTHON: 'python',
  SWITCH: 'switch',
  DEPENDENT: 'dependent',
  CONDITIONS: 'conditions',
}[type]);
const columns = ref([
  { title: '序号', colKey: 'serial-number', width: 60, fixed: 'left', align: 'center' },
  { title: '任务实例标识', colKey: 'taskInstanceCode', width: 116 },
  { title: '工作流实例标识', colKey: 'processInstanceCode', width: 131, ellipsis: true },
  { title: '任务节点标识', colKey: 'taskNodeCode', width: 121 },
  { title: '任务节点名称', colKey: 'taskNodeName', width: 250 },
  { title: '工作流标识', colKey: 'processCode', width: 250 },
  { title: '工作流名称', colKey: 'processName', width: 250, cell: (h, { row }) => (<t-link hover="color" theme="primary" onClick={() => handleView(row)} disabled={!hasAuth(row, 'READ')}>{row.processName}</t-link>) },
  { title: '节点类型', colKey: 'taskType', width: 89, cell: (h, { row }) => getTaskType(row.taskType) },
  {
    title: '状态',
    colKey: 'taskState',
    width: 100,
    cell: (h, { row }) => (
      <LabelWithDot color={getStatusColor(row.taskState)} label={getStatusLabel(row.taskState)}></LabelWithDot>
    ),
  },
  { title: '提交时间', colKey: 'submitTime', width: 198, cell: (h, { row }) => formatTime(row.submitTime) },
  { title: '开始时间', colKey: 'startTime', width: 198, cell: (h, { row }) => formatTime(row.startTime) },
  { title: '结束时间', colKey: 'endTime', width: 198, cell: (h, { row }) => formatTime(row.endTime) },
  { title: '运行时长', colKey: 'costTime', width: 116, ellipsis: true, cell: (h, { row }) => secondConvertTime(row.costTime / 1000) },
  { title: '重跑次数', colKey: 'retryTimes', width: 116, ellipsis: true },
  { title: '操作', colKey: 'operation', width: 235, fixed: 'right',
    cell: (h, { row }) => (
      <t-space>
        <t-link theme="primary" hover="color" disabled={ !['failed', 'kill', 'stop'].includes(row.taskState) || !hasAuth(row, 'RUN') } onClick={ () => handleForce(row) }>
          强制成功
        </t-link>
        <t-link theme="primary" hover="color" onClick={ () => handleViewLog(row) }>
          查看日志
        </t-link>
        <t-link theme="primary" hover="color" onClick={() => handleDownLoad(row)}>
          下载日志
        </t-link>
      </t-space>
    ),
  },
]);

const dialogRef = ref(null);
const handleViewLog = (row) => {
  dialogRef.value.open(row);
};

const downloadDialogRef = ref(null);
const handleDownLoad = (row) => {
  downloadDialogRef.value.open(row);
};

const handleForce = async (row) => {
  const { taskInstanceCode, processCode } = row;
  const [err, data] = await to(fetchTaskInstanceOperate({ taskInstanceId: taskInstanceCode, spaceId: route.query.spaceId, jobCode: processCode }));
  if (err) {
    return;
  }
  if (data) {
    MessagePlugin('success', '操作成功');
    fetchData();
  }
};

watch(() => props.current, (val) => {
  if (val !== 'TaskInstance') return;
  nextTick(() => fetchData());
}, { immediate: true });

watch(() => route.query.spaceId, (val) => {
  if (!val) return;
  if (props.current !== 'TaskInstance') return;
  setPaginationCurrent(1);
  setPaginationSize(10);
  fetchData();
});

const setFormData = (data) => {
  Object.assign(form, data);
};

const handleRefreshTaskInstance = (data = {}) => {
  const [start, end] = getRecentlyTime(6);
  setFormData({ taskInstanceId: '', searchValue: '', taskInstanceName: '', taskStates: [], time: [formatTime(start), formatTime(end)], jobInstanceId: data.jobCode });
  fetchData();
};

onMounted(() => {
  emitter.on(EVENT_NAMES.DATA_DEVELOPMENT.REFRESH_TASK_INSTANCE, handleRefreshTaskInstance);
});
onBeforeUnmount(() => {
  emitter.off(EVENT_NAMES.DATA_DEVELOPMENT.REFRESH_TASK_INSTANCE, handleRefreshTaskInstance);
});
</script>

<style lang="less" scoped>
.page-development__task-instance {
  height: 100%;
  .search-wrap {
    padding: 16px 0;
    :deep(.basic-search-input) {
      width: 209px;
    }
  }
  :deep(.t-table) {
    height: calc(100% - 108px);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
}
</style>
