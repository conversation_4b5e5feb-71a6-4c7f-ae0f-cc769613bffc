<template>
  <div class="workbench-info__wrap">
    <section class="workbench-ifno__tab">
      <span
        v-for="tab in tabs"
        :key="tab.value"
        :class="{ actived: actived === tab.value }"
        @click="actived = tab.value"
      >
        {{ tab.label }}
      </span>
    </section>
    <section class="workbench-ifno__content">
      <components :is="componentName" :data="data"></components>
    </section>
  </div>
</template>

<script setup>
import { computed, ref, defineProps } from 'vue';
import { BasicInfo, StatisticsInfo, FieldInfo, AdvancedInfo } from './index';

defineProps({
  data: Object,
});

const tabs = [
  { label: '基础信息', value: 'basic' },
  { label: '统计信息', value: 'statistics' },
  { label: '字段信息', value: 'field' },
  { label: '高级信息', value: 'advanced' },
];

const actived = ref('basic');

const componentName = computed(() => ({
  basic: BasicInfo,
  statistics: StatisticsInfo,
  field: FieldInfo,
  advanced: AdvancedInfo,
}[actived.value]));
</script>

<style lang="less" scoped>
.workbench-info__wrap {
  height: 100%;
}
.workbench-ifno__tab {
  display: flex;
  width: 100%;
  height: 40px;
  padding: 8px 12px 0 12px;
  border-bottom: 1px solid #ebf0f7;
  & > span {
    position: relative;
    margin-right: 8px;
    text-align: center;
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: var(--des-font-normal);
    height: 32px;
    line-height: 32px;
    color: #666666;
    cursor: pointer;
    &:last-child {
      margin-right: 0;
    }
    &.actived {
      color: var(--des-color-theme);
      font-weight: var(--des-font-bold);
      &::after {
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        height: 2px;
        background-color: var(--des-color-theme);
        content: "";
      }
    }
    &[disabled] {
      cursor: not-allowed;
    }
  }
}
.workbench-ifno__content {
  height: calc(100% - 40px);
  padding: 12px;
}
</style>
