<template>
  <t-drawer :visible.sync="visible" header="调试记录" placement="bottom" :footer="false" :closeBtn="true" destroyOnClose showInAttachedElement :sizeDraggable="true">
    <div class="debug-record-wrap">
      <div class="aside">
        <p class="title">任务节点</p>
        <div class="list-wrap">
          <t-list :scroll="{ type: 'virtual' }">
            <t-list-item class="actived" @click="handleClick">列表内容的描述性文字</t-list-item>
          </t-list>
        </div>
      </div>
      <div class="main">
        <p class="title">详情</p>
        <div class="list-wrap">
          123...
        </div>
      </div>
    </div>
  </t-drawer>
</template>

<script setup>
import { ref, defineProps, defineExpose } from 'vue';

defineProps({
  currentNodeData: {
    type: Object,
    default: () => ({}),
  },
});

const visible = ref(false);

function open(node) {
  console.log('[ node ] >', node.data);
  visible.value = true;
}

function handleClick() {
}

defineExpose({
  open,
});
</script>

<style lang="less" scoped>
.debug-record-wrap {
  height: 100%;
  display: flex;
  .aside {
    width: 204px;
    border-right: 1px solid #e8e8e8;
    padding: 8px 16px 16px 16px;
    .list-wrap {
      height: calc(100% - 38px);
      overflow: auto;
      .t-list {
        height: 100%;
        .t-list-item {
          padding: 0;
          cursor: pointer;
          margin-bottom: 8px;
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
      .actived {
        color: var(--des-color-theme);
      }
    }
  }
  .main {
    padding: 8px 16px 16px 16px;
    .list-wrap {
      height: calc(100% - 38px);
      overflow: auto;
    }
  }
  .title {
    margin-bottom: 16px;
  }
}
:deep(.t-drawer__body) {
  padding: 0 !important;
}
</style>
