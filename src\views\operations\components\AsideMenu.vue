<template>
  <t-menu theme="light" width="210px" v-model="actived" @change="handleMenuChange">
    <t-menu-item value="resource" v-auth="'Menu_resourceManage'">
      <template #icon>
        <des-icon name="des-icon-shujumulu"></des-icon>
      </template>
      <span class="text">资源管理</span>
    </t-menu-item>
    <t-menu-item value="srdata" v-auth="'Menu_srDataOperation'">
      <template #icon>
        <DesIcon name="des-icon-StarRocksyunwei"></DesIcon>
      </template>
      <span class="text">SR数据运维</span>
    </t-menu-item>
    <!-- <t-menu-item value="sqlworkbench" v-auth="'Menu_sqlWorkbench'">
      <template #icon>
        <DesIcon name="des-icon-SQLgongzuosi"></DesIcon>
      </template>
      <span class="text">SQL工作台</span>
    </t-menu-item> -->
  </t-menu>
</template>

<script setup>
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';

const route = useRoute();
const router = useRouter();
const rootRouteName = route.path.split('/')[2];
const actived = ref(rootRouteName);

const handleMenuChange = (active) => {
  router.push({ name: active });
};
</script>

<style lang="less" scoped>
.t-menu {
  :deep(.t-menu__item) {
    height: 38px;
    &:nth-child(n + 2) {
      margin-top: 8px;
    }
  }
  .text {
    margin-left: 12px;
  }
}
</style>
